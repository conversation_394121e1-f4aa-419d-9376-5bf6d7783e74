{"abi": [{"type": "constructor", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "symbol", "type": "string", "internalType": "string"}, {"name": "mintAmount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "DEFAULT_ADMIN_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "DOMAIN_SEPARATOR", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "MINTER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "PAUSER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "SNAPSHOT_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "burn", "inputs": [{"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "burnFrom", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "eip712Domain", "inputs": [], "outputs": [{"name": "fields", "type": "bytes1", "internalType": "bytes1"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "version", "type": "string", "internalType": "string"}, {"name": "chainId", "type": "uint256", "internalType": "uint256"}, {"name": "verifyingContract", "type": "address", "internalType": "address"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "extensions", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "flashFee", "inputs": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "flashLoan", "inputs": [{"name": "receiver", "type": "address", "internalType": "contract IERC3156FlashBorrower"}, {"name": "token", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getRoleAdmin", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "grantRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "hasRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "maxFlash<PERSON><PERSON>", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "mint", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "nonces", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "pause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "paused", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "permit", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "callerConfirmation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "revokeRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "unpause", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "EIP712DomainChanged", "inputs": [], "anonymous": false}, {"type": "event", "name": "Paused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleAdminChanged", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "previousAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "newAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleGranted", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleRevoked", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Unpaused", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AccessControlBadConfirmation", "inputs": []}, {"type": "error", "name": "AccessControlUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "neededRole", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "ECDSAInvalidSignature", "inputs": []}, {"type": "error", "name": "ECDSAInvalidSignatureLength", "inputs": [{"name": "length", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ECDSAInvalidSignatureS", "inputs": [{"name": "s", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "ERC20InsufficientAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSpender", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC2612ExpiredSignature", "inputs": [{"name": "deadline", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC2612InvalidSigner", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC3156ExceededMaxLoan", "inputs": [{"name": "max<PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC3156InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC3156UnsupportedToken", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "EnforcedPause", "inputs": []}, {"type": "error", "name": "ExpectedPause", "inputs": []}, {"type": "error", "name": "InvalidAccountNonce", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "currentNonce", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidShortString", "inputs": []}, {"type": "error", "name": "StringTooLong", "inputs": [{"name": "str", "type": "string", "internalType": "string"}]}], "bytecode": {"object": "0x610160604052348015610010575f5ffd5b5060405161201638038061201683398101604081905261002f916104e3565b6040805180820190915260018152603160f81b6020820152839081908185600361005983826105d3565b50600461006682826105d3565b50610076915083905060076101b9565b610120526100858160086101b9565b61014052815160208084019190912060e052815190820120610100524660a05261011160e05161010051604080517f8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f60208201529081019290925260608201524660808201523060a08201525f9060c00160405160208183030381529060405280519060200120905090565b60805250503060c052506101255f336101eb565b506101507f5fdbd35e8da83ee755d5e62a539e5ed7f47126abede0b8b10f9ea43dc6eed07f336101eb565b5061017b7f65d7a28e3265b37a6474929f336521b332c1681b933f6cb9f3376673440d862a336101eb565b506101a67f9f2df0fed2c77648de5860a4cc508cd0818c85b8b8a1ab4ceeef8d981c8956a6336101eb565b506101b13382610296565b505050610704565b5f6020835110156101d4576101cd836102d3565b90506101e5565b816101df84826105d3565b5060ff90505b92915050565b5f8281526005602090815260408083206001600160a01b038516845290915281205460ff1661028f575f8381526005602090815260408083206001600160a01b03861684529091529020805460ff191660011790556102473390565b6001600160a01b0316826001600160a01b0316847f2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d60405160405180910390a45060016101e5565b505f6101e5565b6001600160a01b0382166102c45760405163ec442f0560e01b81525f60048201526024015b60405180910390fd5b6102cf5f8383610310565b5050565b5f5f829050601f815111156102fd578260405163305a27a960e01b81526004016102bb919061068d565b8051610308826106c2565b179392505050565b61031b838383610320565b505050565b6001600160a01b03831661034a578060025f82825461033f91906106e5565b909155506103ba9050565b6001600160a01b0383165f908152602081905260409020548181101561039c5760405163391434e360e21b81526001600160a01b038516600482015260248101829052604481018390526064016102bb565b6001600160a01b0384165f9081526020819052604090209082900390555b6001600160a01b0382166103d6576002805482900390556103f4565b6001600160a01b0382165f9081526020819052604090208054820190555b816001600160a01b0316836001600160a01b03167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef8360405161043991815260200190565b60405180910390a3505050565b634e487b7160e01b5f52604160045260245ffd5b5f82601f830112610469575f5ffd5b81516001600160401b0381111561048257610482610446565b604051601f8201601f19908116603f011681016001600160401b03811182821017156104b0576104b0610446565b6040528181528382016020018510156104c7575f5ffd5b8160208501602083015e5f918101602001919091529392505050565b5f5f5f606084860312156104f5575f5ffd5b83516001600160401b0381111561050a575f5ffd5b6105168682870161045a565b602086015190945090506001600160401b03811115610533575f5ffd5b61053f8682870161045a565b925050604084015190509250925092565b600181811c9082168061056457607f821691505b60208210810361058257634e487b7160e01b5f52602260045260245ffd5b50919050565b601f82111561031b57805f5260205f20601f840160051c810160208510156105ad5750805b601f840160051c820191505b818110156105cc575f81556001016105b9565b5050505050565b81516001600160401b038111156105ec576105ec610446565b610600816105fa8454610550565b84610588565b6020601f821160018114610632575f831561061b5750848201515b5f19600385901b1c1916600184901b1784556105cc565b5f84815260208120601f198516915b828110156106615787850151825560209485019460019092019101610641565b508482101561067e57868401515f19600387901b60f8161c191681555b50505050600190811b01905550565b602081525f82518060208401528060208501604085015e5f604082850101526040601f19601f83011684010191505092915050565b80516020808301519190811015610582575f1960209190910360031b1b16919050565b808201808211156101e557634e487b7160e01b5f52601160045260245ffd5b60805160a05160c05160e0516101005161012051610140516118c16107555f395f610ef701525f610eca01525f610cf401525f610ccc01525f610c2701525f610c5101525f610c7b01526118c15ff3fe608060405234801561000f575f5ffd5b50600436106101e7575f3560e01c80637028e2cd11610109578063a217fddf1161009e578063d547741f1161006e578063d547741f14610427578063d9d98ce41461043a578063dd62ed3e1461044d578063e63ab1e914610485575f5ffd5b8063a217fddf146103d3578063a9059cbb146103da578063d505accf146103ed578063d539139314610400575f5ffd5b80638456cb59116100d95780638456cb591461039557806384b0196e1461039d57806391d14854146103b857806395d89b41146103cb575f5ffd5b80637028e2cd1461032057806370a082311461034757806379cc67901461036f5780637ecebe0014610382575f5ffd5b80633644e5151161017f57806342966c681161014f57806342966c68146102dc5780635c975abb146102ef5780635cffe9de146102fa578063613255ab1461030d575f5ffd5b80633644e515146102a657806336568abe146102ae5780633f4ba83a146102c157806340c10f19146102c9575f5ffd5b806323b872dd116101ba57806323b872dd1461024d578063248a9ca3146102605780632f2ff15d14610282578063313ce56714610297575f5ffd5b806301ffc9a7146101eb57806306fdde0314610213578063095ea7b31461022857806318160ddd1461023b575b5f5ffd5b6101fe6101f9366004611487565b6104ac565b60405190151581526020015b60405180910390f35b61021b6104e2565b60405161020a91906114dc565b6101fe610236366004611502565b610572565b6002545b60405190815260200161020a565b6101fe61025b36600461152c565b610589565b61023f61026e36600461156a565b5f9081526005602052604090206001015490565b610295610290366004611581565b6105ac565b005b6040516012815260200161020a565b61023f6105d6565b6102956102bc366004611581565b6105e4565b61029561061c565b6102956102d7366004611502565b610651565b6102956102ea36600461156a565b610685565b60065460ff166101fe565b6101fe6103083660046115af565b61068f565b61023f61031b366004611648565b610806565b61023f7f5fdbd35e8da83ee755d5e62a539e5ed7f47126abede0b8b10f9ea43dc6eed07f81565b61023f610355366004611648565b6001600160a01b03165f9081526020819052604090205490565b61029561037d366004611502565b61082b565b61023f610390366004611648565b610844565b61029561084e565b6103a5610880565b60405161020a9796959493929190611663565b6101fe6103c6366004611581565b6108c2565b61021b6108ec565b61023f5f81565b6101fe6103e8366004611502565b6108fb565b6102956103fb3660046116f9565b610908565b61023f7f9f2df0fed2c77648de5860a4cc508cd0818c85b8b8a1ab4ceeef8d981c8956a681565b610295610435366004611581565b610a3e565b61023f610448366004611502565b610a62565b61023f61045b36600461176a565b6001600160a01b039182165f90815260016020908152604080832093909416825291909152205490565b61023f7f65d7a28e3265b37a6474929f336521b332c1681b933f6cb9f3376673440d862a81565b5f6001600160e01b03198216637965db0b60e01b14806104dc57506301ffc9a760e01b6001600160e01b03198316145b92915050565b6060600380546104f190611796565b80601f016020809104026020016040519081016040528092919081815260200182805461051d90611796565b80156105685780601f1061053f57610100808354040283529160200191610568565b820191905f5260205f20905b81548152906001019060200180831161054b57829003601f168201915b5050505050905090565b5f3361057f818585610aa0565b5060019392505050565b5f33610596858285610aad565b6105a1858585610b23565b506001949350505050565b5f828152600560205260409020600101546105c681610b80565b6105d08383610b8a565b50505050565b5f6105df610c1b565b905090565b6001600160a01b038116331461060d5760405163334bd91960e11b815260040160405180910390fd5b6106178282610d44565b505050565b7f65d7a28e3265b37a6474929f336521b332c1681b933f6cb9f3376673440d862a61064681610b80565b61064e610daf565b50565b7f9f2df0fed2c77648de5860a4cc508cd0818c85b8b8a1ab4ceeef8d981c8956a661067b81610b80565b6106178383610e01565b61064e3382610e35565b5f5f61069a86610806565b9050808511156106c55760405163fd9a760960e01b8152600481018290526024015b60405180910390fd5b5f6106d08787610a62565b90506106dc8887610e01565b6040516323e30c8b60e01b81527f439148f0bbc682ca079e46d6e2c2f0c1e3b820f1a291b069d8882abf8cf18dd9906001600160a01b038a16906323e30c8b906107349033908c908c9088908d908d906004016117ce565b6020604051808303815f875af1158015610750573d5f5f3e3d5ffd5b505050506040513d601f19601f820116820180604052508101906107749190611826565b1461079c5760405162678c5b60e81b81526001600160a01b03891660048201526024016106bc565b5f6107b189306107ac858b611851565b610aad565b8115806107c557506001600160a01b038116155b156107e2576107dd896107d8848a611851565b610e35565b6107f7565b6107ec8988610e35565b6107f7898284610b23565b50600198975050505050505050565b5f6001600160a01b038216301461081d575f6104dc565b6002546104dc905f19611864565b610836823383610aad565b6108408282610e35565b5050565b5f6104dc82610e69565b7f65d7a28e3265b37a6474929f336521b332c1681b933f6cb9f3376673440d862a61087881610b80565b61064e610e86565b5f6060805f5f5f6060610891610ec3565b610899610ef0565b604080515f80825260208201909252600f60f81b9b939a50919850469750309650945092509050565b5f9182526005602090815260408084206001600160a01b0393909316845291905290205460ff1690565b6060600480546104f190611796565b5f3361057f818585610b23565b8342111561092c5760405163313c898160e11b8152600481018590526024016106bc565b5f7f6e71edae12b1b97f4d1f60370fef10105fa2faae0126114a169c64845d6126c98888886109778c6001600160a01b03165f90815260096020526040902080546001810190915590565b6040805160208101969096526001600160a01b0394851690860152929091166060840152608083015260a082015260c0810186905260e0016040516020818303038152906040528051906020012090505f6109d182610f1d565b90505f6109e082878787610f49565b9050896001600160a01b0316816001600160a01b031614610a27576040516325c0072360e11b81526001600160a01b0380831660048301528b1660248201526044016106bc565b610a328a8a8a610aa0565b50505050505050505050565b5f82815260056020526040902060010154610a5881610b80565b6105d08383610d44565b5f6001600160a01b0383163014610a9757604051635ad3edc960e11b81526001600160a01b03841660048201526024016106bc565b5f5b9392505050565b6106178383836001610f75565b6001600160a01b038381165f908152600160209081526040808320938616835292905220545f198110156105d05781811015610b1557604051637dc7a0d960e11b81526001600160a01b038416600482015260248101829052604481018390526064016106bc565b6105d084848484035f610f75565b6001600160a01b038316610b4c57604051634b637e8f60e11b81525f60048201526024016106bc565b6001600160a01b038216610b755760405163ec442f0560e01b81525f60048201526024016106bc565b610617838383611047565b61064e8133611052565b5f610b9583836108c2565b610c14575f8381526005602090815260408083206001600160a01b03861684529091529020805460ff19166001179055610bcc3390565b6001600160a01b0316826001600160a01b0316847f2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d60405160405180910390a45060016104dc565b505f6104dc565b5f306001600160a01b037f000000000000000000000000000000000000000000000000000000000000000016148015610c7357507f000000000000000000000000000000000000000000000000000000000000000046145b15610c9d57507f000000000000000000000000000000000000000000000000000000000000000090565b6105df604080517f8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f60208201527f0000000000000000000000000000000000000000000000000000000000000000918101919091527f000000000000000000000000000000000000000000000000000000000000000060608201524660808201523060a08201525f9060c00160405160208183030381529060405280519060200120905090565b5f610d4f83836108c2565b15610c14575f8381526005602090815260408083206001600160a01b0386168085529252808320805460ff1916905551339286917ff6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b9190a45060016104dc565b610db761108b565b6006805460ff191690557f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa335b6040516001600160a01b03909116815260200160405180910390a1565b6001600160a01b038216610e2a5760405163ec442f0560e01b81525f60048201526024016106bc565b6108405f8383611047565b6001600160a01b038216610e5e57604051634b637e8f60e11b81525f60048201526024016106bc565b610840825f83611047565b6001600160a01b0381165f908152600960205260408120546104dc565b610e8e6110b0565b6006805460ff191660011790557f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a258610de43390565b60606105df7f000000000000000000000000000000000000000000000000000000000000000060076110d4565b60606105df7f000000000000000000000000000000000000000000000000000000000000000060086110d4565b5f6104dc610f29610c1b565b8360405161190160f01b8152600281019290925260228201526042902090565b5f5f5f5f610f598888888861117d565b925092509250610f698282611245565b50909695505050505050565b6001600160a01b038416610f9e5760405163e602df0560e01b81525f60048201526024016106bc565b6001600160a01b038316610fc757604051634a1406b160e11b81525f60048201526024016106bc565b6001600160a01b038085165f90815260016020908152604080832093871683529290522082905580156105d057826001600160a01b0316846001600160a01b03167f8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b9258460405161103991815260200190565b60405180910390a350505050565b6106178383836112fd565b61105c82826108c2565b6108405760405163e2517d3f60e01b81526001600160a01b0382166004820152602481018390526044016106bc565b60065460ff166110ae57604051638dfc202b60e01b815260040160405180910390fd5b565b60065460ff16156110ae5760405163d93c066560e01b815260040160405180910390fd5b606060ff83146110ee576110e783611423565b90506104dc565b8180546110fa90611796565b80601f016020809104026020016040519081016040528092919081815260200182805461112690611796565b80156111715780601f1061114857610100808354040283529160200191611171565b820191905f5260205f20905b81548152906001019060200180831161115457829003601f168201915b505050505090506104dc565b5f80807f7fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a08411156111b657505f9150600390508261123b565b604080515f808252602082018084528a905260ff891692820192909252606081018790526080810186905260019060a0016020604051602081039080840390855afa158015611207573d5f5f3e3d5ffd5b5050604051601f1901519150506001600160a01b03811661123257505f92506001915082905061123b565b92505f91508190505b9450945094915050565b5f82600381111561125857611258611877565b03611261575050565b600182600381111561127557611275611877565b036112935760405163f645eedf60e01b815260040160405180910390fd5b60028260038111156112a7576112a7611877565b036112c85760405163fce698f760e01b8152600481018290526024016106bc565b60038260038111156112dc576112dc611877565b03610840576040516335e2f38360e21b8152600481018290526024016106bc565b6001600160a01b038316611327578060025f82825461131c9190611851565b909155506113979050565b6001600160a01b0383165f90815260208190526040902054818110156113795760405163391434e360e21b81526001600160a01b038516600482015260248101829052604481018390526064016106bc565b6001600160a01b0384165f9081526020819052604090209082900390555b6001600160a01b0382166113b3576002805482900390556113d1565b6001600160a01b0382165f9081526020819052604090208054820190555b816001600160a01b0316836001600160a01b03167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef8360405161141691815260200190565b60405180910390a3505050565b60605f61142f83611460565b6040805160208082528183019092529192505f91906020820181803683375050509182525060208101929092525090565b5f60ff8216601f8111156104dc57604051632cd44ac360e21b815260040160405180910390fd5b5f60208284031215611497575f5ffd5b81356001600160e01b031981168114610a99575f5ffd5b5f81518084528060208401602086015e5f602082860101526020601f19601f83011685010191505092915050565b602081525f610a9960208301846114ae565b6001600160a01b038116811461064e575f5ffd5b5f5f60408385031215611513575f5ffd5b823561151e816114ee565b946020939093013593505050565b5f5f5f6060848603121561153e575f5ffd5b8335611549816114ee565b92506020840135611559816114ee565b929592945050506040919091013590565b5f6020828403121561157a575f5ffd5b5035919050565b5f5f60408385031215611592575f5ffd5b8235915060208301356115a4816114ee565b809150509250929050565b5f5f5f5f5f608086880312156115c3575f5ffd5b85356115ce816114ee565b945060208601356115de816114ee565b935060408601359250606086013567ffffffffffffffff811115611600575f5ffd5b8601601f81018813611610575f5ffd5b803567ffffffffffffffff811115611626575f5ffd5b886020828401011115611637575f5ffd5b959894975092955050506020019190565b5f60208284031215611658575f5ffd5b8135610a99816114ee565b60ff60f81b8816815260e060208201525f61168160e08301896114ae565b828103604084015261169381896114ae565b606084018890526001600160a01b038716608085015260a0840186905283810360c0850152845180825260208087019350909101905f5b818110156116e85783518352602093840193909201916001016116ca565b50909b9a5050505050505050505050565b5f5f5f5f5f5f5f60e0888a03121561170f575f5ffd5b873561171a816114ee565b9650602088013561172a816114ee565b95506040880135945060608801359350608088013560ff8116811461174d575f5ffd5b9699959850939692959460a0840135945060c09093013592915050565b5f5f6040838503121561177b575f5ffd5b8235611786816114ee565b915060208301356115a4816114ee565b600181811c908216806117aa57607f821691505b6020821081036117c857634e487b7160e01b5f52602260045260245ffd5b50919050565b6001600160a01b03878116825286166020820152604081018590526060810184905260a0608082018190528101829052818360c08301375f81830160c090810191909152601f909201601f1916010195945050505050565b5f60208284031215611836575f5ffd5b5051919050565b634e487b7160e01b5f52601160045260245ffd5b808201808211156104dc576104dc61183d565b818103818111156104dc576104dc61183d565b634e487b7160e01b5f52602160045260245ffdfea2646970667358221220e4fcf342327d5649a19ba0391c23cbc53cbe1156924688db98fc170f7265888864736f6c634300081c0033", "sourceMap": "718:1214:177:-:0;;;1030:347;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3428:431:85;;;;;;;;;;;;-1:-1:-1;;;3428:431:85;;;;1136:4:177;;;;;1116:6;1648:5:67;:13;1136:4:177;1648:5:67;:13;:::i;:::-;-1:-1:-1;1671:7:67;:17;1681:7;1671;:17;:::i;:::-;-1:-1:-1;3501:45:85;;-1:-1:-1;3501:4:85;;-1:-1:-1;3532:13:85;3501:30;:45::i;:::-;3493:53;;3567:51;:7;3601:16;3567:33;:51::i;:::-;3556:62;;3642:22;;;;;;;;;;3628:36;;3691:25;;;;;;3674:42;;3744:13;3727:30;;3792:23;4326:11;;4339:14;;4304:80;;;2079:95;4304:80;;;4265:25:188;4306:18;;;4299:34;;;;4349:18;;;4342:34;4355:13:85;4392:18:188;;;4385:34;4378:4:85;4435:19:188;;;4428:61;4268:7:85;;4237:19:188;;4304:80:85;;;;;;;;;;;;4294:91;;;;;;4287:98;;4213:179;;3792:23;3767:48;;-1:-1:-1;;3847:4:85;3825:27;;-1:-1:-1;1152:42:177::2;2241:4:51;1183:10:177;1152;:42::i;:::-;-1:-1:-1::0;1204:37:177::2;861:26;1230:10;1204;:37::i;:::-;-1:-1:-1::0;1251:35:177::2;931:24;1275:10;1251;:35::i;:::-;-1:-1:-1::0;1296:35:177::2;999:24;1320:10;1296;:35::i;:::-;-1:-1:-1::0;1341:29:177::2;1347:10;1359::::0;1341:5:::2;:29::i;:::-;1030:347:::0;;;718:1214;;2887:340:81;2983:11;3032:2;3016:5;3010:19;:24;3006:215;;;3057:20;3071:5;3057:13;:20::i;:::-;3050:27;;;;3006:215;3134:5;3108:46;3149:5;3134;3108:46;:::i;:::-;-1:-1:-1;1390:66:81;;-1:-1:-1;3006:215:81;2887:340;;;;:::o;6155:316:51:-;6232:4;2930:12;;;:6;:12;;;;;;;;-1:-1:-1;;;;;2930:29:51;;;;;;;;;;;;6248:217;;6291:12;;;;:6;:12;;;;;;;;-1:-1:-1;;;;;6291:29:51;;;;;;;;;:36;;-1:-1:-1;;6291:36:51;6323:4;6291:36;;;6373:12;735:10:77;;656:96;6373:12:51;-1:-1:-1;;;;;6346:40:51;6364:7;-1:-1:-1;;;;;6346:40:51;6358:4;6346:40;;;;;;;;;;-1:-1:-1;6407:4:51;6400:11;;6248:217;-1:-1:-1;6449:5:51;6442:12;;7362:208:67;-1:-1:-1;;;;;7432:21:67;;7428:91;;7476:32;;-1:-1:-1;;;7476:32:67;;7505:1;7476:32;;;4646:51:188;4619:18;;7476:32:67;;;;;;;;7428:91;7528:35;7544:1;7548:7;7557:5;7528:7;:35::i;:::-;7362:208;;:::o;1708:286:81:-;1773:11;1796:17;1822:3;1796:30;;1854:2;1840:4;:11;:16;1836:72;;;1893:3;1879:18;;-1:-1:-1;;;1879:18:81;;;;;;;;:::i;1836:72::-;1974:11;;1957:13;1974:4;1957:13;:::i;:::-;1949:36;;1708:286;-1:-1:-1;;;1708:286:81:o;1652:132:177:-;1746:31;1760:4;1766:2;1770:6;1746:13;:31::i;:::-;1652:132;;;:::o;5912:1107:67:-;-1:-1:-1;;;;;6001:18:67;;5997:540;;6153:5;6137:12;;:21;;;;;;;:::i;:::-;;;;-1:-1:-1;5997:540:67;;-1:-1:-1;5997:540:67;;-1:-1:-1;;;;;6211:15:67;;6189:19;6211:15;;;;;;;;;;;6244:19;;;6240:115;;;6290:50;;-1:-1:-1;;;6290:50:67;;-1:-1:-1;;;;;5880:32:188;;6290:50:67;;;5862:51:188;5929:18;;;5922:34;;;5972:18;;;5965:34;;;5835:18;;6290:50:67;5660:345:188;6240:115:67;-1:-1:-1;;;;;6475:15:67;;:9;:15;;;;;;;;;;6493:19;;;;6475:37;;5997:540;-1:-1:-1;;;;;6551:16:67;;6547:425;;6714:12;:21;;;;;;;6547:425;;;-1:-1:-1;;;;;6925:13:67;;:9;:13;;;;;;;;;;:22;;;;;;6547:425;7002:2;-1:-1:-1;;;;;6987:25:67;6996:4;-1:-1:-1;;;;;6987:25:67;;7006:5;6987:25;;;;6156::188;;6144:2;6129:18;;6010:177;6987:25:67;;;;;;;;5912:1107;;;:::o;14:127:188:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:723;200:5;253:3;246:4;238:6;234:17;230:27;220:55;;271:1;268;261:12;220:55;298:13;;-1:-1:-1;;;;;323:30:188;;320:56;;;356:18;;:::i;:::-;405:2;399:9;497:2;459:17;;-1:-1:-1;;455:31:188;;;488:2;451:40;447:54;435:67;;-1:-1:-1;;;;;517:34:188;;553:22;;;514:62;511:88;;;579:18;;:::i;:::-;615:2;608:22;639;;;680:19;;;701:4;676:30;673:39;-1:-1:-1;670:59:188;;;725:1;722;715:12;670:59;782:6;775:4;767:6;763:17;756:4;748:6;744:17;738:51;837:1;809:19;;;830:4;805:30;798:41;;;;813:6;146:723;-1:-1:-1;;;146:723:188:o;874:618::-;982:6;990;998;1051:2;1039:9;1030:7;1026:23;1022:32;1019:52;;;1067:1;1064;1057:12;1019:52;1094:16;;-1:-1:-1;;;;;1122:30:188;;1119:50;;;1165:1;1162;1155:12;1119:50;1188:61;1241:7;1232:6;1221:9;1217:22;1188:61;:::i;:::-;1295:2;1280:18;;1274:25;1178:71;;-1:-1:-1;1274:25:188;-1:-1:-1;;;;;;1311:32:188;;1308:52;;;1356:1;1353;1346:12;1308:52;1379:63;1434:7;1423:8;1412:9;1408:24;1379:63;:::i;:::-;1369:73;;;1482:2;1471:9;1467:18;1461:25;1451:35;;874:618;;;;;:::o;1497:380::-;1576:1;1572:12;;;;1619;;;1640:61;;1694:4;1686:6;1682:17;1672:27;;1640:61;1747:2;1739:6;1736:14;1716:18;1713:38;1710:161;;1793:10;1788:3;1784:20;1781:1;1774:31;1828:4;1825:1;1818:15;1856:4;1853:1;1846:15;1710:161;;1497:380;;;:::o;2008:518::-;2110:2;2105:3;2102:11;2099:421;;;2146:5;2143:1;2136:16;2190:4;2187:1;2177:18;2260:2;2248:10;2244:19;2241:1;2237:27;2231:4;2227:38;2296:4;2284:10;2281:20;2278:47;;;-1:-1:-1;2319:4:188;2278:47;2374:2;2369:3;2365:12;2362:1;2358:20;2352:4;2348:31;2338:41;;2429:81;2447:2;2440:5;2437:13;2429:81;;;2506:1;2492:16;;2473:1;2462:13;2429:81;;;2433:3;;2008:518;;;:::o;2702:1299::-;2822:10;;-1:-1:-1;;;;;2844:30:188;;2841:56;;;2877:18;;:::i;:::-;2906:97;2996:6;2956:38;2988:4;2982:11;2956:38;:::i;:::-;2950:4;2906:97;:::i;:::-;3052:4;3083:2;3072:14;;3100:1;3095:649;;;;3788:1;3805:6;3802:89;;;-1:-1:-1;3857:19:188;;;3851:26;3802:89;-1:-1:-1;;2659:1:188;2655:11;;;2651:24;2647:29;2637:40;2683:1;2679:11;;;2634:57;3904:81;;3065:930;;3095:649;1955:1;1948:14;;;1992:4;1979:18;;-1:-1:-1;;3131:20:188;;;3249:222;3263:7;3260:1;3257:14;3249:222;;;3345:19;;;3339:26;3324:42;;3452:4;3437:20;;;;3405:1;3393:14;;;;3279:12;3249:222;;;3253:3;3499:6;3490:7;3487:19;3484:201;;;3560:19;;;3554:26;-1:-1:-1;;3643:1:188;3639:14;;;3655:3;3635:24;3631:37;3627:42;3612:58;3597:74;;3484:201;-1:-1:-1;;;;3731:1:188;3715:14;;;3711:22;3698:36;;-1:-1:-1;2702:1299:188:o;4708:418::-;4857:2;4846:9;4839:21;4820:4;4889:6;4883:13;4932:6;4927:2;4916:9;4912:18;4905:34;4991:6;4986:2;4978:6;4974:15;4969:2;4958:9;4954:18;4948:50;5047:1;5042:2;5033:6;5022:9;5018:22;5014:31;5007:42;5117:2;5110;5106:7;5101:2;5093:6;5089:15;5085:29;5074:9;5070:45;5066:54;5058:62;;;4708:418;;;;:::o;5131:297::-;5249:12;;5296:4;5285:16;;;5279:23;;5249:12;5314:16;;5311:111;;;-1:-1:-1;;5388:4:188;5384:17;;;;5381:1;5377:25;5373:38;5362:50;;5131:297;-1:-1:-1;5131:297:188:o;5433:222::-;5498:9;;;5519:10;;;5516:133;;;5571:10;5566:3;5562:20;5559:1;5552:31;5606:4;5603:1;5596:15;5634:4;5631:1;5624:15;6010:177;718:1214:177;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "718:1214:177:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2541:202:51;;;;;;:::i;:::-;;:::i;:::-;;;470:14:188;;463:22;445:41;;433:2;418:18;2541:202:51;;;;;;;;1760:89:67;;;:::i;:::-;;;;;;;:::i;3902:186::-;;;;;;:::i;:::-;;:::i;2803:97::-;2881:12;;2803:97;;;1670:25:188;;;1658:2;1643:18;2803:97:67;1524:177:188;4680:244:67;;;;;;:::i;:::-;;:::i;3786:120:51:-;;;;;;:::i;:::-;3851:7;3877:12;;;:6;:12;;;;;:22;;;;3786:120;4202:136;;;;;;:::i;:::-;;:::i;:::-;;2688:82:67;;;2761:2;3146:36:188;;3134:2;3119:18;2688:82:67;3004:184:188;2614:112:71;;;:::i;5304:245:51:-;;;;;;:::i;:::-;;:::i;1460:75:177:-;;;:::i;1541:105::-;;;;;;:::i;:::-;;:::i;618:87:69:-;;;;;;:::i;:::-;;:::i;1726:84:80:-;1796:7;;;;1726:84;;4807:962:70;;;;;;:::i;:::-;;:::i;1957:161::-;;;;;;:::i;:::-;;:::i;821:66:177:-;;861:26;821:66;;2933:116:67;;;;;;:::i;:::-;-1:-1:-1;;;;;3024:18:67;2998:7;3024:18;;;;;;;;;;;;2933:116;1021:158:69;;;;;;:::i;:::-;;:::i;1790:140:177:-;;;;;;:::i;:::-;;:::i;1383:71::-;;;:::i;5228:557:85:-;;;:::i;:::-;;;;;;;;;;;;;:::i;2830:136:51:-;;;;;;:::i;:::-;;:::i;1962:93:67:-;;;:::i;2196:49:51:-;;2241:4;2196:49;;3244:178:67;;;;;;:::i;:::-;;:::i;1668:672:71:-;;;;;;:::i;:::-;;:::i;961:62:177:-;;999:24;961:62;;4618:138:51;;;;;;:::i;:::-;;:::i;2483:232:70:-;;;;;;:::i;:::-;;:::i;3455:140:67:-;;;;;;:::i;:::-;-1:-1:-1;;;;;3561:18:67;;;3535:7;3561:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;;;;3455:140;893:62:177;;931:24;893:62;;2541:202:51;2626:4;-1:-1:-1;;;;;;2649:47:51;;-1:-1:-1;;;2649:47:51;;:87;;-1:-1:-1;;;;;;;;;;829:40:87;;;2700:36:51;2642:94;2541:202;-1:-1:-1;;2541:202:51:o;1760:89:67:-;1805:13;1837:5;1830:12;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1760:89;:::o;3902:186::-;3975:4;735:10:77;4029:31:67;735:10:77;4045:7:67;4054:5;4029:8;:31::i;:::-;-1:-1:-1;4077:4:67;;3902:186;-1:-1:-1;;;3902:186:67:o;4680:244::-;4767:4;735:10:77;4823:37:67;4839:4;735:10:77;4854:5:67;4823:15;:37::i;:::-;4870:26;4880:4;4886:2;4890:5;4870:9;:26::i;:::-;-1:-1:-1;4913:4:67;;4680:244;-1:-1:-1;;;;4680:244:67:o;4202:136:51:-;3851:7;3877:12;;;:6;:12;;;;;:22;;;2473:16;2484:4;2473:10;:16::i;:::-;4306:25:::1;4317:4;4323:7;4306:10;:25::i;:::-;;4202:136:::0;;;:::o;2614:112:71:-;2673:7;2699:20;:18;:20::i;:::-;2692:27;;2614:112;:::o;5304:245:51:-;-1:-1:-1;;;;;5397:34:51;;735:10:77;5397:34:51;5393:102;;5454:30;;-1:-1:-1;;;5454:30:51;;;;;;;;;;;5393:102;5505:37;5517:4;5523:18;5505:11;:37::i;:::-;;5304:245;;:::o;1460:75:177:-;931:24;2473:16:51;2484:4;2473:10;:16::i;:::-;1518:10:177::1;:8;:10::i;:::-;1460:75:::0;:::o;1541:105::-;999:24;2473:16:51;2484:4;2473:10;:16::i;:::-;1622:17:177::1;1628:2;1632:6;1622:5;:17::i;618:87:69:-:0;672:26;735:10:77;692:5:69;672;:26::i;4807:962:70:-;4971:4;4987:15;5005:19;5018:5;5005:12;:19::i;:::-;4987:37;;5046:7;5038:5;:15;5034:84;;;5076:31;;-1:-1:-1;;;5076:31:70;;;;;1670:25:188;;;1643:18;;5076:31:70;;;;;;;;5034:84;5127:11;5141:22;5150:5;5157;5141:8;:22::i;:::-;5127:36;;5173:31;5187:8;5198:5;5173;:31::i;:::-;5218:59;;-1:-1:-1;;;5218:59:70;;1039:45;;-1:-1:-1;;;;;5218:20:70;;;;;:59;;735:10:77;;5253:5:70;;5260;;5267:3;;5272:4;;;;5218:59;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:75;5214:154;;5316:41;;-1:-1:-1;;;5316:41:70;;-1:-1:-1;;;;;8846:32:188;;5316:41:70;;;8828:51:188;8801:18;;5316:41:70;8682:203:188;5214:154:70;5377:24;5433:62;5457:8;5476:4;5483:11;5491:3;5483:5;:11;:::i;:::-;5433:15;:62::i;:::-;5509:8;;;:42;;-1:-1:-1;;;;;;5521:30:70;;;5509:42;5505:237;;;5567:37;5581:8;5592:11;5600:3;5592:5;:11;:::i;:::-;5567:5;:37::i;:::-;5505:237;;;5635:31;5649:8;5660:5;5635;:31::i;:::-;5680:51;5698:8;5709:16;5727:3;5680:9;:51::i;:::-;-1:-1:-1;5758:4:70;;4807:962;-1:-1:-1;;;;;;;;4807:962:70:o;1957:161::-;2023:7;-1:-1:-1;;;;;2049:22:70;;2066:4;2049:22;:62;;2110:1;2049:62;;;2881:12:67;;2074:33:70;;-1:-1:-1;;2074:33:70;:::i;1021:158:69:-;1096:45;1112:7;735:10:77;1135:5:69;1096:15;:45::i;:::-;1151:21;1157:7;1166:5;1151;:21::i;:::-;1021:158;;:::o;1790:140:177:-;1878:7;1904:19;1917:5;1904:12;:19::i;1383:71::-;931:24;2473:16:51;2484:4;2473:10;:16::i;:::-;1439:8:177::1;:6;:8::i;5228:557:85:-:0;5326:13;5353:18;5385:21;5420:15;5449:25;5488:12;5514:27;5617:13;:11;:13::i;:::-;5644:16;:14;:16::i;:::-;5752;;;5736:1;5752:16;;;;;;;;;-1:-1:-1;;;5566:212:85;;;-1:-1:-1;5566:212:85;;-1:-1:-1;5674:13:85;;-1:-1:-1;5709:4:85;;-1:-1:-1;5736:1:85;-1:-1:-1;5752:16:85;-1:-1:-1;5566:212:85;-1:-1:-1;5228:557:85:o;2830:136:51:-;2907:4;2930:12;;;:6;:12;;;;;;;;-1:-1:-1;;;;;2930:29:51;;;;;;;;;;;;;;;2830:136::o;1962:93:67:-;2009:13;2041:7;2034:14;;;;;:::i;3244:178::-;3313:4;735:10:77;3367:27:67;735:10:77;3384:2:67;3388:5;3367:9;:27::i;1668:672:71:-;1889:8;1871:15;:26;1867:97;;;1920:33;;-1:-1:-1;;;1920:33:71;;;;;1670:25:188;;;1643:18;;1920:33:71;1524:177:188;1867:97:71;1974:18;1024:95;2033:5;2040:7;2049:5;2056:16;2066:5;-1:-1:-1;;;;;1121:14:78;819:7;1121:14;;;:7;:14;;;;;:16;;;;;;;;;759:395;2056:16:71;2005:78;;;;;;9704:25:188;;;;-1:-1:-1;;;;;9765:32:188;;;9745:18;;;9738:60;9834:32;;;;9814:18;;;9807:60;9883:18;;;9876:34;9926:19;;;9919:35;9970:19;;;9963:35;;;9676:19;;2005:78:71;;;;;;;;;;;;1995:89;;;;;;1974:110;;2095:12;2110:28;2127:10;2110:16;:28::i;:::-;2095:43;;2149:14;2166:28;2180:4;2186:1;2189;2192;2166:13;:28::i;:::-;2149:45;;2218:5;-1:-1:-1;;;;;2208:15:71;:6;-1:-1:-1;;;;;2208:15:71;;2204:88;;2246:35;;-1:-1:-1;;;2246:35:71;;-1:-1:-1;;;;;10201:32:188;;;2246:35:71;;;10183:51:188;10270:32;;10250:18;;;10243:60;10156:18;;2246:35:71;10009:300:188;2204:88:71;2302:31;2311:5;2318:7;2327:5;2302:8;:31::i;:::-;1857:483;;;1668:672;;;;;;;:::o;4618:138:51:-;3851:7;3877:12;;;:6;:12;;;;;:22;;;2473:16;2484:4;2473:10;:16::i;:::-;4723:26:::1;4735:4;4741:7;4723:11;:26::i;2483:232:70:-:0;2560:7;-1:-1:-1;;;;;2583:22:70;;2600:4;2583:22;2579:90;;2628:30;;-1:-1:-1;;;2628:30:70;;-1:-1:-1;;;;;8846:32:188;;2628:30:70;;;8828:51:188;8801:18;;2628:30:70;8682:203:188;2579:90:70;3183:7;2685:23;2678:30;2483:232;-1:-1:-1;;;2483:232:70:o;8630:128:67:-;8714:37;8723:5;8730:7;8739:5;8746:4;8714:8;:37::i;10319:476::-;-1:-1:-1;;;;;3561:18:67;;;10418:24;3561:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;-1:-1:-1;;10484:36:67;;10480:309;;;10559:5;10540:16;:24;10536:130;;;10591:60;;-1:-1:-1;;;10591:60:67;;-1:-1:-1;;;;;10534:32:188;;10591:60:67;;;10516:51:188;10583:18;;;10576:34;;;10626:18;;;10619:34;;;10489:18;;10591:60:67;10314:345:188;10536:130:67;10707:57;10716:5;10723:7;10751:5;10732:16;:24;10758:5;10707:8;:57::i;5297:300::-;-1:-1:-1;;;;;5380:18:67;;5376:86;;5421:30;;-1:-1:-1;;;5421:30:67;;5448:1;5421:30;;;8828:51:188;8801:18;;5421:30:67;8682:203:188;5376:86:67;-1:-1:-1;;;;;5475:16:67;;5471:86;;5514:32;;-1:-1:-1;;;5514:32:67;;5543:1;5514:32;;;8828:51:188;8801:18;;5514:32:67;8682:203:188;5471:86:67;5566:24;5574:4;5580:2;5584:5;5566:7;:24::i;3175:103:51:-;3241:30;3252:4;735:10:77;3241::51;:30::i;6155:316::-;6232:4;6253:22;6261:4;6267:7;6253;:22::i;:::-;6248:217;;6291:12;;;;:6;:12;;;;;;;;-1:-1:-1;;;;;6291:29:51;;;;;;;;;:36;;-1:-1:-1;;6291:36:51;6323:4;6291:36;;;6373:12;735:10:77;;656:96;6373:12:51;-1:-1:-1;;;;;6346:40:51;6364:7;-1:-1:-1;;;;;6346:40:51;6358:4;6346:40;;;;;;;;;;-1:-1:-1;6407:4:51;6400:11;;6248:217;-1:-1:-1;6449:5:51;6442:12;;3945:262:85;3998:7;4029:4;-1:-1:-1;;;;;4038:11:85;4021:28;;:63;;;;;4070:14;4053:13;:31;4021:63;4017:184;;;-1:-1:-1;4107:22:85;;3945:262::o;4017:184::-;4167:23;4304:80;;;2079:95;4304:80;;;11202:25:188;4326:11:85;11243:18:188;;;11236:34;;;;4339:14:85;11286:18:188;;;11279:34;4355:13:85;11329:18:188;;;11322:34;4378:4:85;11372:19:188;;;11365:61;4268:7:85;;11174:19:188;;4304:80:85;;;;;;;;;;;;4294:91;;;;;;4287:98;;4213:179;;6708:317:51;6786:4;6806:22;6814:4;6820:7;6806;:22::i;:::-;6802:217;;;6876:5;6844:12;;;:6;:12;;;;;;;;-1:-1:-1;;;;;6844:29:51;;;;;;;;;;:37;;-1:-1:-1;;6844:37:51;;;6900:40;735:10:77;;6844:12:51;;6900:40;;6876:5;6900:40;-1:-1:-1;6961:4:51;6954:11;;2586:117:80;1597:16;:14;:16::i;:::-;2644:7:::1;:15:::0;;-1:-1:-1;;2644:15:80::1;::::0;;2674:22:::1;735:10:77::0;2683:12:80::1;2674:22;::::0;-1:-1:-1;;;;;8846:32:188;;;8828:51;;8816:2;8801:18;2674:22:80::1;;;;;;;2586:117::o:0;7362:208:67:-;-1:-1:-1;;;;;7432:21:67;;7428:91;;7476:32;;-1:-1:-1;;;7476:32:67;;7505:1;7476:32;;;8828:51:188;8801:18;;7476:32:67;8682:203:188;7428:91:67;7528:35;7544:1;7548:7;7557:5;7528:7;:35::i;7888:206::-;-1:-1:-1;;;;;7958:21:67;;7954:89;;8002:30;;-1:-1:-1;;;8002:30:67;;8029:1;8002:30;;;8828:51:188;8801:18;;8002:30:67;8682:203:188;7954:89:67;8052:35;8060:7;8077:1;8081:5;8052:7;:35::i;2379:143:71:-;-1:-1:-1;;;;;624:14:78;;2470:7:71;624:14:78;;;:7;:14;;;;;;2496:19:71;538:107:78;2339:115:80;1350:19;:17;:19::i;:::-;2398:7:::1;:14:::0;;-1:-1:-1;;2398:14:80::1;2408:4;2398:14;::::0;;2427:20:::1;2434:12;735:10:77::0;;656:96;6105:126:85;6151:13;6183:41;:5;6210:13;6183:26;:41::i;6557:135::-;6606:13;6638:47;:8;6668:16;6638:29;:47::i;5017:176::-;5094:7;5120:66;5153:20;:18;:20::i;:::-;5175:10;4049:4:86;4043:11;-1:-1:-1;;;4067:23:86;;4119:4;4110:14;;4103:39;;;;4171:4;4162:14;;4155:34;4227:4;4212:20;;;3874:374;8225:260:84;8310:7;8330:17;8349:18;8369:16;8389:25;8400:4;8406:1;8409;8412;8389:10;:25::i;:::-;8329:85;;;;;;8424:28;8436:5;8443:8;8424:11;:28::i;:::-;-1:-1:-1;8469:9:84;;8225:260;-1:-1:-1;;;;;;8225:260:84:o;9605:432:67:-;-1:-1:-1;;;;;9717:19:67;;9713:89;;9759:32;;-1:-1:-1;;;9759:32:67;;9788:1;9759:32;;;8828:51:188;8801:18;;9759:32:67;8682:203:188;9713:89:67;-1:-1:-1;;;;;9815:21:67;;9811:90;;9859:31;;-1:-1:-1;;;9859:31:67;;9887:1;9859:31;;;8828:51:188;8801:18;;9859:31:67;8682:203:188;9811:90:67;-1:-1:-1;;;;;9910:18:67;;;;;;;:11;:18;;;;;;;;:27;;;;;;;;;:35;;;9955:76;;;;10005:7;-1:-1:-1;;;;;9989:31:67;9998:5;-1:-1:-1;;;;;9989:31:67;;10014:5;9989:31;;;;1670:25:188;;1658:2;1643:18;;1524:177;9989:31:67;;;;;;;;9605:432;;;;:::o;1652:132:177:-;1746:31;1760:4;1766:2;1770:6;1746:13;:31::i;3408:197:51:-;3496:22;3504:4;3510:7;3496;:22::i;:::-;3491:108;;3541:47;;-1:-1:-1;;;3541:47:51;;-1:-1:-1;;;;;10856:32:188;;3541:47:51;;;10838:51:188;10905:18;;;10898:34;;;10811:18;;3541:47:51;10664:274:188;2078:126:80;1796:7;;;;2136:62;;2172:15;;-1:-1:-1;;;2172:15:80;;;;;;;;;;;2136:62;2078:126::o;1878:128::-;1796:7;;;;1939:61;;;1974:15;;-1:-1:-1;;;1974:15:80;;;;;;;;;;;3368:267:81;3462:13;1390:66;3491:46;;3487:142;;3560:15;3569:5;3560:8;:15::i;:::-;3553:22;;;;3487:142;3613:5;3606:12;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6541:1551:84;6667:17;;;7621:66;7608:79;;7604:164;;;-1:-1:-1;7719:1:84;;-1:-1:-1;7723:30:84;;-1:-1:-1;7755:1:84;7703:54;;7604:164;7879:24;;;7862:14;7879:24;;;;;;;;;11664:25:188;;;11737:4;11725:17;;11705:18;;;11698:45;;;;11759:18;;;11752:34;;;11802:18;;;11795:34;;;7879:24:84;;11636:19:188;;7879:24:84;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;7879:24:84;;-1:-1:-1;;7879:24:84;;;-1:-1:-1;;;;;;;7917:20:84;;7913:113;;-1:-1:-1;7969:1:84;;-1:-1:-1;7973:29:84;;-1:-1:-1;7969:1:84;;-1:-1:-1;7953:62:84;;7913:113;8044:6;-1:-1:-1;8052:20:84;;-1:-1:-1;8052:20:84;;-1:-1:-1;6541:1551:84;;;;;;;;;:::o;8618:532::-;8713:20;8704:5;:29;;;;;;;;:::i;:::-;;8700:444;;8618:532;;:::o;8700:444::-;8809:29;8800:5;:38;;;;;;;;:::i;:::-;;8796:348;;8861:23;;-1:-1:-1;;;8861:23:84;;;;;;;;;;;8796:348;8914:35;8905:5;:44;;;;;;;;:::i;:::-;;8901:243;;8972:46;;-1:-1:-1;;;8972:46:84;;;;;1670:25:188;;;1643:18;;8972:46:84;1524:177:188;8901:243:84;9048:30;9039:5;:39;;;;;;;;:::i;:::-;;9035:109;;9101:32;;-1:-1:-1;;;9101:32:84;;;;;1670:25:188;;;1643:18;;9101:32:84;1524:177:188;5912:1107:67;-1:-1:-1;;;;;6001:18:67;;5997:540;;6153:5;6137:12;;:21;;;;;;;:::i;:::-;;;;-1:-1:-1;5997:540:67;;-1:-1:-1;5997:540:67;;-1:-1:-1;;;;;6211:15:67;;6189:19;6211:15;;;;;;;;;;;6244:19;;;6240:115;;;6290:50;;-1:-1:-1;;;6290:50:67;;-1:-1:-1;;;;;10534:32:188;;6290:50:67;;;10516:51:188;10583:18;;;10576:34;;;10626:18;;;10619:34;;;10489:18;;6290:50:67;10314:345:188;6240:115:67;-1:-1:-1;;;;;6475:15:67;;:9;:15;;;;;;;;;;6493:19;;;;6475:37;;5997:540;-1:-1:-1;;;;;6551:16:67;;6547:425;;6714:12;:21;;;;;;;6547:425;;;-1:-1:-1;;;;;6925:13:67;;:9;:13;;;;;;;;;;:22;;;;;;6547:425;7002:2;-1:-1:-1;;;;;6987:25:67;6996:4;-1:-1:-1;;;;;6987:25:67;;7006:5;6987:25;;;;1670::188;;1658:2;1643:18;;1524:177;6987:25:67;;;;;;;;5912:1107;;;:::o;2078:378:81:-;2137:13;2162:11;2176:16;2187:4;2176:10;:16::i;:::-;2300:14;;;2311:2;2300:14;;;;;;;;;2162:30;;-1:-1:-1;2280:17:81;;2300:14;;;;;;;;;-1:-1:-1;;;2363:16:81;;;-1:-1:-1;2408:4:81;2399:14;;2392:28;;;;-1:-1:-1;2363:16:81;2078:378::o;2528:245::-;2589:7;2661:4;2625:40;;2688:2;2679:11;;2675:69;;;2713:20;;-1:-1:-1;;;2713:20:81;;;;;;;;;;;14:286:188;72:6;125:2;113:9;104:7;100:23;96:32;93:52;;;141:1;138;131:12;93:52;167:23;;-1:-1:-1;;;;;;219:32:188;;209:43;;199:71;;266:1;263;256:12;497:289;539:3;577:5;571:12;604:6;599:3;592:19;660:6;653:4;646:5;642:16;635:4;630:3;626:14;620:47;712:1;705:4;696:6;691:3;687:16;683:27;676:38;775:4;768:2;764:7;759:2;751:6;747:15;743:29;738:3;734:39;730:50;723:57;;;497:289;;;;:::o;791:220::-;940:2;929:9;922:21;903:4;960:45;1001:2;990:9;986:18;978:6;960:45;:::i;1016:131::-;-1:-1:-1;;;;;1091:31:188;;1081:42;;1071:70;;1137:1;1134;1127:12;1152:367;1220:6;1228;1281:2;1269:9;1260:7;1256:23;1252:32;1249:52;;;1297:1;1294;1287:12;1249:52;1336:9;1323:23;1355:31;1380:5;1355:31;:::i;:::-;1405:5;1483:2;1468:18;;;;1455:32;;-1:-1:-1;;;1152:367:188:o;1706:508::-;1783:6;1791;1799;1852:2;1840:9;1831:7;1827:23;1823:32;1820:52;;;1868:1;1865;1858:12;1820:52;1907:9;1894:23;1926:31;1951:5;1926:31;:::i;:::-;1976:5;-1:-1:-1;2033:2:188;2018:18;;2005:32;2046:33;2005:32;2046:33;:::i;:::-;1706:508;;2098:7;;-1:-1:-1;;;2178:2:188;2163:18;;;;2150:32;;1706:508::o;2219:226::-;2278:6;2331:2;2319:9;2310:7;2306:23;2302:32;2299:52;;;2347:1;2344;2337:12;2299:52;-1:-1:-1;2392:23:188;;2219:226;-1:-1:-1;2219:226:188:o;2632:367::-;2700:6;2708;2761:2;2749:9;2740:7;2736:23;2732:32;2729:52;;;2777:1;2774;2767:12;2729:52;2822:23;;;-1:-1:-1;2921:2:188;2906:18;;2893:32;2934:33;2893:32;2934:33;:::i;:::-;2986:7;2976:17;;;2632:367;;;;;:::o;3424:1014::-;3552:6;3560;3568;3576;3584;3637:3;3625:9;3616:7;3612:23;3608:33;3605:53;;;3654:1;3651;3644:12;3605:53;3693:9;3680:23;3712:31;3737:5;3712:31;:::i;:::-;3762:5;-1:-1:-1;3819:2:188;3804:18;;3791:32;3832:33;3791:32;3832:33;:::i;:::-;3884:7;-1:-1:-1;3964:2:188;3949:18;;3936:32;;-1:-1:-1;4045:2:188;4030:18;;4017:32;4072:18;4061:30;;4058:50;;;4104:1;4101;4094:12;4058:50;4127:22;;4180:4;4172:13;;4168:27;-1:-1:-1;4158:55:188;;4209:1;4206;4199:12;4158:55;4249:2;4236:16;4275:18;4267:6;4264:30;4261:50;;;4307:1;4304;4297:12;4261:50;4352:7;4347:2;4338:6;4334:2;4330:15;4326:24;4323:37;4320:57;;;4373:1;4370;4363:12;4320:57;3424:1014;;;;-1:-1:-1;3424:1014:188;;-1:-1:-1;;;4404:2:188;4396:11;;4426:6;3424:1014::o;4443:247::-;4502:6;4555:2;4543:9;4534:7;4530:23;4526:32;4523:52;;;4571:1;4568;4561:12;4523:52;4610:9;4597:23;4629:31;4654:5;4629:31;:::i;4695:1238::-;5101:3;5096;5092:13;5084:6;5080:26;5069:9;5062:45;5143:3;5138:2;5127:9;5123:18;5116:31;5043:4;5170:46;5211:3;5200:9;5196:19;5188:6;5170:46;:::i;:::-;5264:9;5256:6;5252:22;5247:2;5236:9;5232:18;5225:50;5298:33;5324:6;5316;5298:33;:::i;:::-;5362:2;5347:18;;5340:34;;;-1:-1:-1;;;;;5411:32:188;;5405:3;5390:19;;5383:61;5431:3;5460:19;;5453:35;;;5525:22;;;5519:3;5504:19;;5497:51;5597:13;;5619:22;;;5669:2;5695:15;;;;-1:-1:-1;5657:15:188;;;;-1:-1:-1;5738:169:188;5752:6;5749:1;5746:13;5738:169;;;5813:13;;5801:26;;5856:2;5882:15;;;;5847:12;;;;5774:1;5767:9;5738:169;;;-1:-1:-1;5924:3:188;;4695:1238;-1:-1:-1;;;;;;;;;;;4695:1238:188:o;5938:1037::-;6049:6;6057;6065;6073;6081;6089;6097;6150:3;6138:9;6129:7;6125:23;6121:33;6118:53;;;6167:1;6164;6157:12;6118:53;6206:9;6193:23;6225:31;6250:5;6225:31;:::i;:::-;6275:5;-1:-1:-1;6332:2:188;6317:18;;6304:32;6345:33;6304:32;6345:33;:::i;:::-;6397:7;-1:-1:-1;6477:2:188;6462:18;;6449:32;;-1:-1:-1;6580:2:188;6565:18;;6552:32;;-1:-1:-1;6662:3:188;6647:19;;6634:33;6711:4;6698:18;;6686:31;;6676:59;;6731:1;6728;6721:12;6676:59;5938:1037;;;;-1:-1:-1;5938:1037:188;;;;6754:7;6834:3;6819:19;;6806:33;;-1:-1:-1;6938:3:188;6923:19;;;6910:33;;5938:1037;-1:-1:-1;;5938:1037:188:o;6980:388::-;7048:6;7056;7109:2;7097:9;7088:7;7084:23;7080:32;7077:52;;;7125:1;7122;7115:12;7077:52;7164:9;7151:23;7183:31;7208:5;7183:31;:::i;:::-;7233:5;-1:-1:-1;7290:2:188;7275:18;;7262:32;7303:33;7262:32;7303:33;:::i;7373:380::-;7452:1;7448:12;;;;7495;;;7516:61;;7570:4;7562:6;7558:17;7548:27;;7516:61;7623:2;7615:6;7612:14;7592:18;7589:38;7586:161;;7669:10;7664:3;7660:20;7657:1;7650:31;7704:4;7701:1;7694:15;7732:4;7729:1;7722:15;7586:161;;7373:380;;;:::o;7758:730::-;-1:-1:-1;;;;;8027:32:188;;;8009:51;;8096:32;;8091:2;8076:18;;8069:60;8160:2;8145:18;;8138:34;;;8203:2;8188:18;;8181:34;;;8047:3;8246;8231:19;;8224:32;;;8272:19;;8265:35;;;8293:6;8343;8337:3;8322:19;;8309:49;8408:1;8378:22;;;8402:3;8374:32;;;8367:43;;;;8471:2;8450:15;;;-1:-1:-1;;8446:29:188;8431:45;8427:55;;7758:730;-1:-1:-1;;;;;7758:730:188:o;8493:184::-;8563:6;8616:2;8604:9;8595:7;8591:23;8587:32;8584:52;;;8632:1;8629;8622:12;8584:52;-1:-1:-1;8655:16:188;;8493:184;-1:-1:-1;8493:184:188:o;8890:127::-;8951:10;8946:3;8942:20;8939:1;8932:31;8982:4;8979:1;8972:15;9006:4;9003:1;8996:15;9022:125;9087:9;;;9108:10;;;9105:36;;;9121:18;;:::i;9152:128::-;9219:9;;;9240:11;;;9237:37;;;9254:18;;:::i;11840:127::-;11901:10;11896:3;11892:20;11889:1;11882:31;11932:4;11929:1;11922:15;11956:4;11953:1;11946:15", "linkReferences": {}, "immutableReferences": {"28107": [{"start": 3195, "length": 32}], "28109": [{"start": 3153, "length": 32}], "28111": [{"start": 3111, "length": 32}], "28113": [{"start": 3276, "length": 32}], "28115": [{"start": 3316, "length": 32}], "28118": [{"start": 3786, "length": 32}], "28121": [{"start": 3831, "length": 32}]}}, "methodIdentifiers": {"DEFAULT_ADMIN_ROLE()": "a217fddf", "DOMAIN_SEPARATOR()": "3644e515", "MINTER_ROLE()": "d5391393", "PAUSER_ROLE()": "e63ab1e9", "SNAPSHOT_ROLE()": "7028e2cd", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "burn(uint256)": "42966c68", "burnFrom(address,uint256)": "79cc6790", "decimals()": "313ce567", "eip712Domain()": "84b0196e", "flashFee(address,uint256)": "d9d98ce4", "flashLoan(address,address,uint256,bytes)": "5cffe9de", "getRoleAdmin(bytes32)": "248a9ca3", "grantRole(bytes32,address)": "2f2ff15d", "hasRole(bytes32,address)": "91d14854", "maxFlashLoan(address)": "613255ab", "mint(address,uint256)": "40c10f19", "name()": "06fdde03", "nonces(address)": "7ecebe00", "pause()": "8456cb59", "paused()": "5c975abb", "permit(address,address,uint256,uint256,uint8,bytes32,bytes32)": "d505accf", "renounceRole(bytes32,address)": "36568abe", "revokeRole(bytes32,address)": "d547741f", "supportsInterface(bytes4)": "01ffc9a7", "symbol()": "95d89b41", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd", "unpause()": "3f4ba83a"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"mintAmount\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AccessControlBadConfirmation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"neededRole\",\"type\":\"bytes32\"}],\"name\":\"AccessControlUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ECDSAInvalidSignature\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"length\",\"type\":\"uint256\"}],\"name\":\"ECDSAInvalidSignatureLength\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"ECDSAInvalidSignatureS\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSpender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"}],\"name\":\"ERC2612ExpiredSignature\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"ERC2612InvalidSigner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"maxLoan\",\"type\":\"uint256\"}],\"name\":\"ERC3156ExceededMaxLoan\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC3156InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"ERC3156UnsupportedToken\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"EnforcedPause\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ExpectedPause\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"currentNonce\",\"type\":\"uint256\"}],\"name\":\"InvalidAccountNonce\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidShortString\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"str\",\"type\":\"string\"}],\"name\":\"StringTooLong\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"EIP712DomainChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Paused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"previousAdminRole\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"newAdminRole\",\"type\":\"bytes32\"}],\"name\":\"RoleAdminChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleGranted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleRevoked\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Unpaused\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"DEFAULT_ADMIN_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DOMAIN_SEPARATOR\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MINTER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"PAUSER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"SNAPSHOT_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"burn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"burnFrom\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"eip712Domain\",\"outputs\":[{\"internalType\":\"bytes1\",\"name\":\"fields\",\"type\":\"bytes1\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"version\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"chainId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"verifyingContract\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"salt\",\"type\":\"bytes32\"},{\"internalType\":\"uint256[]\",\"name\":\"extensions\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"flashFee\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"contract IERC3156FlashBorrower\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"flashLoan\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleAdmin\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"grantRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hasRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"maxFlashLoan\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"mint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"nonces\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"paused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"permit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"callerConfirmation\",\"type\":\"address\"}],\"name\":\"renounceRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"revokeRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"unpause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"AccessControlBadConfirmation()\":[{\"details\":\"The caller of a function is not the expected one. NOTE: Don't confuse with {AccessControlUnauthorizedAccount}.\"}],\"AccessControlUnauthorizedAccount(address,bytes32)\":[{\"details\":\"The `account` is missing a role.\"}],\"ECDSAInvalidSignature()\":[{\"details\":\"The signature derives the `address(0)`.\"}],\"ECDSAInvalidSignatureLength(uint256)\":[{\"details\":\"The signature has an invalid length.\"}],\"ECDSAInvalidSignatureS(bytes32)\":[{\"details\":\"The signature has an S value that is in the upper half order.\"}],\"ERC20InsufficientAllowance(address,uint256,uint256)\":[{\"details\":\"Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\",\"params\":{\"allowance\":\"Amount of tokens a `spender` is allowed to operate with.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC20InsufficientBalance(address,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC20InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC20InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidSpender(address)\":[{\"details\":\"Indicates a failure with the `spender` to be approved. Used in approvals.\",\"params\":{\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC2612ExpiredSignature(uint256)\":[{\"details\":\"Permit deadline has expired.\"}],\"ERC2612InvalidSigner(address,address)\":[{\"details\":\"Mismatched signature.\"}],\"ERC3156ExceededMaxLoan(uint256)\":[{\"details\":\"The requested loan exceeds the max loan value for `token`.\"}],\"ERC3156InvalidReceiver(address)\":[{\"details\":\"The receiver of a flashloan is not a valid {IERC3156FlashBorrower-onFlashLoan} implementer.\"}],\"ERC3156UnsupportedToken(address)\":[{\"details\":\"The loan token is not valid.\"}],\"EnforcedPause()\":[{\"details\":\"The operation failed because the contract is paused.\"}],\"ExpectedPause()\":[{\"details\":\"The operation failed because the contract is not paused.\"}],\"InvalidAccountNonce(address,uint256)\":[{\"details\":\"The nonce used for an `account` is not the expected current nonce.\"}]},\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"EIP712DomainChanged()\":{\"details\":\"MAY be emitted to signal that the domain could have changed.\"},\"Paused(address)\":{\"details\":\"Emitted when the pause is triggered by `account`.\"},\"RoleAdminChanged(bytes32,bytes32,bytes32)\":{\"details\":\"Emitted when `newAdminRole` is set as ``role``'s admin role, replacing `previousAdminRole` `DEFAULT_ADMIN_ROLE` is the starting admin for all roles, despite {RoleAdminChanged} not being emitted to signal this.\"},\"RoleGranted(bytes32,address,address)\":{\"details\":\"Emitted when `account` is granted `role`. `sender` is the account that originated the contract call. This account bears the admin role (for the granted role). Expected in cases where the role was granted using the internal {AccessControl-_grantRole}.\"},\"RoleRevoked(bytes32,address,address)\":{\"details\":\"Emitted when `account` is revoked `role`. `sender` is the account that originated the contract call:   - if using `revokeRole`, it is the admin role bearer   - if using `renounceRole`, it is the role bearer (i.e. `account`)\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"},\"Unpaused(address)\":{\"details\":\"Emitted when the pause is lifted by `account`.\"}},\"kind\":\"dev\",\"methods\":{\"DOMAIN_SEPARATOR()\":{\"details\":\"Returns the domain separator used in the encoding of the signature for {permit}, as defined by {EIP712}.\"},\"allowance(address,address)\":{\"details\":\"Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"balanceOf(address)\":{\"details\":\"Returns the value of tokens owned by `account`.\"},\"burn(uint256)\":{\"details\":\"Destroys a `value` amount of tokens from the caller. See {ERC20-_burn}.\"},\"burnFrom(address,uint256)\":{\"details\":\"Destroys a `value` amount of tokens from `account`, deducting from the caller's allowance. See {ERC20-_burn} and {ERC20-allowance}. Requirements: - the caller must have allowance for ``accounts``'s tokens of at least `value`.\"},\"decimals()\":{\"details\":\"Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between Ether and Wei. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}.\"},\"eip712Domain()\":{\"details\":\"returns the fields and values that describe the domain separator used by this contract for EIP-712 signature.\"},\"flashFee(address,uint256)\":{\"details\":\"Returns the fee applied when doing flash loans. This function calls the {_flashFee} function which returns the fee applied when doing flash loans.\",\"params\":{\"token\":\"The token to be flash loaned.\",\"value\":\"The amount of tokens to be loaned.\"},\"returns\":{\"_0\":\"The fees applied to the corresponding flash loan.\"}},\"flashLoan(address,address,uint256,bytes)\":{\"details\":\"Performs a flash loan. New tokens are minted and sent to the `receiver`, who is required to implement the {IERC3156FlashBorrower} interface. By the end of the flash loan, the receiver is expected to own value + fee tokens and have them approved back to the token contract itself so they can be burned.\",\"params\":{\"data\":\"An arbitrary datafield that is passed to the receiver.\",\"receiver\":\"The receiver of the flash loan. Should implement the {IERC3156FlashBorrower-onFlashLoan} interface.\",\"token\":\"The token to be flash loaned. Only `address(this)` is supported.\",\"value\":\"The amount of tokens to be loaned.\"},\"returns\":{\"_0\":\"`true` if the flash loan was successful.\"}},\"getRoleAdmin(bytes32)\":{\"details\":\"Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}.\"},\"grantRole(bytes32,address)\":{\"details\":\"Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event.\"},\"hasRole(bytes32,address)\":{\"details\":\"Returns `true` if `account` has been granted `role`.\"},\"maxFlashLoan(address)\":{\"details\":\"Returns the maximum amount of tokens available for loan.\",\"params\":{\"token\":\"The address of the token that is requested.\"},\"returns\":{\"_0\":\"The amount of token that can be loaned. NOTE: This function does not consider any form of supply cap, so in case it's used in a token with a cap like {ERC20Capped}, make sure to override this function to integrate the cap instead of `type(uint256).max`.\"}},\"name()\":{\"details\":\"Returns the name of the token.\"},\"nonces(address)\":{\"details\":\"Returns the current nonce for `owner`. This value must be included whenever a signature is generated for {permit}. Every successful call to {permit} increases ``owner``'s nonce by one. This prevents a signature from being used multiple times.\"},\"paused()\":{\"details\":\"Returns true if the contract is paused, and false otherwise.\"},\"permit(address,address,uint256,uint256,uint8,bytes32,bytes32)\":{\"details\":\"Sets `value` as the allowance of `spender` over ``owner``'s tokens, given ``owner``'s signed approval. IMPORTANT: The same issues {IERC20-approve} has related to transaction ordering also apply here. Emits an {Approval} event. Requirements: - `spender` cannot be the zero address. - `deadline` must be a timestamp in the future. - `v`, `r` and `s` must be a valid `secp256k1` signature from `owner` over the EIP712-formatted function arguments. - the signature must use ``owner``'s current nonce (see {nonces}). For more information on the signature format, see the https://eips.ethereum.org/EIPS/eip-2612#specification[relevant EIP section]. CAUTION: See Security Considerations above.\"},\"renounceRole(bytes32,address)\":{\"details\":\"Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event.\"},\"revokeRole(bytes32,address)\":{\"details\":\"Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event.\"},\"supportsInterface(bytes4)\":{\"details\":\"Returns true if this contract implements the interface defined by `interfaceId`. See the corresponding https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[ERC section] to learn more about how these ids are created. This function call must use less than 30 000 gas.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"totalSupply()\":{\"details\":\"Returns the value of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/shared/StubErc20.sol\":\"StubERC20\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/access/AccessControl.sol\":{\"keccak256\":\"0xb64ecf1154f183412bcde47168f3af245e4120846346a0b3872c631e361156d2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://85331049a60659bc4489733ccd3cbeb177b65691122a8cb637bf9267ab25e23d\",\"dweb:/ipfs/QmbGpDcdwKTirzSCoZfE4rHG7jBSWsE4K2iSb6UCYXtLJv\"]},\"lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x5643a5cadd1278581308b20becb48a50946c159fc31c29fc407ea9a61fc865d1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c7d79f305a239207a24fa2174897c8ea8ff1e81cb790d440fd54c89a0e85f63e\",\"dweb:/ipfs/QmT847eeAMnRN3DaG1zsKNMn7qipNAidqv1REnKexPkrfA\"]},\"lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol\":{\"keccak256\":\"0xb541b133f2d85eb37ae866cb21123be93bd3671b6840c47f951da52d3a704548\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://22443f43ece107eaf453aa9a41a59410cece5429c2779c794fbd2c8b5aa29d38\",\"dweb:/ipfs/Qmbum2jwLYuT9aZ2fr9NMLwWFsVavonrGm2VnbAL9hP2jn\"]},\"lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol\":{\"keccak256\":\"0x3f91c79d6f55db9e4fc36e1cfe6a483a7b0f5be60fecbd979555071673746d47\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9b1e3c64cbeb2757a2a1a45c69f7f3984a93b0eadd1016341b64f9d94f89d7c4\",\"dweb:/ipfs/QmP1Mj14U4vMTFa2rv2nodMbWSCov2ac9Md8W2aUcgYdKX\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol\":{\"keccak256\":\"0xad94c8d7246a50210f7bcb54e5b91fc9f1c6e137263ac972ca5dd0f7f6d4d49d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6938e96fbb0cf3d961788b6c3522400e255d8057d1b9f7e08a50e0b48486b007\",\"dweb:/ipfs/QmNXG3MPzDXjHJ9iWDYCz4vi9RBTgVBnZjndnfBwMfhkyD\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol\":{\"keccak256\":\"0xd92910b862581523ad4e9b99f0bf738f4e62700a5e305953c7fda7db2cfd0f73\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://799f3f0f30822ac806800bbe7fe63b9991490c4e1d9edb75f5993d9350320819\",\"dweb:/ipfs/QmT8T4SokW6YxpDJQiafpeYNRGtjC5gFHxRqKTRXRyP6zB\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol\":{\"keccak256\":\"0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215\",\"dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol\":{\"keccak256\":\"0xc8960b7d3e504e98883de33856a917a473c05034cd61880df2a60b5c47c214fe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://80542373fa695b68d65b4d7222e852df5bda035a82e86ee559336c93b2bf7051\",\"dweb:/ipfs/QmZgH14DPTnKfA5gMSTMiUa6ExuqFfAozmEtLXiWc1iDiw\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol\":{\"keccak256\":\"0xa602c8beaae2d9e2ab1ce585a54547a6d4da32d32e4d002d20ccba55b19258d8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac6553b5b07788a0bb67cc53596837d795280233a9a5cb3a9b3e1fde56822f78\",\"dweb:/ipfs/QmVoHXoma4ZbPKVRJJRosvhipa4rtCMU9QQvWHWKiRUxvi\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318\",\"dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79\",\"dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol\":{\"keccak256\":\"0x2659248df25e34000ed214b3dc8da2160bc39874c992b477d9e2b1b3283dc073\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c345af1b0e7ea28d1216d6a04ab28f5534a5229b9edf9ca3cd0e84950ae58d26\",\"dweb:/ipfs/QmY63jtSrYpLRe8Gj1ep2vMDCKxGNNG3hnNVKBVnrs2nmA\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol\":{\"keccak256\":\"0x4d43ed4b9ff9e4c671274976d59a58dbcc7b69bd7ac11b1710f5b7607cf15b74\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0b47b42998f675cb6a51f2e74ef5906a6fa63ec6718f3fd56ee035d6f77143f9\",\"dweb:/ipfs/QmREnAXqPJBvAwfWfDzaFhNfSRWF4Jdy9ZrpHLw1KdQweY\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol\":{\"keccak256\":\"0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834\",\"dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol\":{\"keccak256\":\"0x62dc9346044aabf22d78541bd495aa6ca05a7f5100aed26196ba35d40b59fcb5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5221df4501c74cd4493fee1a0f0788e02c4dc78c3c601e9f557f557c5a53ea92\",\"dweb:/ipfs/QmZpzyYY9dKLrgvYhXSHT93jwqb1UGvtGNMQk5dpECY5pa\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Nonces.sol\":{\"keccak256\":\"0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e\",\"dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/Pausable.sol\":{\"keccak256\":\"0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c\",\"dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8\"]},\"lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol\":{\"keccak256\":\"0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35\",\"dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/Strings.sol\":{\"keccak256\":\"0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211\",\"dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4\",\"dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol\":{\"keccak256\":\"0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f\",\"dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol\":{\"keccak256\":\"0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59\",\"dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol\":{\"keccak256\":\"0x41ddfafe0d00dc22e35119d41cb0ca93673960689d35710fd12875139e64bd9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49d90142e15cdc4ca00de16e1882fa0a0daad8b46403628beb90c67a3efe4fc4\",\"dweb:/ipfs/QmNizYnFNcGixHxsknEccr2cQWyyQBqFF7h2bXLmefQz6M\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03\",\"dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ\"]},\"lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol\":{\"keccak256\":\"0x743aa2d21f6c26885e0aa6a1c84f7f7bc58fbd6df6bab32bed23f1a41f50454a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a651d38b4261840d3744e571edf2b59455352a8c7dac5d35b019afefa343ea3b\",\"dweb:/ipfs/QmSy3UkTCQDYTjKtGwtqPRrXaofcqtVZxaF6j1dV44wqvr\"]},\"lib/openzeppelin-contracts/contracts/utils/types/Time.sol\":{\"keccak256\":\"0x36776530f012618bc7526ceb28e77b85e582cb12d9b9466a71d4bd6bf952e4cc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f867d046908497287d8a67643dd5d7e38c4027af4ab0a74ffbe1d6790c383c6\",\"dweb:/ipfs/QmQ7s9gMP1nkwThFmoDifnGgpUMsMe5q5ZrAxGDsNnRGza\"]},\"test/shared/StubErc20.sol\":{\"keccak256\":\"0xf3508dc98ae444d142d9993c52cebd856aba40c3e53d64bfeb63e71d190b12ee\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0cc01d254b6d5569d1cb426250db9df1b01afde9dd7b52e1efa0691112fcd918\",\"dweb:/ipfs/QmPnL9wFpSKXprrEFS9kkC2WzK2kAgWSH1snom1wiorCxn\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "mintAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "AccessControlBadConfirmation"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "type": "error", "name": "AccessControlUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "ECDSAInvalidSignature"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "type": "error", "name": "ECDSAInvalidSignatureLength"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "type": "error", "name": "ECDSAInvalidSignatureS"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientAllowance"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientBalance"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "type": "error", "name": "ERC20InvalidApprover"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "type": "error", "name": "ERC20InvalidReceiver"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "type": "error", "name": "ERC20InvalidSender"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "type": "error", "name": "ERC20InvalidSpender"}, {"inputs": [{"internalType": "uint256", "name": "deadline", "type": "uint256"}], "type": "error", "name": "ERC2612ExpiredSignature"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "ERC2612InvalidSigner"}, {"inputs": [{"internalType": "uint256", "name": "max<PERSON><PERSON>", "type": "uint256"}], "type": "error", "name": "ERC3156ExceededMaxLoan"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "type": "error", "name": "ERC3156InvalidReceiver"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "ERC3156UnsupportedToken"}, {"inputs": [], "type": "error", "name": "EnforcedPause"}, {"inputs": [], "type": "error", "name": "ExpectedPause"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "currentNonce", "type": "uint256"}], "type": "error", "name": "InvalidAccountNonce"}, {"inputs": [], "type": "error", "name": "InvalidShortString"}, {"inputs": [{"internalType": "string", "name": "str", "type": "string"}], "type": "error", "name": "StringTooLong"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [], "type": "event", "name": "EIP712DomainChanged", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": false}], "type": "event", "name": "Paused", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "newAdminRole", "type": "bytes32", "indexed": true}], "type": "event", "name": "RoleAdminChanged", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleGranted", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleRevoked", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": false}], "type": "event", "name": "Unpaused", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MINTER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "PAUSER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "SNAPSHOT_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "burn"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "burnFrom"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "eip712Domain", "outputs": [{"internalType": "bytes1", "name": "fields", "type": "bytes1"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "version", "type": "string"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "address", "name": "verifyingContract", "type": "address"}, {"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "uint256[]", "name": "extensions", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "flashFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "contract IERC3156FlashBorrower", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "flashLoan", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "grantRole"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "stateMutability": "view", "type": "function", "name": "maxFlash<PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "mint"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "pause"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "permit"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "renounceRole"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "revokeRole"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "unpause"}], "devdoc": {"kind": "dev", "methods": {"DOMAIN_SEPARATOR()": {"details": "Returns the domain separator used in the encoding of the signature for {permit}, as defined by {EIP712}."}, "allowance(address,address)": {"details": "Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called."}, "approve(address,uint256)": {"details": "See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address."}, "balanceOf(address)": {"details": "Returns the value of tokens owned by `account`."}, "burn(uint256)": {"details": "Destroys a `value` amount of tokens from the caller. See {ERC20-_burn}."}, "burnFrom(address,uint256)": {"details": "Destroys a `value` amount of tokens from `account`, deducting from the caller's allowance. See {ERC20-_burn} and {ERC20-allowance}. Requirements: - the caller must have allowance for ``accounts``'s tokens of at least `value`."}, "decimals()": {"details": "Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between <PERSON><PERSON> and <PERSON>. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}."}, "eip712Domain()": {"details": "returns the fields and values that describe the domain separator used by this contract for EIP-712 signature."}, "flashFee(address,uint256)": {"details": "Returns the fee applied when doing flash loans. This function calls the {_flashFee} function which returns the fee applied when doing flash loans.", "params": {"token": "The token to be flash loaned.", "value": "The amount of tokens to be loaned."}, "returns": {"_0": "The fees applied to the corresponding flash loan."}}, "flashLoan(address,address,uint256,bytes)": {"details": "Performs a flash loan. New tokens are minted and sent to the `receiver`, who is required to implement the {IERC3156FlashBorrower} interface. By the end of the flash loan, the receiver is expected to own value + fee tokens and have them approved back to the token contract itself so they can be burned.", "params": {"data": "An arbitrary datafield that is passed to the receiver.", "receiver": "The receiver of the flash loan. Should implement the {IERC3156FlashBorrower-onFlashLoan} interface.", "token": "The token to be flash loaned. Only `address(this)` is supported.", "value": "The amount of tokens to be loaned."}, "returns": {"_0": "`true` if the flash loan was successful."}}, "getRoleAdmin(bytes32)": {"details": "Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}."}, "grantRole(bytes32,address)": {"details": "Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event."}, "hasRole(bytes32,address)": {"details": "Returns `true` if `account` has been granted `role`."}, "maxFlashLoan(address)": {"details": "Returns the maximum amount of tokens available for loan.", "params": {"token": "The address of the token that is requested."}, "returns": {"_0": "The amount of token that can be loaned. NOTE: This function does not consider any form of supply cap, so in case it's used in a token with a cap like {ERC20Capped}, make sure to override this function to integrate the cap instead of `type(uint256).max`."}}, "name()": {"details": "Returns the name of the token."}, "nonces(address)": {"details": "Returns the current nonce for `owner`. This value must be included whenever a signature is generated for {permit}. Every successful call to {permit} increases ``owner``'s nonce by one. This prevents a signature from being used multiple times."}, "paused()": {"details": "Returns true if the contract is paused, and false otherwise."}, "permit(address,address,uint256,uint256,uint8,bytes32,bytes32)": {"details": "Sets `value` as the allowance of `spender` over ``owner``'s tokens, given ``owner``'s signed approval. IMPORTANT: The same issues {IERC20-approve} has related to transaction ordering also apply here. Emits an {Approval} event. Requirements: - `spender` cannot be the zero address. - `deadline` must be a timestamp in the future. - `v`, `r` and `s` must be a valid `secp256k1` signature from `owner` over the EIP712-formatted function arguments. - the signature must use ``owner``'s current nonce (see {nonces}). For more information on the signature format, see the https://eips.ethereum.org/EIPS/eip-2612#specification[relevant EIP section]. CAUTION: See Security Considerations above."}, "renounceRole(bytes32,address)": {"details": "Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event."}, "revokeRole(bytes32,address)": {"details": "Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event."}, "supportsInterface(bytes4)": {"details": "Returns true if this contract implements the interface defined by `interfaceId`. See the corresponding https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[ERC section] to learn more about how these ids are created. This function call must use less than 30 000 gas."}, "symbol()": {"details": "Returns the symbol of the token, usually a shorter version of the name."}, "totalSupply()": {"details": "Returns the value of tokens in existence."}, "transfer(address,uint256)": {"details": "See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`."}, "transferFrom(address,address,uint256)": {"details": "See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/shared/StubErc20.sol": "StubERC20"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/access/AccessControl.sol": {"keccak256": "0xb64ecf1154f183412bcde47168f3af245e4120846346a0b3872c631e361156d2", "urls": ["bzz-raw://85331049a60659bc4489733ccd3cbeb177b65691122a8cb637bf9267ab25e23d", "dweb:/ipfs/QmbGpDcdwKTirzSCoZfE4rHG7jBSWsE4K2iSb6UCYXtLJv"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x5643a5cadd1278581308b20becb48a50946c159fc31c29fc407ea9a61fc865d1", "urls": ["bzz-raw://c7d79f305a239207a24fa2174897c8ea8ff1e81cb790d440fd54c89a0e85f63e", "dweb:/ipfs/QmT847eeAMnRN3DaG1zsKNMn7qipNAidqv1REnKexPkrfA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol": {"keccak256": "0xb541b133f2d85eb37ae866cb21123be93bd3671b6840c47f951da52d3a704548", "urls": ["bzz-raw://22443f43ece107eaf453aa9a41a59410cece5429c2779c794fbd2c8b5aa29d38", "dweb:/ipfs/Qmbum2jwLYuT9aZ2fr9NMLwWFsVavonrGm2VnbAL9hP2jn"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol": {"keccak256": "0x3f91c79d6f55db9e4fc36e1cfe6a483a7b0f5be60fecbd979555071673746d47", "urls": ["bzz-raw://9b1e3c64cbeb2757a2a1a45c69f7f3984a93b0eadd1016341b64f9d94f89d7c4", "dweb:/ipfs/QmP1Mj14U4vMTFa2rv2nodMbWSCov2ac9Md8W2aUcgYdKX"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol": {"keccak256": "0xad94c8d7246a50210f7bcb54e5b91fc9f1c6e137263ac972ca5dd0f7f6d4d49d", "urls": ["bzz-raw://6938e96fbb0cf3d961788b6c3522400e255d8057d1b9f7e08a50e0b48486b007", "dweb:/ipfs/QmNXG3MPzDXjHJ9iWDYCz4vi9RBTgVBnZjndnfBwMfhkyD"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol": {"keccak256": "0xd92910b862581523ad4e9b99f0bf738f4e62700a5e305953c7fda7db2cfd0f73", "urls": ["bzz-raw://799f3f0f30822ac806800bbe7fe63b9991490c4e1d9edb75f5993d9350320819", "dweb:/ipfs/QmT8T4SokW6YxpDJQiafpeYNRGtjC5gFHxRqKTRXRyP6zB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol": {"keccak256": "0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad", "urls": ["bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215", "dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol": {"keccak256": "0xc8960b7d3e504e98883de33856a917a473c05034cd61880df2a60b5c47c214fe", "urls": ["bzz-raw://80542373fa695b68d65b4d7222e852df5bda035a82e86ee559336c93b2bf7051", "dweb:/ipfs/QmZgH14DPTnKfA5gMSTMiUa6ExuqFfAozmEtLXiWc1iDiw"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol": {"keccak256": "0xa602c8beaae2d9e2ab1ce585a54547a6d4da32d32e4d002d20ccba55b19258d8", "urls": ["bzz-raw://ac6553b5b07788a0bb67cc53596837d795280233a9a5cb3a9b3e1fde56822f78", "dweb:/ipfs/QmVoHXoma4ZbPKVRJJRosvhipa4rtCMU9QQvWHWKiRUxvi"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9", "urls": ["bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318", "dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db", "urls": ["bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79", "dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol": {"keccak256": "0x2659248df25e34000ed214b3dc8da2160bc39874c992b477d9e2b1b3283dc073", "urls": ["bzz-raw://c345af1b0e7ea28d1216d6a04ab28f5534a5229b9edf9ca3cd0e84950ae58d26", "dweb:/ipfs/QmY63jtSrYpLRe8Gj1ep2vMDCKxGNNG3hnNVKBVnrs2nmA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol": {"keccak256": "0x4d43ed4b9ff9e4c671274976d59a58dbcc7b69bd7ac11b1710f5b7607cf15b74", "urls": ["bzz-raw://0b47b42998f675cb6a51f2e74ef5906a6fa63ec6718f3fd56ee035d6f77143f9", "dweb:/ipfs/QmREnAXqPJBvAwfWfDzaFhNfSRWF4Jdy9ZrpHLw1KdQweY"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol": {"keccak256": "0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8", "urls": ["bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834", "dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol": {"keccak256": "0x62dc9346044aabf22d78541bd495aa6ca05a7f5100aed26196ba35d40b59fcb5", "urls": ["bzz-raw://5221df4501c74cd4493fee1a0f0788e02c4dc78c3c601e9f557f557c5a53ea92", "dweb:/ipfs/QmZpzyYY9dKLrgvYhXSHT93jwqb1UGvtGNMQk5dpECY5pa"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Nonces.sol": {"keccak256": "0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f", "urls": ["bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e", "dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Pausable.sol": {"keccak256": "0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f", "urls": ["bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c", "dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol": {"keccak256": "0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402", "urls": ["bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35", "dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"keccak256": "0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52", "urls": ["bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211", "dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5", "urls": ["bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4", "dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol": {"keccak256": "0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e", "urls": ["bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f", "dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol": {"keccak256": "0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631", "urls": ["bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59", "dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol": {"keccak256": "0x41ddfafe0d00dc22e35119d41cb0ca93673960689d35710fd12875139e64bd9f", "urls": ["bzz-raw://49d90142e15cdc4ca00de16e1882fa0a0daad8b46403628beb90c67a3efe4fc4", "dweb:/ipfs/QmNizYnFNcGixHxsknEccr2cQWyyQBqFF7h2bXLmefQz6M"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3", "urls": ["bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03", "dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol": {"keccak256": "0x743aa2d21f6c26885e0aa6a1c84f7f7bc58fbd6df6bab32bed23f1a41f50454a", "urls": ["bzz-raw://a651d38b4261840d3744e571edf2b59455352a8c7dac5d35b019afefa343ea3b", "dweb:/ipfs/QmSy3UkTCQDYTjKtGwtqPRrXaofcqtVZxaF6j1dV44wqvr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/types/Time.sol": {"keccak256": "0x36776530f012618bc7526ceb28e77b85e582cb12d9b9466a71d4bd6bf952e4cc", "urls": ["bzz-raw://9f867d046908497287d8a67643dd5d7e38c4027af4ab0a74ffbe1d6790c383c6", "dweb:/ipfs/QmQ7s9gMP1nkwThFmoDifnGgpUMsMe5q5ZrAxGDsNnRGza"], "license": "MIT"}, "test/shared/StubErc20.sol": {"keccak256": "0xf3508dc98ae444d142d9993c52cebd856aba40c3e53d64bfeb63e71d190b12ee", "urls": ["bzz-raw://0cc01d254b6d5569d1cb426250db9df1b01afde9dd7b52e1efa0691112fcd918", "dweb:/ipfs/QmPnL9wFpSKXprrEFS9kkC2WzK2kAgWSH1snom1wiorCxn"], "license": "MIT"}}, "version": 1}, "id": 177}