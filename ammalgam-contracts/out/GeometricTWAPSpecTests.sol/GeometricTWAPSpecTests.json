{"abi": [{"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "setUp", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "testBlockUpdateBeforeInitialization", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_AfterOneLapAroundLongTermBuffer", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_AfterOneLapAroundMidTermBuffer", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_AfterOneLongTermInterval", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_AfterTwoUpdates", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_ChangeLongTermIntervalBeforeFullCycle", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_DecrementLongTermConfigToLowerThanCurrentIndex", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_FullyLoadBufferAfterIntervalChange", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_InitializeWithOneUpdate", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_InvalidLongTermIntervalConfig", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_InvalidMidTermIntervalConfig", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_LendingStateTickFirstCallNotAvailable", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_LendingStateTickSameBlockCallUnchanged", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_LendingStateTick_NotAvailable", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_LendingStateUpdateForOneBlock", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_LendingStateUpdateForTwoBlocks", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_LongTermBufferBlocksWithMissingBlocks", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_LongTermTickChangesAfterConfigMovedUp", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_MidTermIntervalConfigs", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_ModifyLongTermIntervalWithMissingBlocks", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_NegativeTicks", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_NineDaysLongTermBuffer", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_OneMidTermRoundMissedBlock", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_Outlier_MaxTickDelta", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_Outlier_PriceGoesDown", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_Outlier_PriceGoesUp", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_PartialLoadBufferAfterIntervalChange", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_PositiveTicks", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_PriceMovementDown", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_PriceMovementUp", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_ReturnsSameValueAfterConfigMovedDown", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_SimpleTicksWithMissingBlocks", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_TickRange_LongTermIndexAsFactor", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_TickRange_LongTermIndexAsFactorWithMissingBlocks", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_TickRange_MaxTickBound_LongMin_Fuzz", "inputs": [{"name": "long", "type": "int16", "internalType": "int16"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "testGeometricTWAP_TickRange_MaxTickBound_MidMin_Fuzz", "inputs": [{"name": "mid", "type": "int16", "internalType": "int16"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "testGeometricTWAP_TickRange_MinAndMaxTickBound", "inputs": [], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "testGeometricTWAP_TickRange_MinTickBound_LongMax_Fuzz", "inputs": [{"name": "long", "type": "int16", "internalType": "int16"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "testGeometricTWAP_TickRange_MinTickBound_MidMax_Fuzz", "inputs": [{"name": "mid", "type": "int16", "internalType": "int16"}], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "testGeometricTWAP_TickRange_PriceDownwards", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_TickRange_PriceMovementSharplyDown", "inputs": [], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "testGeometricTWAP_TickRange_PriceMovementSharplyUp", "inputs": [], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "testGeometricTWAP_TickRange_PriceUpwards", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_TickRange_VerifyTicksAfterIntervalChange", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_TickRanges", "inputs": [], "outputs": [], "stateMutability": "pure"}, {"type": "function", "name": "testGeometricTWAP_WithOneMissedBlock", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_WithOneMissedBlockAroundLongTermCycle", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_WithOneMissedBlockAroundMidTermCycle", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testGeometricTWAP_firstBlock", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testMinimumLongTermTimeUpdateConfigInit", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testMinimumLongTermTimeUpdateConfigUpdate", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "testOverFlowOfCumulativeTick", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "UpdateLendingTick", "inputs": [{"name": "lendingStateTick", "type": "int16", "indexed": false, "internalType": "int16"}], "anonymous": false}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "error", "name": "InvalidIntervalConfig", "inputs": []}, {"type": "error", "name": "SafeCastOverflowedIntDowncast", "inputs": [{"name": "bits", "type": "uint8", "internalType": "uint8"}, {"name": "value", "type": "int256", "internalType": "int256"}]}], "bytecode": {"object": "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", "sourceMap": "1108:74852:120:-:0;;;3126:44:97;;;3166:4;-1:-1:-1;;3126:44:97;;;;;;;;1016:26:107;;;;;;;;;;;1108:74852:120;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1108:74852:120:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;37378:1154;;;:::i;:::-;;19879:656;;;:::i;1668:179::-;;;:::i;58645:823::-;;;:::i;26998:698::-;;;:::i;4537:252::-;;;:::i;1853:898::-;;;:::i;59474:826::-;;;:::i;2907:134:100:-;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;3823:151;;;:::i;:::-;;;;;;;:::i;16125:1879:120:-;;;:::i;5884:851::-;;;:::i;55741:931::-;;;:::i;3684:133:100:-;;;:::i;3385:141::-;;;:::i;14277:1842:120:-;;;:::i;28449:846::-;;;:::i;47670:2403::-;;;:::i;54807:928::-;;;:::i;27702:741::-;;;:::i;42274:897::-;;;;;;:::i;:::-;;:::i;3193:186:100:-;;;:::i;:::-;;;;;;;:::i;18948:925:120:-;;;:::i;5136:742::-;;;:::i;4795:335::-;;;:::i;29301:1595::-;;;:::i;46188:369::-;;;:::i;50079:3473::-;;;:::i;34461:603::-;;;:::i;21945:916::-;;;:::i;43610:1285::-;;;;;;:::i;:::-;;:::i;34097:358::-;;;:::i;12963:1308::-;;;:::i;6741:1371::-;;;:::i;3047:140:100:-;;;:::i;:::-;;;;;;;:::i;32118:1973:120:-;;;:::i;23294:1058::-;;;:::i;56678:1961::-;;;:::i;36193:1179::-;;;:::i;3532:146:100:-;;;:::i;:::-;;;;;;;:::i;46563:361:120:-;;;:::i;20541:696::-;;;:::i;30902:1210::-;;;:::i;2754:147:100:-;;;:::i;53558:1243:120:-;;;:::i;21243:696::-;;;:::i;38538:1438::-;;;:::i;2459:141:100:-;;;:::i;1243:204:96:-;;;:::i;:::-;;;6545:14:188;;6538:22;6520:41;;6508:2;6493:18;1243:204:96;6380:187:188;3722:809:120;;;:::i;35070:1117::-;;;:::i;24788:1060::-;;;:::i;8118:1722::-;;;:::i;11419:1538::-;;;:::i;2606:142:100:-;;;:::i;40465:386:120:-;;;:::i;18010:932::-;;;:::i;9846:1567::-;;;:::i;44901:1281::-;;;;;;:::i;:::-;;:::i;46930:734::-;;;:::i;2757:538::-;;;:::i;25854:1138::-;;;:::i;3301:242::-;;;:::i;41367:901::-;;;;;;:::i;:::-;;:::i;1016:26:107:-;;;;;;;;;37378:1154:120;2941:1:30;3387:3;464:2:176;37465:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;37455:7:120;:145;;-1:-1:-1;;;;;;37455:145:120;;-1:-1:-1;;;;;37455:145:120;;;;;;;;;;;;;37610:7;;;:28;1478:40;-1:-1:-1;696:1:21;1478:40:120;:::i;:::-;1577:55;;513:2:176;1577:55:120;:::i;:::-;37610:74;;-1:-1:-1;;;;;;37610:74:120;;;;;;;;;;7539:25:188;;;;464:2:176;7580:18:188;;;7573:49;7512:18;;37610:74:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;37805:20;37828:35;26839:21:21;:15;:21;;26678:200;37828:35:120;37873:7;;37933:16;;37805:58;;-1:-1:-1;37873:7:120;;;-1:-1:-1;;;;;37873:7:120;;:40;;37914:1;;37917:32;;37933:16;;37805:58;37917:32;:::i;:::-;37873:77;;-1:-1:-1;;;;;;37873:77:120;;;;;;;;8005:23:188;;;37873:77:120;;;7987:42:188;8065:23;;8045:18;;;8038:51;7960:18;;37873:77:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;37960:16:120;:32;;-1:-1:-1;;37960:32:120;;;;;;;38066:7;;:49;;-1:-1:-1;;;38066:49:120;;-1:-1:-1;38066:49:120;;;7539:25:188;38054:2:120;7580:18:188;;;7573:49;;;38054:2:120;37960:32;38066:7;;-1:-1:-1;;;;;38066:7:120;;:28;;7512:18:188;;38066:49:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;38164:7:120;;:50;;-1:-1:-1;;;38164:50:120;;:7;:50;;;7539:25:188;38152:2:120;7580:18:188;;;7573:49;;;38152:2:120;-1:-1:-1;38164:7:120;;;;-1:-1:-1;;;;;38164:7:120;;-1:-1:-1;38164:28:120;;7512:18:188;;38164:50:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;38314:7:120;;38396:16;;38277:22;;-1:-1:-1;38314:7:120;;;;-1:-1:-1;;;;;38314:7:120;;-1:-1:-1;38314:40:120;;38277:22;;38396:16;;38358:35;26839:21:21;:15;:21;;26678:200;38358:35:120;:54;;;;:::i;:::-;38314:99;;-1:-1:-1;;;;;;38314:99:120;;;;;;;;8005:23:188;;;38314:99:120;;;7987:42:188;8065:23;;8045:18;;;8038:51;7960:18;;38314:99:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;38277:136;-1:-1:-1;38424:101:120;;;;;38492:1;38452:36;38471:17;38452:16;:36;:::i;:::-;38451:42;;;;:::i;:::-;38424:101;;;;;;;;;;;;;;;;;;;:8;:101::i;:::-;37445:1087;;;;37378:1154::o;19879:656::-;19948:29;19980:6;19948:38;;2941:1:30;20082:22:120;464:2:176;20026:99:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;20016:7:120;:109;;-1:-1:-1;;;;;20016:109:120;;;;;;-1:-1:-1;;;;;;20016:109:120;;;;;;;;;-1:-1:-1;20243:69:120;1478:40;20016:7;696:1:21;1478:40:120;:::i;:::-;20256:51;;;;;;:::i;:::-;20309:2;20243:12;:69::i;:::-;20340:7;;:62;;-1:-1:-1;;;20340:62:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;20220:92:120;;-1:-1:-1;20340:7:120;;;-1:-1:-1;;;;;20340:7:120;;:28;;7512:18:188;;20340:62:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;20413:7:120;;;;;-1:-1:-1;;;;;20413:7:120;;-1:-1:-1;20413:21:120;;-1:-1:-1;20435:19:120;20413:7;20435:15;:19;:::i;:::-;20413:66;;-1:-1:-1;;;;;;20413:66:120;;;;;;;;;;9535:25:188;;;;9608:8;9596:21;;9576:18;;;9569:49;9508:18;;20413:66:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;20489:7:120;;:39;;-1:-1:-1;;;20489:39:120;;464:2:176;20489:39:120;;;9771:40:188;20489:7:120;;;;-1:-1:-1;;;;;20489:7:120;;-1:-1:-1;20489:19:120;;-1:-1:-1;9744:18:188;;20489:39:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;19938:597;;19879:656::o;1668:179::-;1702:24;:74;;-1:-1:-1;;1702:74:120;-1:-1:-1;;;1702:74:120;;;1805:35;26839:21:21;:15;:21;;26678:200;1805:35:120;1786:16;:54;;-1:-1:-1;;1786:54:120;;;;;;;;;;;;1668:179::o;58645:823::-;2941:1:30;3387:3;464:2:176;58721:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;58711:7;;:145;;;;;-1:-1:-1;;;;;58711:145:120;;;;;-1:-1:-1;;;;;58711:145:120;;;;;;58866:37;58906:7;;;;;;;;;-1:-1:-1;;;;;58906:7:120;-1:-1:-1;;;;;58906:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;58866:70;-1:-1:-1;58947:18:120;3542:2:30;1478:40:120;1517:1;696::21;1478:40:120;:::i;:::-;58974:51;;;;:::i;:::-;58947:79;-1:-1:-1;59057:18:120;59100:48;513:2:176;58947:79:120;59100:48;:::i;:::-;59078:71;;464:2:176;59078:71:120;:::i;:::-;59210:7;;:70;;-1:-1:-1;;;59210:70:120;;513:2:176;59210:70:120;;;15871:38:188;15956:1;15945:21;;;15925:18;;;15918:49;59057:92:120;;-1:-1:-1;59210:7:120;;;-1:-1:-1;;;;;59210:7:120;;:28;;15844:18:188;;59210:70:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;59296:7;;;;;;;;;-1:-1:-1;;;;;59296:7:120;-1:-1:-1;;;;;59296:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;59290:36;;59397:64;59406:3;:12;;;59397:64;;59420:12;59397:64;;;;;;;;;;;;;;;-1:-1:-1;;;59397:64:120;;;:8;:64::i;:::-;58701:767;;;58645:823::o;26998:698::-;2941:1:30;3387:3;464:2:176;27092:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;27082:7:120;:145;;-1:-1:-1;;;;;;27082:145:120;;-1:-1:-1;;;;;27082:145:120;;;;;;;;;;;;;27237:29;;-1:-1:-1;;;27237:29:120;;-1:-1:-1;27237:29:120;;;16132:25:188;27237:7:120;;;;;;:26;;16105:18:188;;27237:29:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;27277:25:120;27373:1;27305:65;696:1:21;513:2:176;27305:65:120;:::i;:::-;:69;;;;:::i;:::-;27384:7;;:67;;-1:-1:-1;;;27384:67:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;27277:97:120;;-1:-1:-1;27384:7:120;;;-1:-1:-1;;;;;27384:7:120;;:28;;7512:18:188;;27384:67:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;27461:7:120;;:39;;-1:-1:-1;;;27461:39:120;;464:2:176;27461:39:120;;;9771:40:188;27461:7:120;;;;-1:-1:-1;;;;;27461:7:120;;-1:-1:-1;27461:19:120;;-1:-1:-1;9744:18:188;;27461:39:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;27511:29;3387:3:30;27543:1:120;:40;;;;:::i;:::-;27593:7;;:46;;-1:-1:-1;;;27593:46:120;;16774:8:188;16762:21;;27593:46:120;;;16744:40:188;27511:72:120;;-1:-1:-1;27593:7:120;;;-1:-1:-1;;;;;27593:7:120;;:22;;16717:18:188;;27593:46:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4537:252;4612:61;;-1:-1:-1;;;4612:61:120;;-1:-1:-1;;;;;;;;;;;4612:15:120;;;:61;;-1:-1:-1;;;4628:44:120;4612:61;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4722:1;3387:3:30;464:2:176;4693:89:120;;;;;:::i;:::-;17235:8:188;17223:21;;;17205:40;;17281:21;;;;17276:2;17261:18;;17254:49;17350:1;17339:21;17334:2;17319:18;;17312:49;17193:2;17178:18;4693:89:120;;;;;;;;;;;;;;;;;;;;;;;4683:7;;:99;;;;;-1:-1:-1;;;;;4683:99:120;;;;;-1:-1:-1;;;;;4683:99:120;;;;;;4537:252::o;1853:898::-;1921:61;;-1:-1:-1;;;1921:61:120;;-1:-1:-1;;;;;;;;;;;1921:15:120;;;:61;;-1:-1:-1;;;1937:44:120;1921:61;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1992:135;2047:24;2073:2;2116:1;3387:3:30;2077:40:120;;;;:::i;:::-;1992:41;:135::i;:::-;2164:109;2206:24;2232:2;3387:3:30;2164:41:120;:109::i;:::-;2284:61;;-1:-1:-1;;;2284:61:120;;-1:-1:-1;;;;;;;;;;;2284:15:120;;;:61;;-1:-1:-1;;;2300:44:120;2284:61;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2355:143;2410:24;2436:1;2487;2482:2;3387:3:30;2478:1:120;2439:40;;;;:::i;:::-;:45;;;;:::i;:::-;:49;;;;:::i;2355:143::-;2534:61;;-1:-1:-1;;;2534:61:120;;-1:-1:-1;;;;;;;;;;;2534:15:120;;;:61;;-1:-1:-1;;;2550:44:120;2534:61;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2605:139;2660:24;2686:1;2732:2;3387:3:30;2728:1:120;2689:40;;;;:::i;:::-;:45;;;;:::i;2605:139::-;1853:898::o;59474:826::-;2941:1:30;3387:3;464:2:176;59552:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;59542:7;;:145;;;;;-1:-1:-1;;;;;59542:145:120;;;;;-1:-1:-1;;;;;59542:145:120;;;;;;59697:37;59737:7;;;;;;;;;-1:-1:-1;;;;;59737:7:120;-1:-1:-1;;;;;59737:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;59697:70;-1:-1:-1;59778:18:120;3542:2:30;1478:40:120;1517:1;696::21;1478:40:120;:::i;:::-;59805:51;;;;:::i;:::-;59778:79;-1:-1:-1;59888:18:120;59911:48;513:2:176;59778:79:120;59911:48;:::i;:::-;59909:51;;;:::i;:::-;60021:7;;:70;;-1:-1:-1;;;60021:70:120;;513:2:176;60021:70:120;;;15871:38:188;15956:1;15945:21;;;15925:18;;;15918:49;59888:72:120;;-1:-1:-1;60021:7:120;;;-1:-1:-1;;;;;60021:7:120;;:28;;15844:18:188;;60021:70:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;60107:7;;;;;;;;;-1:-1:-1;;;;;60107:7:120;-1:-1:-1;;;;;60107:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;60101:36;;60208:85;60217:3;:12;;;60208:85;;464:2:176;60231:12:120;:33;;;;:::i;:::-;60208:85;;;;;;;;;;;;;;;-1:-1:-1;;;60208:85:120;;;:8;:85::i;2907:134:100:-;2954:33;3018:16;2999:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2999:35:100;;;;;;;;;;;;;;;;;;;;;;;2907:134;:::o;3823:151::-;3872:42;3948:19;3926:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3926:41:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3823:151;:::o;16125:1879:120:-;2941:1:30;3387:3;464:2:176;16219:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;16209:7:120;:145;;-1:-1:-1;;;;;;16209:145:120;;-1:-1:-1;;;;;16209:145:120;;;;;;;;;;;;;16404:29;;-1:-1:-1;;;16404:29:120;;-1:-1:-1;16404:29:120;;;16132:25:188;16404:7:120;;;;;;:26;;16105:18:188;;16404:29:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;16491:7:120;;;;;-1:-1:-1;;;;;16491:7:120;:28;16553:1;16520:30;513:2:176;16549:1:120;16520:30;:::i;:::-;:34;;;;:::i;:::-;16491:84;;-1:-1:-1;;;;;;16491:84:120;;;;;;;15901:6:188;15889:19;;;16491:84:120;;;15871:38:188;464:2:176;15925:18:188;;;15918:49;15844:18;;16491:84:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;16586:37;16626:7;;;;;;;;;-1:-1:-1;;;;;16626:7:120;-1:-1:-1;;;;;16626:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;16586:70;;16701:59;16710:3;:17;;;16701:59;;16729:1;16701:59;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;16701:59:120;;;:8;:59::i;:::-;16792:25;;;;16770:260;;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;16792:53;;;;;;;:::i;:::-;;;;;16770:260;;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;1577:55;;513:2:176;1577:55:120;:::i;:::-;16887:31;;16916:2;16887:31;:::i;:::-;16859:60;;464:2:176;16859:60:120;:::i;:::-;16770:260;;;;;;;;;;;;;;;;;:8;:260::i;:::-;17062:24;;;;:27;;;17040:236;;;;17103:31;513:2:176;17132::120;17103:31;:::i;:::-;:35;;17137:1;17103:35;:::i;:::-;17040:236;;;;;;;;;;;;;;;;;;;:8;:236::i;:::-;17368:7;;:51;;-1:-1:-1;;;17368:51:120;;:7;:51;;;7539:25:188;464:2:176;7580:18:188;;;7573:49;17368:7:120;;;;-1:-1:-1;;;;;17368:7:120;;:28;;7512:18:188;;17368:51:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;17436:7;;;;;;;;;-1:-1:-1;;;;;17436:7:120;-1:-1:-1;;;;;17436:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;17430:36;-1:-1:-1;17476:27:120;17540:19;17558:1;17540:15;:19;:::i;:::-;17506:55;;464:2:176;17506:55:120;:::i;:::-;17476:85;;17606:59;17615:3;:17;;;17606:59;;17634:1;17606:59;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;17606:59:120;;;:8;:59::i;:::-;17697:25;;;;17675:137;;17723:1;17697:28;;;;17675:137;;17727:21;17675:137;;;;;;;;;;;;;;;;;;;:8;:137::i;:::-;17844:24;;;;:27;17822:175;;;;;;;;;;;;;;;;;;17885:15;;17822:175;17844:27;17822:175;;;:8;:175::i;:::-;16199:1805;;16125:1879::o;5884:851::-;2941:1:30;3387:3;464:2:176;5964:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;5954:7;;:145;;;;;-1:-1:-1;;;;;5954:145:120;;;;;-1:-1:-1;;;;;5954:145:120;;;;;;6110:37;6150:7;;;;;;;;;-1:-1:-1;;;;;6150:7:120;-1:-1:-1;;;;;6150:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;6110:70;;6224:57;6233:3;:16;;;6224:57;;6251:1;6224:57;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;6224:57:120;;;:8;:57::i;:::-;6300:24;;;;6291:65;;6325:1;6300:27;;;;;6291:65;;6329:1;6291:65;;;;;;;;;;;;;-1:-1:-1;;;6291:65:120;;;:8;:65::i;:::-;6375:23;;;;:26;6366:80;;;;;;;;;;;;-1:-1:-1;;;6375:26:120;6366:80;;;;;;;;6403:15;;6366:8;:80::i;:::-;6491:59;6500:3;:17;;;6491:59;;6519:1;6491:59;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;6491:59:120;;;:8;:59::i;:::-;6569:25;;;;6560:76;;6595:1;6569:28;;;;;6560:76;;6599:1;6560:76;;;;;;;;;;;;;;;;;:8;:76::i;:::-;6655:24;;;;:27;6646:82;;;;;;;;;;;;;6655:27;6646:82;;;;;;;;6684:15;;6646:8;:82::i;:::-;5944:791;5884:851::o;55741:931::-;55812:13;55835;55859:15;55877:2;55859:20;;2941:1:30;3387:3;56005:9:120;55911:104;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;55889:7:120;:126;;-1:-1:-1;;;;;;55889:126:120;;-1:-1:-1;;;;;55889:126:120;;;;;;;;;;;;;56026:45;;-1:-1:-1;;;56026:45:120;;56055:4;56026:45;;;7539:25:188;-1:-1:-1;7600:21:188;;;7580:18;;;7573:49;56026:7:120;;;;;;:28;;7512:18:188;;56026:45:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;56082:37;56122:7;;;;;;;;;-1:-1:-1;;;;;56122:7:120;-1:-1:-1;;;;;56122:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;56184:7;;:27;;-1:-1:-1;;;56184:27:120;;9800:1:188;9789:21;;;56184:27:120;;;9771:40:188;56082:70:120;;-1:-1:-1;56184:7:120;;;-1:-1:-1;;;;;56184:7:120;;:16;;9744:18:188;;56184:27:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;56162:49;;;;;;;;;56222:52;56231:7;56222:52;;56240:9;56222:52;;;;;;;;;;;;;;;-1:-1:-1;;;56222:52:120;;;:8;:52::i;:::-;56284:56;56293:7;56284:56;;56302:9;56314:1;56302:13;;;;:::i;:::-;56284:56;;;;;;;;;;;;;;;-1:-1:-1;;;56284:56:120;;;:8;:56::i;:::-;56409:7;;:40;;-1:-1:-1;;;56409:40:120;;:7;:40;;;7539:25:188;-1:-1:-1;;7580:18:188;;;7573:49;;;56395:3:120;56409:7;;;-1:-1:-1;;;;;56409:7:120;;:28;;7512:18:188;;56409:40:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;56465:7;;;;;;;;;-1:-1:-1;;;;;56465:7:120;-1:-1:-1;;;;;56465:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;56528:7;;:25;;-1:-1:-1;;;56528:25:120;;9800:1:188;9789:21;;;56528:25:120;;;9771:40:188;56459:36:120;;-1:-1:-1;56528:7:120;;;-1:-1:-1;;;;;56528:7:120;;:16;;9744:18:188;;56528:25:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;56506:47;;;;;;;;;56563;56572:7;56563:47;;-1:-1:-1;;56563:47:120;;;;;;;;;;;;;-1:-1:-1;;;56563:47:120;;;:8;:47::i;:::-;56620:45;56629:7;56620:45;;56638:2;56620:45;;;;;;;;;;;;;-1:-1:-1;;;56620:45:120;;;:8;:45::i;:::-;55802:870;;;;;55741:931::o;3684:133:100:-;3730:33;3794:16;3775:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3775:35:100;;;;;;;;;;;;;;;;;;;;;;3684:133;:::o;3385:141::-;3433:35;3501:18;3480:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3480:39:100;;;;;;;;;;;;;;;;;;;;;;3385:141;:::o;14277:1842:120:-;2941:1:30;3387:3;464:2:176;14370:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;14360:7:120;:145;;-1:-1:-1;;;;;;14360:145:120;;-1:-1:-1;;;;;14360:145:120;;;;;;;;;;;;;14540:29;;-1:-1:-1;;;14540:29:120;;-1:-1:-1;14540:29:120;;;16132:25:188;14540:7:120;;;;;;:26;;16105:18:188;;14540:29:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;14580:37;14620:7;;;;;;;;;-1:-1:-1;;;;;14620:7:120;-1:-1:-1;;;;;14620:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;14704:7;;14580:70;;-1:-1:-1;14704:7:120;;;-1:-1:-1;;;;;14704:7:120;:28;1378:39;14704:7;637:2:21;1378:39:120;:::i;:::-;14704:75;;-1:-1:-1;;;;;;14704:75:120;;;;;;;;;;7539:25:188;;;;464:2:176;7580:18:188;;;7573:49;7512:18;;14704:75:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;14796:7;;;;;;;;;-1:-1:-1;;;;;14796:7:120;-1:-1:-1;;;;;14796:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;14790:36;-1:-1:-1;14836:27:120;14900:19;14918:1;14900:15;:19;:::i;:::-;14866:55;;464:2:176;14866:55:120;:::i;:::-;14836:85;;15001:57;15010:3;:16;;;15001:57;;15028:1;15001:57;;;;;;;;;;;;;-1:-1:-1;;;15001:57:120;;;:8;:57::i;:::-;15090:24;;;;15068:175;;1378:39;1416:1;637:2:21;1378:39:120;:::i;:::-;15090:51;;;;;;;:::i;:::-;;;;;15068:175;;15155:21;15068:175;;;;;;;;;;;;;;;;;;;:8;:175::i;:::-;15262:23;;;;:26;15253:101;;;;;;;;;;;;;;;;;;15290:1;;15253:101;15262:26;15253:101;;;:8;:101::i;:::-;15386:23;;;;15364:182;;15410:1;15386:26;;;;15364:182;;15426:10;15364:182;;;;;;;;;;;;;;;;;:8;:182::i;:::-;15623:7;;:51;;-1:-1:-1;;;15623:51:120;;:7;:51;;;7539:25:188;464:2:176;7580:18:188;;;7573:49;15623:7:120;;;;-1:-1:-1;;;;;15623:7:120;;:28;;7512:18:188;;15623:51:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;15691:7;;;;;;;;;-1:-1:-1;;;;;15691:7:120;-1:-1:-1;;;;;15691:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;15685:36;-1:-1:-1;15789:19:120;15807:1;15789:15;:19;:::i;:::-;15755:55;;464:2:176;15755:55:120;:::i;:::-;15731:79;;15821:57;15830:3;:16;;;15821:57;;15848:1;15821:57;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;15821:57:120;;;:8;:57::i;:::-;15897:24;;;;15888:105;;15922:1;15897:27;;15888:105;16012:23;;;;:26;16003:109;;;;;;;;;;;;;;;;;;16040:15;;16003:109;16012:26;16003:109;;;:8;:109::i;28449:846::-;2941:1:30;3387:3;464:2:176;28542:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;28532:7:120;:145;;-1:-1:-1;;;;;28532:145:120;;;;;;-1:-1:-1;;;;;;28532:145:120;;;;;;;;;-1:-1:-1;28532:7:120;28716:65;696:1:21;513:2:176;28716:65:120;:::i;:::-;:69;;;;:::i;:::-;28688:97;-1:-1:-1;28795:29:120;28827:40;3387:3:30;28827:1:120;:40;:::i;:::-;28795:72;-1:-1:-1;28877:19:120;28899:51;28795:72;513:2:176;28899:51:120;:::i;:::-;28877:73;-1:-1:-1;28960:26:120;28989:16;29004:1;28877:73;28989:16;:::i;:::-;29037:7;;:67;;-1:-1:-1;;;29037:67:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;28960:45:120;;;;;;-1:-1:-1;29037:7:120;;;-1:-1:-1;;;;;29037:7:120;;:28;;7512:18:188;;29037:67:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;29114:7:120;;:46;;-1:-1:-1;;;29114:46:120;;16774:8:188;16762:21;;29114:46:120;;;16744:40:188;29114:7:120;;;;-1:-1:-1;;;;;29114:7:120;;-1:-1:-1;29114:22:120;;-1:-1:-1;16717:18:188;;29114:46:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;29170:7:120;;:68;;-1:-1:-1;;;29170:68:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;29170:7:120;;;;-1:-1:-1;;;;;29170:7:120;;-1:-1:-1;29170:28:120;;-1:-1:-1;7512:18:188;;29170:68:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;29249:7:120;;:39;;-1:-1:-1;;;29249:39:120;;464:2:176;29249:39:120;;;9771:40:188;29249:7:120;;;;-1:-1:-1;;;;;29249:7:120;;-1:-1:-1;29249:19:120;;-1:-1:-1;9744:18:188;;29249:39:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;28522:773;;;;28449:846::o;47670:2403::-;47748:29;1378:39;1416:1;637:2:21;1378:39:120;:::i;:::-;47748:57;;2941:1:30;3387:3;464:2:176;47826:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;47816:7;;:145;;;;;-1:-1:-1;;;;;47816:145:120;;;;;-1:-1:-1;;;;;47816:145:120;;;;;;47971:37;48011:7;;;;;;;;;-1:-1:-1;;;;;48011:7:120;-1:-1:-1;;;;;48011:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;47971:70;-1:-1:-1;48057:8:120;48052:2015;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;1577:55;;513:2:176;1577:55:120;:::i;:::-;48075:28;;48102:1;48075:28;:::i;:::-;48071:1;:32;;;48052:2015;;;48124:15;48142:5;:1;48146;48142:5;:::i;:::-;48124:23;-1:-1:-1;48165:5:120;;;;48161:95;;48190:7;;:51;;-1:-1:-1;;;48190:51:120;;:7;:51;;;7539:25:188;464:2:176;7580:18:188;;;7573:49;48190:7:120;;;;-1:-1:-1;;;;;48190:7:120;;:28;;7512:18:188;;48190:51:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;48161:95;48275:7;;;;;;;;;-1:-1:-1;;;;;48275:7:120;-1:-1:-1;;;;;48275:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;48269:36;-1:-1:-1;48320:20:120;464:2:176;48349:6:120;:1;48353:2;48349:6;:::i;:::-;48343:34;;;;:::i;:::-;48320:57;-1:-1:-1;48408:13:120;48424:30;513:2:176;48424:1:120;:30;:::i;:::-;48408:46;;48469:17;48523:21;48512:8;:32;;;:42;;;-1:-1:-1;48548:6:120;;;;48512:42;:146;;48650:6;:1;48654:2;48650:6;:::i;:::-;48642:15;;48618:14;:40;;;;;;:::i;:::-;48512:146;;;464:2:176;48512:146:120;48469:220;-1:-1:-1;48703:18:120;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;1577:55;;513:2:176;1577:55:120;:::i;:::-;48747:8;:35;;;:75;;;-1:-1:-1;1478:40:120;1517:1;696::21;1478:40:120;:::i;:::-;48786:6;:36;;;;48747:75;:85;;;-1:-1:-1;48826:6:120;;;;48747:85;:208;;48947:6;:1;48951:2;48947:6;:::i;:::-;48939:15;;48896:3;:25;;;48922:6;48896:33;;;;;;;;;:::i;:::-;;;;;:59;;;;;;:::i;:::-;48747:208;;;464:2:176;48747:208:120;48703:283;-1:-1:-1;49074:30:120;513:2:176;49074:1:120;:30;:::i;:::-;:35;;49108:1;49074:35;49070:268;;49129:194;49159:3;:25;;;49185:6;49159:33;;;;;;;;;:::i;:::-;;;;;49129:194;;49214:14;49129:194;;;;;;;;;;;;;;;;;;;:8;:194::i;:::-;49457:7;;49381:19;;;;;;49457:7;;;-1:-1:-1;;;;;49457:7:120;:24;1478:40;49457:7;696:1:21;1478:40:120;:::i;:::-;49482:6;:35;;;49457:61;;;;;;;;;;;;;6545:14:188;6538:22;6520:41;;6508:2;6493:18;;6380:187;49457:61:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;49380:138;;;;;;49532:53;49541:12;49532:53;;49555:13;49532:53;;;;;;;;;;;;;;;-1:-1:-1;;;49532:53:120;;;:8;:53::i;:::-;49599:50;49608:11;49599:50;;49621:12;49599:50;;;;;;;;;;;;;;;-1:-1:-1;;;49599:50:120;;;:8;:50::i;:::-;49663:51;464:2:176;49663:51:120;;49692:9;49663:51;;;;;;;;;;;;;;;-1:-1:-1;;;49663:51:120;;;:8;:51::i;:::-;49763:13;49778;49795:139;49847:12;49861:11;464:2:176;;49914:6:120;49795:139;;:34;:139::i;:::-;49762:172;;-1:-1:-1;49762:172:120;-1:-1:-1;49948:108:120;49762:172;;464:2:176;50013:33:120;50034:12;464:2:176;50013:33:120;:::i;:::-;50049:6;49948:108;;:19;:108::i;:::-;48110:1957;;;;;;;;;;48105:3;;;;;:::i;:::-;;;;48052:2015;;54807:928;54876:13;54899;54923:15;54941:2;54923:20;;2941:1:30;3387:3;55069:9:120;54975:104;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;54953:7:120;:126;;-1:-1:-1;;;;;;54953:126:120;;-1:-1:-1;;;;;54953:126:120;;;;;;;;;;;;;55090:45;;-1:-1:-1;;;55090:45:120;;55119:4;55090:45;;;7539:25:188;-1:-1:-1;7600:21:188;;;7580:18;;;7573:49;55090:7:120;;;;;;:28;;7512:18:188;;55090:45:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;55146:37;55186:7;;;;;;;;;-1:-1:-1;;;;;55186:7:120;-1:-1:-1;;;;;55186:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;55248:7;;:27;;-1:-1:-1;;;55248:27:120;;9800:1:188;9789:21;;;55248:27:120;;;9771:40:188;55146:70:120;;-1:-1:-1;55248:7:120;;;-1:-1:-1;;;;;55248:7:120;;:16;;9744:18:188;;55248:27:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;55226:49;;;;;;;;;55286:52;55295:7;55286:52;;55304:9;55286:52;;;;;;;;;;;;;;;-1:-1:-1;;;55286:52:120;;;:8;:52::i;:::-;55348:56;55357:7;55348:56;;55366:9;55378:1;55366:13;;;;:::i;55348:56::-;55472:7;;:40;;-1:-1:-1;;;55472:40:120;;:7;:40;;;7539:25:188;55459:2:120;7580:18:188;;;7573:49;;;55459:2:120;55472:7;;;-1:-1:-1;;;;;55472:7:120;;:28;;7512:18:188;;55472:40:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;55528:7;;;;;;;;;-1:-1:-1;;;;;55528:7:120;-1:-1:-1;;;;;55528:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;55591:7;;:25;;-1:-1:-1;;;55591:25:120;;9800:1:188;9789:21;;;55591:25:120;;;9771:40:188;55522:36:120;;-1:-1:-1;55591:7:120;;;-1:-1:-1;;;;;55591:7:120;;:16;;9744:18:188;;55591:25:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;55569:47;;;;;;;;;55626;55635:7;55626:47;;-1:-1:-1;;55626:47:120;;;;;;;;;;;;;-1:-1:-1;;;55626:47:120;;;:8;:47::i;27702:741::-;27823:1;2941::30;27925:67:120;27823:1;3387:3:30;27925:67:120;:::i;:::-;464:2:176;27844:190:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;27834:7:120;:200;;-1:-1:-1;;;;;27834:200:120;;;;;;-1:-1:-1;;;;;;27834:200:120;;;;;;;;;-1:-1:-1;27834:7:120;696:1:21;28085:57:120;28114:28;513:2:176;28085:57:120;:::i;:::-;:96;;;;;;:::i;:::-;:100;;;;:::i;:::-;28195:7;;:67;;-1:-1:-1;;;28195:67:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;28045:140:120;;-1:-1:-1;28195:7:120;;;-1:-1:-1;;;;;28195:7:120;;:28;;7512:18:188;;28195:67:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;28273:7:120;;:39;;-1:-1:-1;;;28273:39:120;;464:2:176;28273:39:120;;;9771:40:188;28273:7:120;;;;-1:-1:-1;;;;;28273:7:120;;-1:-1:-1;28273:19:120;;-1:-1:-1;9744:18:188;;28273:39:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;28323:7:120;;;;;-1:-1:-1;;;;;28323:7:120;;-1:-1:-1;28323:22:120;;-1:-1:-1;28346:40:120;3387:3:30;28323:7:120;28346:40;:::i;:::-;28323:64;;-1:-1:-1;;;;;;28323:64:120;;;;;;;16774:8:188;16762:21;;;28323:64:120;;;16744:40:188;16717:18;;28323:64:120;16600:190:188;42274:897:120;42399:1;42383:13;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;752:3:21;42437:44:120;;;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;42437:44;;;;:::i;:::-;42430:109;;;;:::i;:::-;:146;;;;:::i;:::-;42410:166;-1:-1:-1;42586:10:120;42599:21;42619:1;-1:-1:-1;;42599:21:120;:::i;:::-;42586:34;-1:-1:-1;42630:10:120;42643:8;42586:34;42650:1;42643:8;:::i;:::-;42630:21;-1:-1:-1;42673:72:120;;;;;42715:10;-1:-1:-1;;42684:8:120;:4;42691:1;42684:8;:::i;:::-;:28;;;;:::i;:::-;:41;;;;;;:::i;:::-;1234:6:26;42673:5:120;:72::i;:::-;42661:85;-1:-1:-1;42756:12:120;42778:10;42784:4;42661:85;42778:10;:::i;:::-;42756:33;-1:-1:-1;42799:12:120;42820:25;42835:10;42820:25;;;;;:::i;:::-;42799:47;;42858:13;42873;42890:65;42925:4;42931:3;42936:4;42942;42948:6;42890:65;;:34;:65::i;:::-;42857:98;;;;42965:47;42974:7;42965:47;;-1:-1:-1;;42965:47:120;;;;;;;;;;;;;;;-1:-1:-1;;;42965:47:120;;;:8;:47::i;:::-;43022:17;43042:13;43049:6;43042:4;:13;:::i;:::-;:17;;43058:1;43042:17;:::i;:::-;43022:37;;43069:95;43078:7;43069:95;;43107:11;43087:31;;1234:6:26;43087:31:120;;;:65;;43141:11;43087:65;;;1234:6:26;43087:65:120;43069:95;;;;;;;;;;;;;;;-1:-1:-1;;;43069:95:120;;;:8;:95::i;:::-;42373:798;;;;;;;;;42274:897;:::o;3193:186:100:-;3249:56;3346:26;3317:55;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3317:55:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;18948:925:120;19070:1;19032:35;19113:67;19070:1;3387:3:30;19113:67:120;:::i;:::-;19081:99;;2941:1:30;19256:22:120;464:2:176;19200:99:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;19190:7:120;:109;;-1:-1:-1;;;;;19190:109:120;;;;;;-1:-1:-1;;;;;;19190:109:120;;;;;;;;;:7;2249:11:30;-1:-1:-1;1478:40:120;19190:7;696:1:21;1478:40:120;:::i;:::-;19551:51;;;;;;:::i;:::-;19613:7;;:67;;-1:-1:-1;;;19613:67:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;19522:80:120;;-1:-1:-1;19613:7:120;;;-1:-1:-1;;;;;19613:7:120;;:28;;7512:18:188;;19613:67:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;19690:7:120;;:48;;-1:-1:-1;;;19690:48:120;;;;;16132:25:188;;;19690:7:120;;;;-1:-1:-1;;;;;19690:7:120;;-1:-1:-1;19690:17:120;;-1:-1:-1;16105:18:188;;19690:48:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;19748:7:120;;:68;;-1:-1:-1;;;19748:68:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;19748:7:120;;;;-1:-1:-1;;;;;19748:7:120;;-1:-1:-1;19748:28:120;;-1:-1:-1;7512:18:188;;19748:68:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;19827:7:120;;:39;;-1:-1:-1;;;19827:39:120;;464:2:176;19827:39:120;;;9771:40:188;19827:7:120;;;;-1:-1:-1;;;;;19827:7:120;;-1:-1:-1;19827:19:120;;-1:-1:-1;9744:18:188;;19827:39:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;19022:851;;;;;18948:925::o;5136:742::-;2941:1:30;3387:3;464:2:176;5203:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;5193:7:120;:145;;-1:-1:-1;;;;;;5193:145:120;;-1:-1:-1;;;;;5193:145:120;;;;;;;;;;;;;5398:36;;-1:-1:-1;;;5398:36:120;;464:2:176;5398:36:120;;;9771:40:188;-1:-1:-1;;;;;;5398:7:120;;;;:16;;9744:18:188;;5398:36:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;5348:86;;;;;;;;5445:178;5467:7;5445:178;;752:3:21;464:2:176;5488:63:120;;;;;;:::i;:::-;5445:178;;;;;;;;;;;;;;;;;:8;:178::i;:::-;5633:190;;;;;5676:63;752:3:21;464:2:176;5676:63:120;:::i;:::-;:67;;5742:1;5676:67;:::i;:::-;5633:190;;;;;;;;;;;;;;;;;:8;:190::i;:::-;5833:38;5842:8;464:2:176;5833:8:120;:38::i;4795:335::-;2941:1:30;3387:3;464:2:176;4881:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;4871:7:120;:145;;-1:-1:-1;;;;;4871:145:120;;;;;;-1:-1:-1;;;;;;4871:145:120;;;;;;;;;5027:61;;-1:-1:-1;;;5027:61:120;;-1:-1:-1;;;;;;;;;;;5027:15:120;;;:61;;-1:-1:-1;;;5043:44:120;5027:61;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;5098:7:120;;:25;;-1:-1:-1;;;5098:25:120;;5121:1;5098:25;;;16744:40:188;5098:7:120;;;;-1:-1:-1;;;;;5098:7:120;;-1:-1:-1;5098:22:120;;-1:-1:-1;16717:18:188;;5098:25:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;29301:1595;2941:1:30;3387:3;464:2:176;29397:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;29387:7:120;:145;;-1:-1:-1;;;;;29387:145:120;;;;;;-1:-1:-1;;;;;;29387:145:120;;;;;;;;;29749:9;-1:-1:-1;29387:7:120;29797:65;696:1:21;513:2:176;29797:65:120;:::i;:::-;:69;;;;:::i;:::-;29769:97;-1:-1:-1;29898:4:120;29876:19;29967:17;29982:2;29898:4;29967:17;:::i;:::-;30012:7;;:67;;-1:-1:-1;;;30012:67:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;29938:46:120;;;;;;-1:-1:-1;30012:7:120;;;-1:-1:-1;;;;;30012:7:120;;:28;;7512:18:188;;30012:67:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;30110:37;30150:7;;;;;;;;;-1:-1:-1;;;;;30150:7:120;-1:-1:-1;;;;;30150:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;30110:70;;30190:51;30199:3;:17;;;30190:51;;30218:1;30190:51;;;;;;;;;;;;;-1:-1:-1;;;30190:51:120;;;:8;:51::i;:::-;30252:7;;:48;;-1:-1:-1;;;30252:48:120;;;;;16132:25:188;;;30252:7:120;;;;-1:-1:-1;;;;;30252:7:120;;:17;;16105:18:188;;30252:48:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;30310:7:120;;:36;;-1:-1:-1;;;30310:36:120;;16774:8:188;16762:21;;30310:36:120;;;16744:40:188;30310:7:120;;;;-1:-1:-1;;;;;30310:7:120;;-1:-1:-1;30310:22:120;;-1:-1:-1;16717:18:188;;30310:36:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;30363:7;;;;;;;;;-1:-1:-1;;;;;30363:7:120;-1:-1:-1;;;;;30363:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;30357:36;;30403:47;30412:3;:17;;;30403:47;;30431:1;30403:47;;;;;;;;;;;;;-1:-1:-1;;;30403:47:120;;;:8;:47::i;:::-;30461:7;;:51;;-1:-1:-1;;;30461:51:120;;:7;:51;;;7539:25:188;464:2:176;7580:18:188;;;7573:49;30461:7:120;;;;-1:-1:-1;;;;;30461:7:120;;:28;;7512:18:188;;30461:51:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;30549:7;;;;;;;;;-1:-1:-1;;;;;30549:7:120;-1:-1:-1;;;;;30549:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;30543:36;;30589:47;30598:3;:17;;;30589:47;;30617:1;30589:47;;;;;;;;;;;;;-1:-1:-1;;;30589:47:120;;;:8;:47::i;:::-;30647:7;;:68;;-1:-1:-1;;;30647:68:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;30647:7:120;;;;-1:-1:-1;;;;;30647:7:120;;:28;;7512:18:188;;30647:68:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;30752:7;;;;;;;;;-1:-1:-1;;;;;30752:7:120;-1:-1:-1;;;;;30752:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;30746:36;;30792:47;30801:3;:17;;;30792:47;;30820:1;30792:47;;;;;;;;;;;;;-1:-1:-1;;;30792:47:120;;;:8;:47::i;:::-;30850:7;;:39;;-1:-1:-1;;;30850:39:120;;464:2:176;30850:39:120;;;9771:40:188;30850:7:120;;;;-1:-1:-1;;;;;30850:7:120;;:19;;9744:18:188;;30850:39:120;9629:188:188;46188:369:120;46287:3;46312:2;-1:-1:-1;;46274:10:120;;46398:60;46287:3;46312:2;1176:7:26;;46456:1:120;46398:34;:60::i;:::-;46365:93;;;;46468:34;46477:7;46468:34;;46486:4;46468:34;;;;;;;;;;;;;;;-1:-1:-1;;;46468:34:120;;;:8;:34::i;:::-;46512:38;46521:7;46512:38;;46530:4;46537:1;46530:8;;;;:::i;50079:3473::-;50174:29;1378:39;1416:1;637:2:21;1378:39:120;:::i;:::-;50174:57;;2941:1:30;3387:3;464:2:176;50252:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;50242:7;;:145;;;;;-1:-1:-1;;;;;50242:145:120;;;;;-1:-1:-1;;;;;50242:145:120;;;;;;50397:37;50437:7;;;;;;;;;-1:-1:-1;;;;;50437:7:120;-1:-1:-1;;;;;50437:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;50397:70;-1:-1:-1;50478:25:120;;;50557:2989;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;1577:55;;513:2:176;1577:55:120;:::i;:::-;50580:28;;50607:1;50580:28;:::i;:::-;50576:1;:32;;;50557:2989;;;50629:15;50647:5;:1;50651;50647:5;:::i;:::-;50629:23;-1:-1:-1;50670:5:120;;;;50666:184;;50784:7;;:51;;-1:-1:-1;;;50784:51:120;;:7;:51;;;7539:25:188;464:2:176;7580:18:188;;;7573:49;50784:7:120;;;;-1:-1:-1;;;;;50784:7:120;;:28;;7512:18:188;;50784:51:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;50666:184;50869:7;;;;;;;;;-1:-1:-1;;;;;50869:7:120;-1:-1:-1;;;;;50869:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;50863:36;-1:-1:-1;50914:20:120;464:2:176;50951:21:120;50955:17;50951:21;;;;:::i;:::-;50950:28;;50976:2;50950:28;:::i;:::-;50937:71;;;;:::i;:::-;50914:94;-1:-1:-1;51072:17:120;696:1:21;51072:17:120;51273:30;2941:1:30;51301:2:120;51273:30;:::i;:::-;51164:139;;51202:3;:24;;;51227:17;51202:43;;;;;;;:::i;:::-;;;;;51164:81;;26839:21:21;:15;:21;51164:81:120;:::i;:::-;:139;;;;:147;;51310:1;51164:147;;;51306:1;51164:147;51142:207;;;;;;:::i;:::-;51124:278;;;;:::i;:::-;51104:298;;51417:215;51443:3;:17;;;51417:215;;696:1:21;51479:17:120;51499:1;51479:21;;;;:::i;:::-;51478:62;;;;:::i;:::-;51417:215;;;;;;;;;;;;;;;;;:8;:215::i;:::-;51647:17;51701:21;51690:8;:32;;;:147;;51810:26;:21;51834:2;51810:26;:::i;:::-;51786:14;:51;;;;;;:::i;:::-;51690:147;;;464:2:176;51690:147:120;51647:221;-1:-1:-1;51883:18:120;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;1577:55;;513:2:176;1577:55:120;:::i;:::-;51927:8;:35;;;:183;;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;1577:55;;513:2:176;1577:55:120;:::i;:::-;52080:29;;52107:2;52080:29;:::i;:::-;52026:3;:25;;;52052:17;52026:44;;;;;;;:::i;:::-;;;;;:84;;;;;;:::i;:::-;51927:183;;;464:2:176;51927:183:120;51883:258;;52224:209;52250:3;:24;;;637:2:21;52275:1:120;:39;;;;;;:::i;:::-;52250:65;;;;;;;:::i;:::-;;;;;52224:209;;52333:14;52224:209;;;;;;;;;;;;;;;;;;;:8;:209::i;:::-;52552:1;52548;:5;;;:51;;;;;52582:17;52557:22;:42;52548:51;52544:471;;;52675:7;;:45;;-1:-1:-1;;;52675:45:120;;;;;16132:25:188;;;52675:7:120;;;;-1:-1:-1;;;;;52675:7:120;;:26;;16105:18:188;;52675:45:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;52738:38:120;52759:17;52738:38;;:::i;:::-;;;52795:205;52825:3;:25;;;52851:17;52825:44;;;;;;;:::i;:::-;;;;;52795:205;;52891:14;52795:205;;;;;;;;;;;;;;;;;;;:8;:205::i;:::-;53046:17;53029:14;;53160:79;;;;;;;;;464:2:176;;53160:12:120;:79::i;:::-;53111:128;;;;53254:14;53270;53288:139;53340:12;53354:11;464:2:176;;53407:6:120;53288:34;:139::i;:::-;53253:174;;-1:-1:-1;53253:174:120;-1:-1:-1;53441:94:120;53253:174;;464:2:176;53508:17:120;53518:7;53508;:17;:::i;:::-;53528:6;53441:19;:94::i;:::-;50615:2931;;;;;;;;;;50610:3;;;;;:::i;:::-;;;;50557:2989;;34461:603;2941:1:30;3387:3;464:2:176;34556:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;34546:7:120;:145;;-1:-1:-1;;;;;;34546:145:120;;-1:-1:-1;;;;;34546:145:120;;;;;;;;;;;;;34701:7;;;:28;1478:40;-1:-1:-1;696:1:21;1478:40:120;:::i;:::-;1577:55;;513:2:176;1577:55:120;:::i;:::-;34701:74;;-1:-1:-1;;;;;;34701:74:120;;;;;;;;;;7539:25:188;;;;464:2:176;7580:18:188;;;7573:49;7512:18;;34701:74:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;34795:7:120;;:46;;-1:-1:-1;;;34795:46:120;;34836:1;34795:46;;;7987:42:188;;;8045:18;;;8038:51;34786:76:120;;-1:-1:-1;34795:7:120;;;;-1:-1:-1;;;;;34795:7:120;;-1:-1:-1;34795:40:120;;7960:18:188;;34795:46:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;34786:76;;464:2:176;34786:8:120;:76::i;:::-;34895:7;;:46;;-1:-1:-1;;;34895:46:120;;34936:1;34895:46;;;7987:42:188;;;8045:18;;;8038:51;34873:184:120;;34895:7;;;-1:-1:-1;;;;;34895:7:120;;:40;;7960:18:188;;34895:46:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;34873:184;;464:2:176;34873:184:120;;;;;;;;;;;;;;;;;;;:8;:184::i;21945:916::-;2941:1:30;3387:3;464:2:176;22030:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;22020:7:120;:145;;-1:-1:-1;;;;;22020:145:120;;;;;;-1:-1:-1;;;;;;22020:145:120;;;;;;;;;:7;22255:3;-1:-1:-1;22309:89:120;1478:40;22020:7;696:1:21;1478:40:120;:::i;:::-;22362:30;;22391:1;22362:30;:::i;:::-;22322:71;;3387:3:30;22322:71:120;:::i;22309:89::-;22426:7;;:67;;-1:-1:-1;;;22426:67:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;22268:130:120;;-1:-1:-1;22426:7:120;;;-1:-1:-1;;;;;22426:7:120;;:28;;7512:18:188;;22426:67:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;22503:7:120;;:48;;-1:-1:-1;;;22503:48:120;;;;;16132:25:188;;;22503:7:120;;;;-1:-1:-1;;;;;22503:7:120;;-1:-1:-1;22503:17:120;;-1:-1:-1;16105:18:188;;22503:48:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;22561:7:120;;:68;;-1:-1:-1;;;22561:68:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;22561:7:120;;;;-1:-1:-1;;;;;22561:7:120;;-1:-1:-1;22561:28:120;;-1:-1:-1;7512:18:188;;22561:68:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;22640:16;22688:1;22677:8;22659:15;:26;;;;:::i;:::-;:30;;;;:::i;:::-;22736:7;;:69;;-1:-1:-1;;;22736:69:120;;;;;9535:25:188;;;3387:3:30;9576:18:188;;;9569:49;22640::120;;-1:-1:-1;22736:7:120;;;-1:-1:-1;;;;;22736:7:120;;:21;;9508:18:188;;22736:69:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;43610:1285;43737:1;43721:13;43761:21;43737:1;1234:6:26;43761:21:120;:::i;:::-;43748:34;-1:-1:-1;43792:9:120;43841:303;;;;;;-1:-1:-1;;1176:7:26;1478:40:120;;696:1:21;1478:40:120;:::i;:::-;752:3:21;43979:44:120;;;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;43979:44;;;;:::i;:::-;43972:97;;;;:::i;:::-;:158;;;;:::i;:::-;1234:6:26;43921:8:120;:4;43928:1;43921:8;:::i;:::-;:28;;;;:::i;:::-;:209;;;;;;:::i;:::-;43841:5;:303::i;:::-;43815:339;-1:-1:-1;44164:12:120;44186:11;43815:339;44186:4;:11;:::i;:::-;44164:34;-1:-1:-1;44337:12:120;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;752:3:21;44424:35:120;;;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;44424:35;;;;:::i;:::-;44423:74;;;;:::i;:::-;:127;;;;:::i;:::-;44395:155;;;;;;:::i;:::-;44337:237;;44586:13;44601;44618:65;44653:4;44659:3;44664:4;44670;44676:6;44618:65;;:34;:65::i;:::-;44585:98;;-1:-1:-1;44585:98:120;-1:-1:-1;44693:17:120;44713:13;44720:6;44713:4;:13;:::i;:::-;44693:33;;44736:95;44745:7;44736:95;;-1:-1:-1;;44754:31:120;;:11;:31;;;:65;;44808:11;44754:65;;;-1:-1:-1;;44754:65:120;44736:95;;;;;;;;;;;;;;;-1:-1:-1;;;44736:95:120;;;:8;:95::i;:::-;44841:47;44850:7;44841:47;;1234:6:26;44841:47:120;;;;;;;;;;;;;;;-1:-1:-1;;;44841:47:120;;;:8;:47::i;34097:358::-;34181:22;34206:63;:24;34181:22;;34206:57;:63::i;:::-;34181:88;;;;34279:169;34301:16;34279:169;;-1:-1:-1;;34279:169:120;;;;;;;;;;;;;;;;;;;:8;:169::i;12963:1308::-;2941:1:30;3387:3;464:2:176;13038:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;13028:7:120;:145;;-1:-1:-1;;;;;;13028:145:120;;-1:-1:-1;;;;;13028:145:120;;;;;;;;;;;;;13208:29;;-1:-1:-1;;;13208:29:120;;-1:-1:-1;13208:29:120;;;16132:25:188;13208:7:120;;;;;;:26;;16105:18:188;;13208:29:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;13247:7:120;;:51;;-1:-1:-1;;;13247:51:120;;:7;:51;;;7539:25:188;464:2:176;7580:18:188;;;7573:49;13247:7:120;;;;-1:-1:-1;;;;;13247:7:120;;:28;;7512:18:188;;13247:51:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;13309:37;13349:7;;;;;;;;;-1:-1:-1;;;;;13349:7:120;-1:-1:-1;;;;;13349:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;13309:70;-1:-1:-1;13389:27:120;13453:19;13471:1;13453:15;:19;:::i;:::-;13419:55;;464:2:176;13419:55:120;:::i;:::-;13389:85;;13518:57;13527:3;:16;;;13518:57;;13545:1;13518:57;;;;;;;;;;;;;-1:-1:-1;;;13518:57:120;;;:8;:57::i;:::-;13594:24;;;;13585:65;;13619:1;13594:27;;13585:65;13669:24;;;;13660:101;;13694:1;13669:27;;;;13660:101;;13698:21;13660:101;;;;;;;;;;;;;;;;;;;:8;:101::i;:::-;13780:23;;;;:26;13771:89;;;;;;;;;;;;;;;;;;13808:1;;13771:89;13780:26;13771:89;;;:8;:89::i;:::-;13879:23;;;;13870:98;;13903:1;13879:26;;;;13870:98;;13907:15;13870:98;;;;;;;;;;;;;;;;;:8;:98::i;:::-;14013:59;14022:3;:17;;;14013:59;;14041:1;14013:59;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;14013:59:120;;;:8;:59::i;:::-;14091:25;;;;:28;14082:71;;;;;;;;;;;;;14091:28;14082:71;;;;;;;;14117:1;;14082:8;:71::i;:::-;14172:24;;;;:27;14163:101;;;;;;;;;;;;;;;;;;14201:1;;14163:101;14172:27;14163:101;;;:8;:101::i;6741:1371::-;2941:1:30;3387:3;464:2:176;6813:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;6803:7:120;:145;;-1:-1:-1;;;;;;6803:145:120;;-1:-1:-1;;;;;6803:145:120;;;;;;;;;;;;;6958:51;;-1:-1:-1;;;6958:51:120;;-1:-1:-1;6958:51:120;;;7539:25:188;464:2:176;7580:18:188;;;7573:49;6958:7:120;;;;;;:28;;7512:18:188;;6958:51:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;7020:37;7060:7;;;;;;;;;-1:-1:-1;;;;;7060:7:120;-1:-1:-1;;;;;7060:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;7020:70;-1:-1:-1;7100:27:120;7164:19;7182:1;7164:15;:19;:::i;:::-;7130:55;;464:2:176;7130:55:120;:::i;:::-;7100:85;;7229:57;7238:3;:16;;;7229:57;;7256:1;7229:57;;;;;;;;;;;;;-1:-1:-1;;;7229:57:120;;;:8;:57::i;:::-;7305:24;;;;7296:65;;7330:1;7305:27;;7296:65;7380:24;;;;7371:108;;7405:1;7380:27;;;;7371:108;;7409:21;7371:108;;;;;;;;;;;;;;;;;;;:8;:108::i;:::-;7498:23;;;;:26;7489:69;;;;;;;;;;;;;7498:26;7489:69;;;;;;;;7526:1;;7489:8;:69::i;:::-;7577:23;;;;7568:85;;7601:1;7577:26;;;;7568:85;;7605:15;7568:85;;;;;;;;;;;;;;;;;:8;:85::i;:::-;7698:59;7707:3;:17;;;7698:59;;7726:1;7698:59;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;7698:59:120;;;:8;:59::i;:::-;7776:25;;;;7767:76;;7802:1;7776:28;;7767:76;7862:25;;;;7853:71;;7888:1;7862:28;;;;7853:71;;7892:1;7853:71;;;;;;;;;;;;;;;;;:8;:71::i;:::-;7943:24;;;;:27;7934:80;;;;;;;;;;;;;;;;;;7972:1;;7934:80;7943:27;7934:80;;;:8;:80::i;:::-;8033:24;;;;8024:81;;8058:1;8033:27;;;;8024:81;;8062:1;8024:81;;;;;;;;;;;;;;;;;:8;:81::i;3047:140:100:-;3095:34;3162:18;3141:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;32118:1973:120;32256:1;32211:42;32318:74;32256:1;3387:3:30;32318:74:120;:::i;:::-;32267:125;;2941:1:30;32480:29:120;464:2:176;32424:106:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;32402:7:120;:128;;-1:-1:-1;;;;;32402:128:120;;;;;;-1:-1:-1;;;;;;32402:128:120;;;;;;;;;-1:-1:-1;32402:7:120;1478:40;32402:7;696:1:21;1478:40:120;:::i;:::-;32734:30;;32763:1;32734:30;:::i;:::-;32683:47;32696:29;32683:47;;32727:2;32683:12;:47::i;:::-;:82;;;;:::i;:::-;:86;;;;:::i;:::-;32779:7;;:70;;-1:-1:-1;;;32779:70:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;32640:129:120;;-1:-1:-1;32779:7:120;;;-1:-1:-1;;;;;32779:7:120;;:28;;7512:18:188;;32779:70:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;32901:37;32941:7;;;;;;;;;-1:-1:-1;;;;;32941:7:120;-1:-1:-1;;;;;32941:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;32901:70;;32982:59;32991:3;:17;;;32982:59;;33010:1;32982:59;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;32982:59:120;;;:8;:59::i;:::-;33073:24;;;;33051:296;;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;33073:52;;;;;;;:::i;:::-;;;;;33051:296;;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;33139:47;33152:29;33139:47;;33183:2;33139:12;:47::i;:::-;:76;;;;:::i;:::-;:81;;33218:2;33139:81;:::i;:::-;:85;;33223:1;33139:85;:::i;:::-;33051:296;;;;;;;;;;;;;;;;;:8;:296::i;:::-;33491:7;;:39;;-1:-1:-1;;;33491:39:120;;464:2:176;33491:39:120;;;9771:40:188;3387:3:30;;33491:7:120;;;-1:-1:-1;;;;;33491:7:120;;:19;;9744:18:188;;33491:39:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;33540:7:120;;:50;;-1:-1:-1;;;33540:50:120;;16774:8:188;16762:21;;33540:50:120;;;16744:40:188;33540:7:120;;;;-1:-1:-1;;;;;33540:7:120;;-1:-1:-1;33540:22:120;;-1:-1:-1;16717:18:188;;33540:50:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;33600:7:120;;:39;;-1:-1:-1;;;33600:39:120;;464:2:176;33600:39:120;;;9771:40:188;33600:7:120;;;;-1:-1:-1;;;;;33600:7:120;;-1:-1:-1;33600:19:120;;-1:-1:-1;9744:18:188;;33600:39:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;33693:7;;;;;;;;;-1:-1:-1;;;;;33693:7:120;-1:-1:-1;;;;;33693:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;33687:36;;33733:59;33742:3;:17;;;33733:59;;33761:1;33733:59;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;33733:59:120;;;:8;:59::i;:::-;33860:7;;:51;;-1:-1:-1;;;33860:51:120;;:7;:51;;;7539:25:188;464:2:176;7580:18:188;;;7573:49;33860:7:120;;;;-1:-1:-1;;;;;33860:7:120;;:28;;7512:18:188;;33860:51:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;33927:7;;;;;;;;;-1:-1:-1;;;;;33927:7:120;-1:-1:-1;;;;;33927:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;33921:36;;33967:68;33976:3;:17;;;33967:68;;33995:1;33967:68;;;;;;;;;;;;;;;;;:8;:68::i;23294:1058::-;23356:29;23388:40;3387:3:30;23388:1:120;:40;:::i;:::-;23356:72;;2941:1:30;23504:22:120;464:2:176;23448:99:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;23438:7:120;:109;;-1:-1:-1;;;;;23438:109:120;;;;;;-1:-1:-1;;;;;;23438:109:120;;;;;;;;;-1:-1:-1;23586:75:120;1478:40;23438:7;696:1:21;1478:40:120;:::i;:::-;23625:30;;23654:1;23625:30;:::i;23586:75::-;23729:7;;:67;;-1:-1:-1;;;23729:67:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;23558:103:120;;-1:-1:-1;23717:1:120;;23729:7;;;;-1:-1:-1;;;;;23729:7:120;;:28;;7512:18:188;;23729:67:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;23863:7:120;;:70;;-1:-1:-1;;;23863:70:120;;;;;7539:25:188;;;1234:6:26;7580:18:188;;;7573:49;;;1234:6:26;-1:-1:-1;23863:7:120;;;;-1:-1:-1;;;;;23863:7:120;;-1:-1:-1;23863:28:120;;7512:18:188;;23863:70:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;23944:7:120;;;;;-1:-1:-1;;;;;23944:7:120;;-1:-1:-1;23944:21:120;;-1:-1:-1;23966:19:120;23944:7;23966:15;:19;:::i;:::-;23944:66;;-1:-1:-1;;;;;;23944:66:120;;;;;;;;;;9535:25:188;;;;9608:8;9596:21;;9576:18;;;9569:49;9508:18;;23944:66:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;24021:17;3542:2:30;464::176;24041:42:120;;;;:::i;:::-;24021:62;;24115:17;24137:67;464:2:176;24192:11:120;24137:34;:67::i;:::-;-1:-1:-1;24264:7:120;;:81;;-1:-1:-1;;;24264:81:120;;464:2:176;24264:81:120;;;22632:40:188;;;22661:1;22708:21;;;22688:18;;;22681:49;22766:21;;;22746:18;;;22739:49;22824:21;;;22804:18;;;22797:49;24114:90:120;;-1:-1:-1;24264:7:120;;;;-1:-1:-1;;;;;24264:7:120;;:19;;22604::188;;24264:81:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;23346:1006;;;;;;;23294:1058::o;56678:1961::-;2941:1:30;3387:3;464:2:176;56755:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;56745:7;;:145;;;;;-1:-1:-1;;;;;56745:145:120;;;;;-1:-1:-1;;;;;56745:145:120;;;;;;56900:37;56940:7;;;;;;;;;-1:-1:-1;;;;;56940:7:120;-1:-1:-1;;;;;56940:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;56981:7;;:51;;-1:-1:-1;;;56981:51:120;;:7;:51;;;7539:25:188;464:2:176;7580:18:188;;;7573:49;56900:70:120;;-1:-1:-1;56981:7:120;;;-1:-1:-1;;;;;56981:7:120;;:28;;7512:18:188;;56981:51:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;57048:7;;;;;;;;;-1:-1:-1;;;;;57048:7:120;-1:-1:-1;;;;;57048:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;57042:36;-1:-1:-1;57089:27:120;57153:19;57171:1;57153:15;:19;:::i;:::-;57119:55;;464:2:176;57119:55:120;:::i;:::-;57254:24;;;;57089:85;;-1:-1:-1;57245:59:120;;57279:1;57254:27;;;;57245:59;;57283:1;57245:59;;;;;;;;;;;;;-1:-1:-1;;;57245:59:120;;;:8;:59::i;:::-;57323:24;;;;57314:97;;57348:1;57323:27;;;;57314:97;;57352:21;57314:97;;;;;;;;;;;;;;;;;;;:8;:97::i;:::-;57478:7;;:50;;-1:-1:-1;;;57478:50:120;;:7;:50;;;7539:25:188;1234:6:26;7580:18:188;;;7573:49;57478:7:120;;;;-1:-1:-1;;;;;57478:7:120;;:28;;7512:18:188;;57478:50:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;57544:7;;;;;;;;;-1:-1:-1;;;;;57544:7:120;-1:-1:-1;;;;;57544:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;57538:36;-1:-1:-1;57585:18:120;3542:2:30;1478:40:120;1517:1;696::21;1478:40:120;:::i;:::-;57612:51;;;;:::i;:::-;57585:79;-1:-1:-1;57695:18:120;57716:33;57585:79;464:2:176;57716:33:120;:::i;:::-;57695:54;-1:-1:-1;57759:20:120;57783:33;464:2:176;57695:54:120;57783:33;:::i;:::-;57782:40;;57820:2;57782:40;:::i;:::-;57919:24;;;;57759:63;;-1:-1:-1;57910:106:120;;57944:1;57919:27;;;;57910:106;;57948:14;57910:106;;;;;;;;;;;;;;;;;;;:8;:106::i;:::-;58026:77;58035:3;:12;;;58026:77;;58049:12;58026:77;;;;;;;;;;;;;;;;;;;:8;:77::i;:::-;58170:7;;:50;;-1:-1:-1;;;58170:50:120;;:7;:50;;;7539:25:188;-1:-1:-1;;7580:18:188;;;7573:49;58170:7:120;;;;-1:-1:-1;;;;;58170:7:120;;:28;;7512:18:188;;58170:50:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;58236:7;;;;;;;;;-1:-1:-1;;;;;58236:7:120;-1:-1:-1;;;;;58236:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;58230:36;-1:-1:-1;58277:28:120;58293:12;58277:28;;:::i;:::-;;-1:-1:-1;58333:17:120;58277:28;58348:2;58333:17;:::i;:::-;58315:35;;;;:::i;:::-;58447:24;;;;58315:35;;-1:-1:-1;58438:107:120;;58472:1;58447:27;;;;58438:107;;58476:14;58438:107;;;;;;;;;;;;;;;;;;;:8;:107::i;:::-;58555:77;58564:3;:12;;;58555:77;;58578:12;58555:77;;;;;;;;;;;;;;;;;;;:8;:77::i;36193:1179::-;2941:1:30;3387:3;464:2:176;36279:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;36269:7:120;:145;;-1:-1:-1;;;;;;36269:145:120;;-1:-1:-1;;;;;36269:145:120;;;;;;;;;;;;;36424:7;;;:28;1478:40;-1:-1:-1;696:1:21;1478:40:120;:::i;:::-;1577:55;;513:2:176;1577:55:120;:::i;:::-;36424:74;;-1:-1:-1;;;;;;36424:74:120;;;;;;;;;;7539:25:188;;;;464:2:176;7580:18:188;;;7573:49;7512:18;;36424:74:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;36619:20;36642:35;26839:21:21;:15;:21;;26678:200;36642:35:120;36687:7;;36747:16;;36619:58;;-1:-1:-1;36687:7:120;;;-1:-1:-1;;;;;36687:7:120;;:40;;36728:1;;36731:32;;36747:16;;36619:58;36731:32;:::i;:::-;36687:77;;-1:-1:-1;;;;;;36687:77:120;;;;;;;;8005:23:188;;;36687:77:120;;;7987:42:188;8065:23;;8045:18;;;8038:51;7960:18;;36687:77:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;36774:16:120;:32;;-1:-1:-1;;36774:32:120;;;;;;;36880:7;;:49;;-1:-1:-1;;;36880:49:120;;-1:-1:-1;36880:49:120;;;7539:25:188;36868:2:120;7580:18:188;;;7573:49;;;36868:2:120;36774:32;36880:7;;-1:-1:-1;;;;;36880:7:120;;:28;;7512:18:188;;36880:49:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;36939:7;;;;;;;;;-1:-1:-1;;;;;36939:7:120;-1:-1:-1;;;;;36939:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;37069:7:120;;37151:16;;37032:22;;37069:7;;;-1:-1:-1;;;;;37069:7:120;;:40;;37032:22;;37151:16;;37113:35;26839:21:21;:15;:21;;26678:200;37113:35:120;:54;;;;:::i;:::-;37069:99;;-1:-1:-1;;;;;;37069:99:120;;;;;;;;8005:23:188;;;37069:99:120;;;7987:42:188;8065:23;;8045:18;;;8038:51;7960:18;;37069:99:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;37032:136;;37290:75;37299:16;37290:75;;37317:16;37290:75;;;;;;;;;;;;;;;;;;;:8;:75::i;3532:146:100:-;3580:40;3653:18;3632:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;46563:361:120;46660:2;46684;1234:6:26;46647:10:120;;46770:60;46660:2;46684;1234:6:26;;46828:1:120;46770:34;:60::i;:::-;46737:93;;;;46840:33;46849:7;46840:33;;46858:3;46840:33;;;;;;;;;;;;;;;-1:-1:-1;;;46840:33:120;;;:8;:33::i;:::-;46883:34;46892:7;46883:34;;46901:4;46883:34;;;;;;;;;;;;;;;-1:-1:-1;;;46883:34:120;;;:8;:34::i;20541:696::-;20639:2;20601:35;20683:67;20639:2;3387:3:30;20683:67:120;:::i;:::-;20651:99;;2941:1:30;20826:22:120;464:2:176;20770:99:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;20760:7:120;:109;;-1:-1:-1;;;;;20760:109:120;;;;;;-1:-1:-1;;;;;;20760:109:120;;;;;;;;;-1:-1:-1;20903:75:120;1478:40;20760:7;696:1:21;1478:40:120;:::i;:::-;20942:30;;20971:1;20942:30;:::i;20903:75::-;21054:7;;:56;;-1:-1:-1;;;21054:56:120;;;;;7539:25:188;;;-1:-1:-1;;7580:18:188;;;7573:49;;;20880:98:120;;-1:-1:-1;21054:7:120;;;;-1:-1:-1;;;;;21054:7:120;;:28;;7512:18:188;;21054:56:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;21121:7:120;;;;;-1:-1:-1;;;;;21121:7:120;;-1:-1:-1;21121:21:120;;-1:-1:-1;21143:19:120;21121:7;21143:15;:19;:::i;:::-;21121:66;;-1:-1:-1;;;;;;21121:66:120;;;;;;;;;;9535:25:188;;;;9608:8;9596:21;;9576:18;;;9569:49;9508:18;;21121:66:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;21197:7:120;;:33;;-1:-1:-1;;;21197:33:120;;9800:1:188;9789:21;;;21197:33:120;;;9771:40:188;21197:7:120;;;;-1:-1:-1;;;;;21197:7:120;;-1:-1:-1;21197:19:120;;-1:-1:-1;9744:18:188;;21197:33:120;9629:188:188;30902:1210:120;2941:1:30;3387:3;464:2:176;30996:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;30986:7:120;:145;;-1:-1:-1;;;;;30986:145:120;;;;;;-1:-1:-1;;;;;;30986:145:120;;;;;;;;;-1:-1:-1;1478:40:120;30986:7;696:1:21;1478:40:120;:::i;:::-;31170:55;;513:2:176;31170:55:120;:::i;:::-;31142:83;-1:-1:-1;31235:21:120;31259:40;3387:3:30;31259:1:120;:40;:::i;:::-;31235:64;-1:-1:-1;31338:1:120;31309:26;31372:40;3387:3:30;31372:1:120;:40;:::i;:::-;31349:63;-1:-1:-1;31422:25:120;31450:42;513:2:176;31349:63:120;31450:42;:::i;:::-;:46;;31495:1;31450:46;:::i;:::-;31507:7;;:67;;-1:-1:-1;;;31507:67:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;31422:74:120;;;;;;-1:-1:-1;31507:7:120;;;-1:-1:-1;;;;;31507:7:120;;:28;;7512:18:188;;31507:67:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;31584:7:120;;:39;;-1:-1:-1;;;31584:39:120;;464:2:176;31584:39:120;;;9771:40:188;31584:7:120;;;;-1:-1:-1;;;;;31584:7:120;;-1:-1:-1;31584:19:120;;-1:-1:-1;9744:18:188;;31584:39:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;31633:7:120;;:38;;-1:-1:-1;;;31633:38:120;;16774:8:188;16762:21;;31633:38:120;;;16744:40:188;31633:7:120;;;;-1:-1:-1;;;;;31633:7:120;;-1:-1:-1;31633:22:120;;-1:-1:-1;16717:18:188;;31633:38:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;31681:7:120;;:39;;-1:-1:-1;;;31681:39:120;;464:2:176;31681:39:120;;;9771:40:188;31681:7:120;;;;-1:-1:-1;;;;;31681:7:120;;-1:-1:-1;31681:19:120;;-1:-1:-1;9744:18:188;;31681:39:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;31731:7:120;;:68;;-1:-1:-1;;;31731:68:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;31731:7:120;;;;-1:-1:-1;;;;;31731:7:120;;-1:-1:-1;31731:28:120;;-1:-1:-1;7512:18:188;;31731:68:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;31809:7:120;;:39;;-1:-1:-1;;;31809:39:120;;464:2:176;31809:39:120;;;9771:40:188;31809:7:120;;;;-1:-1:-1;;;;;31809:7:120;;-1:-1:-1;31809:19:120;;-1:-1:-1;9744:18:188;;31809:39:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;31941:7:120;;:37;;-1:-1:-1;;;31941:37:120;;16774:8:188;16762:21;;31941:37:120;;;16744:40:188;31941:7:120;;;;-1:-1:-1;;;;;31941:7:120;;-1:-1:-1;31941:22:120;;-1:-1:-1;16717:18:188;;31941:37:120;16600:190:188;2754:147:100;2803:40;2876:18;2855:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;53558:1243:120;53730:135;;513:2:176;;2941:1:30;;3387:3;;464:2:176;;53730:135:120;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;53720:7:120;:145;;-1:-1:-1;;;;;53720:145:120;;;;;;-1:-1:-1;;;;;;53720:145:120;;;;;;;;;-1:-1:-1;53720:7:120;53904:68;696:1:21;53904:68:120;;;;:::i;:::-;:72;;;;:::i;:::-;54009:7;;:67;;-1:-1:-1;;;54009:67:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;53876:100:120;;-1:-1:-1;54009:7:120;;;-1:-1:-1;;;;;54009:7:120;;:28;;7512:18:188;;54009:67:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;54128:2:120;;-1:-1:-1;54087:38:120;;-1:-1:-1;54161:63:120;;-1:-1:-1;54128:2:120;54161:29;:63;:::i;:::-;54140:84;;54242:37;54282:7;;;;;;;;;-1:-1:-1;;;;;54282:7:120;-1:-1:-1;;;;;54282:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;54323:7;;54242:70;;-1:-1:-1;54323:7:120;;;-1:-1:-1;;;;;54323:7:120;:22;54346:70;3387:3:30;54346:31:120;:70;:::i;:::-;54323:94;;-1:-1:-1;;;;;;54323:94:120;;;;;;;16774:8:188;16762:21;;;54323:94:120;;;16744:40:188;16717:18;;54323:94:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;54433:8:120;;-1:-1:-1;;;54428:367:120;696:1:21;54447::120;:40;;;54428:367;;;54508:7;;:61;;-1:-1:-1;;;54508:61:120;;23056:8:188;23044:21;;54508:61:120;;;23026:40:188;464:2:176;23082:18:188;;;23075:49;54508:7:120;;;;-1:-1:-1;;;;;54508:7:120;;:28;;22999:18:188;;54508:61:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;54590:7;;;;;;;;;-1:-1:-1;;;;;54590:7:120;-1:-1:-1;;;;;54590:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;54584:36;;54635:96;54644:3;:17;;;54635:96;;696:1:21;54664::120;54668;54664:5;;;;:::i;:::-;54663:46;;;;;;:::i;:::-;54635:96;;;;;;;;;;;;;-1:-1:-1;;;54635:96:120;;;:8;:96::i;:::-;54745:7;;:39;;-1:-1:-1;;;54745:39:120;;464:2:176;54745:39:120;;;9771:40:188;54745:7:120;;;;-1:-1:-1;;;;;54745:7:120;;:19;;9744:18:188;;54745:39:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;54489:3;;;;;:::i;:::-;;;;54428:367;;21243:696;21341:2;21303:35;21385:67;21341:2;3387:3:30;21385:67:120;:::i;:::-;21353:99;;2941:1:30;21528:22:120;464:2:176;21472:99:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;21462:7:120;:109;;-1:-1:-1;;;;;21462:109:120;;;;;;-1:-1:-1;;;;;;21462:109:120;;;;;;;;;-1:-1:-1;21605:75:120;1478:40;21462:7;696:1:21;1478:40:120;:::i;21605:75::-;21756:7;;:56;;-1:-1:-1;;;21756:56:120;;;;;7539:25:188;;;1234:6:26;7580:18:188;;;7573:49;;;21582:98:120;;-1:-1:-1;21756:7:120;;;;-1:-1:-1;;;;;21756:7:120;;:28;;7512:18:188;;21756:56:120;7369:259:188;38538:1438:120;38607:62;;;;;;;;;38649:2;38607:62;;38653:2;38607:62;;;;;;;;38657:2;38607:62;;;;38661:2;38607:62;;;;;;;;38665:3;38607:62;;;;;;;;38679:63;;;;;;;38728:1;38679:63;;;;;;;;;;38734:1;38679:63;;;;;;;;;;;;;;;;;38607:62;:38;38753:1217;38775:29;38771:1;:33;;;38753:1217;;;38825:28;38856:22;38879:1;38856:25;;;;;;;;;:::i;:::-;;;;;38825:56;;;;38895:35;38933:29;38963:1;38933:32;;;;;;;;;:::i;:::-;;;;;38895:70;;;-1:-1:-1;39031:162:120;39099:21;39122:52;39099:21;38895:70;39122:52;:::i;:::-;:57;;39177:2;39122:57;:::i;:::-;39031:50;:162::i;:::-;39207:169;39282:21;39305:52;39282:21;39305:28;:52;:::i;:::-;:57;;39360:2;39305:57;:::i;:::-;39207;:169::i;:::-;39390:182;39478:21;39501:52;39478:21;39501:28;:52;:::i;:::-;:57;;39556:2;39501:57;:::i;:::-;39390:70;:182::i;:::-;39586:176;39668:21;39691:52;39668:21;39691:28;:52;:::i;:::-;:57;;39746:2;39691:57;:::i;:::-;39586:64;:176::i;:::-;39776:183;39865:21;39888:52;39865:21;39888:28;:52;:::i;:::-;:57;;39943:2;39888:57;:::i;:::-;39776:71;:183::i;:::-;38811:1159;;38806:3;;;;;:::i;:::-;;;;38753:1217;;2459:141:100;2508:34;2575:18;2554:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1243:204:96;1302:7;;1282:4;;1302:7;;1298:143;;;-1:-1:-1;1332:7:96;;;;;1243:204::o;1298:143::-;1377:39;;-1:-1:-1;;;1377:39:96;;-1:-1:-1;;;;;;;;;;;1377:39:96;;;23844:51:188;;;-1:-1:-1;;;23911:18:188;;;23904:34;1428:1:96;;1377:7;;23817:18:188;;1377:39:96;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;1370:60;;1243:204;:::o;3722:809:120:-;3779:139;:24;2941:1:30;3387:3;3779:52:120;:139::i;:::-;3928:80;3986:21;4006:1;1234:6:26;3986:21:120;:::i;:::-;3928:24;;:57;:80::i;:::-;4018:23;;4182:54;637:2:21;4182:16:120;:54;:::i;:::-;4140:97;-1:-1:-1;4253:9:120;4248:277;4272:4;4268:1;:8;4248:277;;;-1:-1:-1;;;;;;;;;;;4297:7:120;4305:43;;;;:15;:43;:::i;:::-;4297:52;;;;;;;;;;;;;16132:25:188;;16120:2;16105:18;;15978:185;4297:52:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4382:35;26839:21:21;:15;:21;;26678:200;4382:35:120;4363:54;-1:-1:-1;4431:83:120;4474:21;4494:1;1234:6:26;4474:21:120;:::i;:::-;4431:24;;4497:16;4431:42;:83::i;:::-;-1:-1:-1;4278:3:120;;4248:277;;35070:1117;2941:1:30;3387:3;464:2:176;35156:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;35146:7:120;:145;;-1:-1:-1;;;;;;35146:145:120;;-1:-1:-1;;;;;35146:145:120;;;;;;;;;;;;;35326:29;;-1:-1:-1;;;35326:29:120;;-1:-1:-1;35326:29:120;;;16132:25:188;35326:7:120;;;;;;:26;;16105:18:188;;35326:29:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;35365:7:120;;:51;;-1:-1:-1;;;35365:51:120;;:7;:51;;;7539:25:188;464:2:176;7580:18:188;;;7573:49;35365:7:120;;;;-1:-1:-1;;;;;35365:7:120;;:28;;7512:18:188;;35365:51:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;35427:37;35467:7;;;;;;;;;-1:-1:-1;;;;;35467:7:120;-1:-1:-1;;;;;35467:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;35508:7;;:56;;-1:-1:-1;;;35508:56:120;;35537:6;35508:56;;;7539:25:188;464:2:176;7580:18:188;;;7573:49;35427:70:120;;-1:-1:-1;35508:7:120;;;-1:-1:-1;;;;;35508:7:120;;:28;;7512:18:188;;35508:56:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;35581:7;;;;;;;;;-1:-1:-1;;;;;35581:7:120;-1:-1:-1;;;;;35581:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;35708:7;;:46;;-1:-1:-1;;;35708:46:120;;35749:1;35708:46;;;7987:42:188;;;8045:18;;;8038:51;35575:36:120;;-1:-1:-1;35708:7:120;;;-1:-1:-1;;;;;35708:7:120;;:40;;7960:18:188;;35708:46:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;35782:3:120;-1:-1:-1;;;;;;;;;;;35796:7:120;35804:21;35782:3;35804:12;:21;:::i;:::-;35796:30;;;;;;;;;;;;;16132:25:188;;16120:2;16105:18;;15978:185;35796:30:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;35883:7:120;;:56;;-1:-1:-1;;;35883:56:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;35883:7:120;;;;-1:-1:-1;;;;;35883:7:120;;-1:-1:-1;35883:28:120;;-1:-1:-1;7512:18:188;;35883:56:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;35975:7:120;;:46;;-1:-1:-1;;;35975:46:120;;35950:22;35975:46;;;7987:42:188;;;8045:18;;;8038:51;;;35950:22:120;-1:-1:-1;35975:7:120;;;;-1:-1:-1;;;;;35975:7:120;;-1:-1:-1;35975:40:120;;7960:18:188;;35975:46:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;35950:71;;36085:95;36094:16;36085:95;;464:2:176;36085:95:120;;;;;;;;;;;;;;;;;;;:8;:95::i;24788:1060::-;24852:29;24884:40;3387:3:30;24884:1:120;:40;:::i;:::-;24852:72;;2941:1:30;25000:22:120;464:2:176;24944:99:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;24934:7:120;:109;;-1:-1:-1;;;;;24934:109:120;;;;;;-1:-1:-1;;;;;;24934:109:120;;;;;;;;;-1:-1:-1;25082:75:120;1478:40;24934:7;696:1:21;1478:40:120;:::i;25082:75::-;25225:7;;:67;;-1:-1:-1;;;25225:67:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;25054:103:120;;-1:-1:-1;25213:1:120;;25225:7;;;;-1:-1:-1;;;;;25225:7:120;;:28;;7512:18:188;;25225:67:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;25359:7:120;;:70;;-1:-1:-1;;;25359:70:120;;;;;7539:25:188;;;-1:-1:-1;;7580:18:188;;;7573:49;;;1176:7:26;-1:-1:-1;25359:7:120;;;;-1:-1:-1;;;;;25359:7:120;;-1:-1:-1;25359:28:120;;7512:18:188;;25359:70:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;25440:7:120;;;;;-1:-1:-1;;;;;25440:7:120;;-1:-1:-1;25440:21:120;;-1:-1:-1;25462:19:120;25440:7;25462:15;:19;:::i;:::-;25440:66;;-1:-1:-1;;;;;;25440:66:120;;;;;;;;;;9535:25:188;;;;9608:8;9596:21;;9576:18;;;9569:49;9508:18;;25440:66:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;25517:17;3542:2:30;464::176;25537:42:120;;;;:::i;8118:1722::-;2941:1:30;3387:3;464:2:176;8199:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;8189:7:120;:145;;-1:-1:-1;;;;;;8189:145:120;;-1:-1:-1;;;;;8189:145:120;;;;;;;;;;;;;8344:76;;-1:-1:-1;;;8344:76:120;;513:2:176;8344:76:120;;;15871:38:188;464:2:176;15925:18:188;;;15918:49;8344:7:120;;;;;;:28;;15844:18:188;;8344:76:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8431:37;8471:7;;;;;;;;;-1:-1:-1;;;;;8471:7:120;-1:-1:-1;;;;;8471:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;8431:70;-1:-1:-1;8511:27:120;8575:19;8593:1;8575:15;:19;:::i;:::-;8541:55;;464:2:176;8541:55:120;:::i;:::-;8511:85;;8640:87;8649:3;:16;;;8640:87;;513:2:176;8696:1:120;8667:30;;;;:::i;:::-;8640:87;;;;;;;;;;;;;;;;;;;:8;:87::i;:::-;8759:24;;;;8737:174;;513:2:176;8759:52:120;;;;8737:174;;8825:21;8737:174;;;;;;;;;;;;;;;;;;;:8;:174::i;:::-;8943:23;;;;8921:179;;513:2:176;8943:51:120;;;;;8921:179;;9008:15;8921:179;;;;;;;;;;;;;;;;;:8;:179::i;:::-;9145:59;9154:3;:17;;;9145:59;;9173:1;9145:59;;;;;;;;;;;;;-1:-1:-1;;;9145:59:120;;;:8;:59::i;:::-;9223:25;;;;:28;9214:70;;;;;;;;;;;;;9223:28;9214:70;;;;;;;;9249:1;;9214:8;:70::i;:::-;9316:25;;;;9294:140;;9342:1;9316:28;;;;9294:140;;9346:21;9294:140;;;;;;;;;;;;;;;;;;;:8;:140::i;:::-;9453:25;;;;9444:70;;9479:1;9453:28;;;;9444:70;;9483:1;9444:70;;;;;;;;;;;;;;;;;:8;:70::i;:::-;9533:24;;;;:27;9524:100;;;;;;;;;;;;;;;;;;9562:1;;9524:100;9533:27;9524:100;;;:8;:100::i;:::-;9643:24;;;;9634:109;;9668:1;9643:27;;;;9634:109;;9672:15;9634:109;;;;;;;;;;;;;;;;;:8;:109::i;:::-;9762:24;;;;9753:80;;9787:1;9762:27;;;;;9753:80;;9791:1;9753:80;;;;;;;;;;;;;;;;;:8;:80::i;11419:1538::-;2941:1:30;3387:3;464:2:176;11507:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;11497:7:120;:145;;-1:-1:-1;;;;;;11497:145:120;;-1:-1:-1;;;;;11497:145:120;;;;;;;;;;;;;11652:7;;;:28;1478:40;-1:-1:-1;696:1:21;1478:40:120;:::i;:::-;1577:55;;513:2:176;1577:55:120;:::i;:::-;11652:74;;-1:-1:-1;;;;;;11652:74:120;;;;;;;;;;7539:25:188;;;;464:2:176;7580:18:188;;;7573:49;7512:18;;11652:74:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;11737:37;11777:7;;;;;;;;;-1:-1:-1;;;;;11777:7:120;-1:-1:-1;;;;;11777:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;11737:70;-1:-1:-1;11817:27:120;11881:19;11899:1;11881:15;:19;:::i;:::-;11847:55;;464:2:176;11847:55:120;:::i;:::-;11817:85;;11947:59;11956:3;:17;;;11947:59;;11975:1;11947:59;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;11947:59:120;;;:8;:59::i;:::-;12038:25;;;;12016:188;;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;12038:53;;;;;;;:::i;:::-;;;;;12016:188;;12105:21;12016:188;;;;;;;;;;;;;;;;;;;:8;:188::i;:::-;12236:24;;;;12214:178;;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;12236:52;;;;;;;:::i;:::-;;;;;12214:178;;12302:15;12214:178;;;;;;;;;;;;;;;;;:8;:178::i;:::-;12403:7;;:76;;-1:-1:-1;;;12403:76:120;;513:2:176;12403:76:120;;;15871:38:188;464:2:176;15925:18:188;;;15918:49;12403:7:120;;;;-1:-1:-1;;;;;12403:7:120;;:28;;15844:18:188;;12403:76:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;12496:7;;;;;;;;;-1:-1:-1;;;;;12496:7:120;-1:-1:-1;;;;;12496:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;12490:36;-1:-1:-1;12594:19:120;12612:1;12594:15;:19;:::i;:::-;12560:55;;464:2:176;12560:55:120;:::i;:::-;12536:79;;12626:59;12635:3;:17;;;12626:59;;12654:1;12626:59;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;12626:59:120;;;:8;:59::i;:::-;12717:25;;;;12695:137;;12743:1;12717:28;;;;12695:137;;12747:21;12695:137;;;;;;;;;;;;;;;;;;;:8;:137::i;:::-;12851:24;;;;:27;12842:108;;;;;;;;;;;;;;;;;;12880:15;;12842:108;12851:27;12842:108;;;:8;:108::i;2606:142:100:-;2655:35;2723:18;2702:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2702:39:100;;;;;;;;;;;;;;;;;;;;;;2606:142;:::o;40465:386:120:-;40545:10;40558:21;-1:-1:-1;;40578:1:120;40558:21;:::i;:::-;40545:34;-1:-1:-1;40601:1:120;40589:9;;;40670:60;40545:34;40601:1;40589:9;;40728:1;40670:34;:60::i;:::-;40637:93;;;;40740:47;40749:7;40740:47;;-1:-1:-1;;40740:47:120;;;;;;;;;;;;;;;-1:-1:-1;;;40740:47:120;;;:8;:47::i;:::-;40797;40806:7;40797:47;;1234:6:26;40797:47:120;;;;;;;;;;;;;;;-1:-1:-1;;;40797:47:120;;;:8;:47::i;18010:932::-;2941:1:30;3387:3;464:2:176;18093:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;18083:7:120;:145;;-1:-1:-1;;;;;;18083:145:120;;-1:-1:-1;;;;;18083:145:120;;;;;;;;;;;;;18238:7;;;:28;1378:39;-1:-1:-1;637:2:21;1378:39:120;:::i;:::-;18238:75;;-1:-1:-1;;;;;;18238:75:120;;;;;;;;;;7539:25:188;;;;464:2:176;7580:18:188;;;7573:49;7512:18;;18238:75:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;18348:7:120;;:29;;-1:-1:-1;;;18348:29:120;;:7;:29;;;16132:25:188;18348:7:120;;;;-1:-1:-1;;;;;18348:7:120;;-1:-1:-1;18348:26:120;;-1:-1:-1;16105:18:188;;18348:29:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;18387:7:120;;:51;;-1:-1:-1;;;18387:51:120;;:7;:51;;;7539:25:188;464:2:176;7580:18:188;;;7573:49;18387:7:120;;;;-1:-1:-1;;;;;18387:7:120;;:28;;7512:18:188;;18387:51:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;18449:37;18489:7;;;;;;;;;-1:-1:-1;;;;;18489:7:120;-1:-1:-1;;;;;18489:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;18449:70;-1:-1:-1;18529:27:120;18593:19;18611:1;18593:15;:19;:::i;:::-;18559:55;;464:2:176;18559:55:120;:::i;:::-;18529:85;;18658:57;18667:3;:16;;;18658:57;;18685:1;18658:57;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;18658:57:120;;;:8;:57::i;:::-;18734:24;;;;18725:104;;18759:1;18734:27;;;;18725:104;;18763:21;18725:104;;;;;;;;;;;;;;;;;;;:8;:104::i;:::-;18848:23;;;;:26;18839:96;;;;;;;;;;;;;;;;;;18876:15;;18839:96;18848:26;18839:96;;;:8;:96::i;9846:1567::-;2941:1:30;3387:3;464:2:176;9933:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;9923:7:120;:145;;-1:-1:-1;;;;;;9923:145:120;;-1:-1:-1;;;;;9923:145:120;;;;;;;;;;;;;10078:7;;;:28;1378:39;-1:-1:-1;637:2:21;1378:39:120;:::i;:::-;10078:75;;-1:-1:-1;;;;;;10078:75:120;;;;;;;;;;7539:25:188;;;;464:2:176;7580:18:188;;;7573:49;7512:18;;10078:75:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;10164:37;10204:7;;;;;;;;;-1:-1:-1;;;;;10204:7:120;-1:-1:-1;;;;;10204:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;10164:70;-1:-1:-1;10244:27:120;10308:19;10326:1;10308:15;:19;:::i;:::-;10274:55;;464:2:176;10274:55:120;:::i;:::-;10244:85;;10373:57;10382:3;:16;;;10373:57;;10400:1;10373:57;;;;;;;;;;;;;-1:-1:-1;;;10373:57:120;;;:8;:57::i;:::-;10462:24;;;;10440:176;;1378:39;1416:1;637:2:21;1378:39:120;:::i;:::-;10462:51;;;;;;;:::i;:::-;;;;;10440:176;;10527:21;10440:176;;;;;;;;;;;;;;;;;;;:8;:176::i;:::-;10648:23;;;;10626:178;;1378:39;1416:1;637:2:21;1378:39:120;:::i;:::-;10648:50;;;;;;;:::i;10626:178::-;10878:7;;:51;;-1:-1:-1;;;10878:51:120;;:7;:51;;;7539:25:188;464:2:176;7580:18:188;;;7573:49;10878:7:120;;;;-1:-1:-1;;;;;10878:7:120;;:28;;7512:18:188;;10878:51:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;10946:7;;;;;;;;;-1:-1:-1;;;;;10946:7:120;-1:-1:-1;;;;;10946:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;10940:36;-1:-1:-1;11044:19:120;11062:1;11044:15;:19;:::i;:::-;11010:55;;464:2:176;11010:55:120;:::i;:::-;10986:79;;11109:57;11118:3;:16;;;11109:57;;11136:1;11109:57;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;11109:57:120;;;:8;:57::i;:::-;11185:24;;;;11176:112;;11210:1;11185:27;;;;11176:112;;11214:21;11176:112;;;;;;;;;;;;;;;;;;;:8;:112::i;:::-;11307:23;;;;11298:108;;11331:1;11307:26;;44901:1281;45026:1;45010:13;45050:21;45026:1;1234:6:26;45050:21:120;:::i;:::-;45037:34;-1:-1:-1;45081:10:120;45130:302;;;;;;-1:-1:-1;;1176:7:26;1478:40:120;;696:1:21;1478:40:120;:::i;45130:302::-;45105:337;-1:-1:-1;45452:12:120;45474:10;45105:337;45474:4;:10;:::i;:::-;45452:33;-1:-1:-1;45624:12:120;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;752:3:21;45711:35:120;;;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;45711:35;;;;:::i;:::-;45710:74;;;;:::i;:::-;:127;;;;:::i;:::-;45682:155;;;;;;:::i;:::-;45624:237;;45873:13;45888;45905:65;45940:4;45946:3;45951:4;45957;45963:6;45905:65;;:34;:65::i;46930:734::-;47008:1;46992:666;47015:1;47011;:5;;;46992:666;;;47037:46;47081:1;47037:43;:46::i;:::-;47097;47141:1;47097:43;:46::i;:::-;47157;47201:1;47157:43;:46::i;:::-;47217;47261:1;47217:43;:46::i;:::-;47277;47321:1;47277:43;:46::i;:::-;47337;47381:1;47337:43;:46::i;:::-;47397:51;47446:1;47397:48;:51::i;:::-;47462:52;47512:1;47462:49;:52::i;:::-;47528;47578:1;47528:49;:52::i;:::-;47594:53;47645:1;47594:50;:53::i;:::-;47018:3;;46992:666;;2757:538;2827:139;:24;2941:1:30;3387:3;2827:52:120;:139::i;:::-;2976:61;;-1:-1:-1;;;2976:61:120;;-1:-1:-1;;;;;;;;;;;2976:15:120;;;:61;;-1:-1:-1;;;2992:44:120;2976:61;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3047:104;3084:24;3149:1;3387:3:30;3110:40:120;;;;:::i;:::-;3047:36;:104::i;:::-;3188:100;3225:24;3387:3:30;3188:36:120;:100::i;25854:1138::-;25935:36;3387:3:30;25935:75:120;;2941:1:30;3387:3;464:2:176;26030:135:120;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;26020:7:120;:145;;-1:-1:-1;;;;;26020:145:120;;;;;;-1:-1:-1;;;;;;26020:145:120;;;;;;;;;26214:1;-1:-1:-1;26020:7:120;26254:68;696:1:21;26254:68:120;;;;:::i;:::-;:72;;;;:::i;:::-;26226:100;-1:-1:-1;26359:18:120;26380:60;26412:28;26380:29;:60;:::i;:::-;26359:81;-1:-1:-1;26489:26:120;26518:50;696:1:21;26518:50:120;;;;:::i;:::-;26602:7;;:67;;-1:-1:-1;;;26602:67:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;26489:79:120;;-1:-1:-1;26602:7:120;;;-1:-1:-1;;;;;26602:7:120;;:28;;7512:18:188;;26602:67:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;26679:7:120;;:35;;-1:-1:-1;;;26679:35:120;;16774:8:188;16762:21;;26679:35:120;;;16744:40:188;26679:7:120;;;;-1:-1:-1;;;;;26679:7:120;;-1:-1:-1;26679:22:120;;-1:-1:-1;16717:18:188;;26679:35:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;26724:7:120;;:68;;-1:-1:-1;;;26724:68:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;26724:7:120;;;;-1:-1:-1;;;;;26724:7:120;;-1:-1:-1;26724:28:120;;-1:-1:-1;7512:18:188;;26724:68:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;26803:37;26843:7;;;;;;;;;-1:-1:-1;;;;;26843:7:120;-1:-1:-1;;;;;26843:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;26803:70;;26884:51;26893:3;:17;;;26884:51;;26912:1;26884:51;;;;;;;;;;;;;-1:-1:-1;;;26884:51:120;;;:8;:51::i;:::-;26946:7;;:39;;-1:-1:-1;;;26946:39:120;;464:2:176;26946:39:120;;;9771:40:188;26946:7:120;;;;-1:-1:-1;;;;;26946:7:120;;:19;;9744:18:188;;26946:39:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3301:242;3365:61;;-1:-1:-1;;;3365:61:120;;-1:-1:-1;;;;;;;;;;;3365:15:120;;;:61;;-1:-1:-1;;;3381:44:120;3365:61;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3436:100;3473:24;3387:3:30;3436:36:120;:100::i;41367:901::-;41494:1;41478:13;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;752:3:21;41532:44:120;;;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;41532:44;;;;:::i;:::-;41525:109;;;;:::i;:::-;:146;;;;:::i;:::-;41505:166;-1:-1:-1;41681:10:120;41694:21;41714:1;-1:-1:-1;;41694:21:120;:::i;:::-;41681:34;-1:-1:-1;41725:9:120;41737:8;41681:34;41744:1;41737:8;:::i;:::-;41725:20;-1:-1:-1;41768:73:120;;;;;41811:10;-1:-1:-1;;41780:8:120;:4;41787:1;41780:8;:::i;41768:73::-;41755:87;-1:-1:-1;41852:12:120;41874:11;41881:4;41755:87;41874:11;:::i;:::-;41852:34;-1:-1:-1;41896:12:120;41917:25;41932:10;41917:25;;;;;:::i;:::-;41896:47;;41955:13;41970;41987:65;42022:4;42028:3;42033:4;42039;42045:6;41987:65;;:34;:65::i;2980:132:96:-;3076:29;;-1:-1:-1;;;3076:29:96;;-1:-1:-1;;;;;;;;;;;3076:11:96;;;:29;;3088:4;;3094:5;;3101:3;;3076:29;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2980:132;;;:::o;6215:704:89:-;6277:7;6300:1;6305;6300:6;6296:150;;6400:35;1035:4:79;6400:11:89;:35::i;:::-;6896:1;6891;6887;:5;6886:11;;;;;:::i;:::-;;6900:1;6886:15;6876:5;;;6860:42;6853:49;;6215:704;;;;;:::o;4643:571:21:-;4815:54;;-1:-1:-1;;4815:54:21;-1:-1:-1;;;4815:54:21;;;4897:25;;;;;:125;;-1:-1:-1;4966:56:21;;;;1418:2;4966:56;:::i;:::-;4942:21;:80;;;4897:125;4880:208;;;5054:23;;-1:-1:-1;;;5054:23:21;;;;;;;;;;;4880:208;5097:49;;-1:-1:-1;;5156:51:21;-1:-1:-1;;;5097:49:21;;;;;-1:-1:-1;;;;5156:51:21;;-1:-1:-1;;;5156:51:21;;;;;;;;4643:571::o;2386:134:96:-;2484:29;;-1:-1:-1;;;2484:29:96;;-1:-1:-1;;;;;;;;;;;2484:11:96;;;:29;;2496:4;;2502:5;;2509:3;;2484:29;;;:::i;14247:1265:21:-;14437:13;14452;14498:69;14517:12;14531:11;14544:9;14555:11;14498:18;:69::i;:::-;14477:90;;-1:-1:-1;14477:90:21;-1:-1:-1;14771:25:21;;;;:15;;;;:25;14747:13;14880:154;752:3;901:26;14942:35;;;;14747:13;14880:14;:154::i;:::-;14852:5;:182;14812:236;;-1:-1:-1;;15256:25:21;;15247:6;15234:9;15227:17;;:26;:54;:102;;-1:-1:-1;;15227:102:21;;;15302:6;15290:9;:18;;;15227:102;15201:128;-1:-1:-1;1234:6:26;15386:25:21;15353:17;;;:26;;:30;:58;:142;;1234:6:26;15353:142:21;;;15448:6;15436:9;:18;;;15457:1;15436:22;15353:142;15343:152;;14676:830;;14247:1265;;;;;;;;:::o;75479:479:120:-;75603:12;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;75756:6;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;75727:35;;;;:::i;:::-;75689:74;;752:3:21;75689:74:120;:::i;:::-;:127;;;;:::i;:::-;75661:155;;;;;;:::i;:::-;75603:237;;75851:43;75860:7;75851:43;;75876:6;75869:4;:13;;;;;;:::i;:::-;75851:43;;;;;;;;;;;;;;;-1:-1:-1;;;75851:43:120;;;:8;:43::i;:::-;75904:47;75913:7;75904:47;;75929:6;75922:4;:13;;;;;;:::i;:::-;:17;;75938:1;75922:17;:::i;:::-;75904:47;;;;;;;;;;;;;;;-1:-1:-1;;;75904:47:120;;;:8;:47::i;4171:208:106:-;4251:13;4285:19;4292:1;4295:3;4300;4285:6;:19::i;:::-;4314:58;;;;;;;;;;;-1:-1:-1;;;4314:58:106;;;;4352:19;;-1:-1:-1;;;4352:19:106;;;;;16132:25:188;;;4276:28:106;;-1:-1:-1;4314:58:106;;-1:-1:-1;;;;;;;;;;;4352:11:106;;;16105:18:188;;4352:19:106;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;4352:19:106;;;;;;;;;;;;:::i;:::-;4314:21;:58::i;:::-;4171:208;;;;;:::o;2866:108:96:-;2943:24;;-1:-1:-1;;;2943:24:96;;;;;26644:25:188;;;26685:18;;;26678:34;;;-1:-1:-1;;;;;;;;;;;2943:11:96;;;26617:18:188;;2943:24:96;26474:244:188;11189:1141:180;11322:13;11337;11368:471;11409:9;11394:12;:24;:439;;11679:9;11665:11;:23;11664:169;;11795:11;11783:9;:23;:49;;11821:11;11368:16;:471::i;11783:49::-;11809:9;11368:16;:471::i;11664:169::-;11723:11;11709;:25;:53;;11751:11;11368:16;:471::i;11709:53::-;11737:11;11368:16;:471::i;11394:439::-;11465:12;11451:11;:26;:184;;11594:11;11579:12;:26;:55;;11623:11;11368:16;:471::i;11579:55::-;11608:12;11368:16;:471::i;:::-;11358:481;;11856:471;11897:9;11882:12;:24;:439;;12167:9;12153:11;:23;12152:169;;12283:11;12271:9;:23;:49;;12309:11;11368:16;:471::i;12152:169::-;12211:11;12197;:25;:53;;12239:11;11368:16;:471::i;11882:439::-;11953:12;11939:11;:26;:184;;12082:11;12067:12;:26;:55;;12111:11;11368:16;:471::i;11856:::-;11846:481;;11189:1141;;;;;;;:::o;17342:648:21:-;17528:5;17721:22;17745:26;17787:90;17807:4;17813:1;17816:22;17840:29;17871:5;17787:19;:90::i;:::-;17720:157;;;;17888:61;17904:4;17910:16;17928:20;17888:15;:61::i;:::-;-1:-1:-1;17967:16:21;17342:648;-1:-1:-1;;;;17342:648:21:o;60892:454:120:-;61012:17;61031:20;61063:24;61138:7;61090:55;;1416:1;637:2:21;1378:39:120;;;;:::i;:::-;61090:9;:45;;;;;;:::i;:::-;:55;;;;:::i;:::-;61063:82;-1:-1:-1;61155:34:120;;;;61222:35;61155:34;61063:82;61222:35;:::i;:::-;61199:59;-1:-1:-1;1378:39:120;1416:1;637:2:21;1378:39:120;:::i;:::-;61288:14;:50;;;;;;:::i;:::-;61268:71;;61053:293;;60892:454;;;;;:::o;61418:1588::-;61585:29;61617:15;61585:47;;61681:21;61704:28;464:2:176;61652:101:120;;;;;:::i;:::-;17235:8:188;17223:21;;;17205:40;;17281:21;;;;17276:2;17261:18;;17254:49;17350:1;17339:21;17334:2;17319:18;;17312:49;17193:2;17178:18;61652:101:120;;;;;;;;;;;;;;;;;;;;;;;61642:7;;:111;;;;;-1:-1:-1;;;;;61642:111:120;;;;;-1:-1:-1;;;;;61642:111:120;;;;;;61763:37;61803:7;;;;;;;;;-1:-1:-1;;;;;61803:7:120;-1:-1:-1;;;;;61803:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;61763:70;;61887:83;61896:3;:25;;;61887:83;;61923:21;61887:83;;;;;;;;;;;;;;;-1:-1:-1;;;61887:83:120;;;:8;:83::i;:::-;62035:57;62044:3;:16;;;62035:57;;62062:1;62035:57;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;62035:57:120;;;:8;:57::i;:::-;62111:24;;;;62102:65;;62136:1;62111:27;;62102:65;62186:23;;;;62177:86;;62210:1;62186:26;;;;;62177:86;;62214:21;62177:86;;;;;;;;;;;;;-1:-1:-1;;;62177:86:120;;;:8;:86::i;:::-;62274:7;;;;;-1:-1:-1;;;;;62274:7:120;:28;62303:26;62327:2;62303:21;:26;:::i;:::-;62274:76;;-1:-1:-1;;;;;;62274:76:120;;;;;;;23056:8:188;23044:21;;;62274:76:120;;;23026:40:188;464:2:176;23082:18:188;;;23075:49;22999:18;;62274:76:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;62383:7;;;;;;;;;-1:-1:-1;;;;;62383:7:120;-1:-1:-1;;;;;62383:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;62377:36;;62491:57;62500:3;:16;;;62491:57;;62518:1;62491:57;;;;;;;;;;;;;-1:-1:-1;;;62491:57:120;;;:8;:57::i;:::-;62567:24;;;;62558:65;;62592:1;62567:27;;62558:65;62655:24;;;;:27;;;62633:179;;;;62696:49;464:2:176;62702:21:120;62696:49;:::i;:::-;62633:179;;;;;;;;;;;;;;;;;;;:8;:179::i;:::-;62831:23;;;;62822:86;;62855:1;62831:26;;62822:86;62927:23;;;;62918:81;;62951:1;62927:26;;;;62918:81;;62955:15;62918:81;;;;;;;;;;;;;;;;;:8;:81::i;63012:2381::-;63186:29;63218:15;63186:47;;63282:21;63305:28;464:2:176;63253:101:120;;;;;:::i;:::-;17235:8:188;17223:21;;;17205:40;;17281:21;;;;17276:2;17261:18;;17254:49;17350:1;17339:21;17334:2;17319:18;;17312:49;17193:2;17178:18;63253:101:120;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;63243:7:120;:111;;-1:-1:-1;;;;;;63243:111:120;;-1:-1:-1;;;;;63243:111:120;;;;;;;;;;;;;-1:-1:-1;;63486:7:120;;;:26;63513;63537:2;63513:21;:26;:::i;:::-;63486:54;;-1:-1:-1;;;;;;63486:54:120;;;;;;;16774:8:188;16762:21;;;63486:54:120;;;16744:40:188;16717:18;;63486:54:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;63567:7:120;;:62;;-1:-1:-1;;;63567:62:120;;23056:8:188;23044:21;;63567:62:120;;;23026:40:188;464:2:176;23082:18:188;;;23075:49;63567:7:120;;;;-1:-1:-1;;;;;63567:7:120;;:28;;22999:18:188;;63567:62:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;63640:37;63680:7;;;;;;;;;-1:-1:-1;;;;;63680:7:120;-1:-1:-1;;;;;63680:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;63640:70;;63754:57;63763:3;:16;;;63754:57;;63781:1;63754:57;;;;;;;;;;;;;-1:-1:-1;;;63754:57:120;;;:8;:57::i;:::-;63830:24;;;;63821:65;;63855:1;63830:27;;63821:65;63918:24;;;;:27;;;63896:227;;;;464:2:176;63965:26:120;:21;63989:2;63965:26;:::i;:::-;63959:54;;;;:::i;:::-;63896:227;;;;;;;;;;;;;;;;;;;:8;:227::i;:::-;64142:23;;;;:26;64133:109;;;;;;;;;;;;;;;;;;64170:21;;64133:109;64142:26;64133:109;;;:8;:109::i;:::-;64261:24;;;;:27;64252:111;;;;;;;;;;;;;;;;;;64290:21;;64252:111;64261:27;64252:111;;;:8;:111::i;:::-;64518:7;;64405:15;;64518:7;;;-1:-1:-1;;;;;64518:7:120;:26;64545;64569:2;64545:21;:26;:::i;:::-;64518:54;;-1:-1:-1;;;;;;64518:54:120;;;;;;;16774:8:188;16762:21;;;64518:54:120;;;16744:40:188;16717:18;;64518:54:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;64599:7:120;;:62;;-1:-1:-1;;;64599:62:120;;23056:8:188;23044:21;;64599:62:120;;;23026:40:188;464:2:176;23082:18:188;;;23075:49;64599:7:120;;;;-1:-1:-1;;;;;64599:7:120;;:28;;22999:18:188;;64599:62:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;64677:7;;;;;;;;;-1:-1:-1;;;;;64677:7:120;-1:-1:-1;;;;;64677:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;64671:36;;64751:57;64760:3;:16;;;64751:57;;64778:1;64751:57;;;;;;;;;;;;;;;;;:8;:57::i;:::-;64840:24;;;;:27;;;64818:248;;;;464:2:176;64892:26:120;:21;64916:2;64892:26;:::i;:::-;64887:32;;:1;:32;:::i;:::-;64881:60;;;;:::i;:::-;64818:248;;;;;;;;;;;;;;;;;;;:8;:248::i;:::-;65085:23;;;;65076:109;;65109:1;65085:26;;;;65076:109;;65113:20;65076:109;;;;;;;;;;;;;;;;;:8;:109::i;:::-;65217:24;;;;:27;65195:191;;;;;;;;;;;;;;;;;;65258:21;;65195:191;65217:27;65195:191;;;:8;:191::i;65399:1788::-;65586:29;1378:39;1416:1;637:2:21;1378:39:120;:::i;:::-;65619:26;65643:2;65619:21;:26;:::i;:::-;65618:56;;;;;;:::i;:::-;65586:88;-1:-1:-1;65701:23:120;1378:39;1416:1;637:2:21;1378:39:120;:::i;:::-;65754:57;;;;;;:::i;:::-;65727:85;;464:2:176;65727:85:120;:::i;:::-;65701:111;;65861:21;65884:28;464:2:176;65832:101:120;;;;;:::i;:::-;17235:8:188;17223:21;;;17205:40;;17281:21;;;;17276:2;17261:18;;17254:49;17350:1;17339:21;17334:2;17319:18;;17312:49;17193:2;17178:18;65832:101:120;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;65822:7:120;:111;;-1:-1:-1;;;;;;65822:111:120;;-1:-1:-1;;;;;65822:111:120;;;;;;;;;;;;;65943:71;;-1:-1:-1;;;65943:71:120;;;;;7539:25:188;;;464:2:176;7580:18:188;;;7573:49;65943:7:120;;;;;;:28;;7512:18:188;;65943:71:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;66025:37;66065:7;;;;;;;;;-1:-1:-1;;;;;66065:7:120;-1:-1:-1;;;;;66065:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;66025:70;;66139:57;66148:3;:16;;;66139:57;;66166:1;66139:57;;;;;;;;;;;;;-1:-1:-1;;;66139:57:120;;;:8;:57::i;:::-;66228:24;;;;66206:169;;1378:39;1416:1;637:2:21;1378:39:120;:::i;:::-;66228:51;;;;;;;:::i;:::-;;;;;66206:169;;66293:17;66206:169;;;;;;;;;;;;;;;;;;;:8;:169::i;:::-;66407:23;;;;66385:178;;1378:39;1416:1;637:2:21;1378:39:120;:::i;66385:178::-;66637:7;;;;;-1:-1:-1;;;;;66637:7:120;:28;66666:26;66690:2;66666:21;:26;:::i;:::-;66637:76;;-1:-1:-1;;;;;;66637:76:120;;;;;;;23056:8:188;23044:21;;;66637:76:120;;;23026:40:188;464:2:176;23082:18:188;;;23075:49;22999:18;;66637:76:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;66747:7;;;;;;;;;-1:-1:-1;;;;;66747:7:120;-1:-1:-1;;;;;66747:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;66741:36;;66788:57;66797:3;:16;;;66788:57;;66815:1;66788:57;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;66788:57:120;;;:8;:57::i;:::-;66877:24;;;;:27;66855:206;;;;66938:49;464:2:176;66944:21:120;66938:49;:::i;:::-;66918:69;;;;:17;:69;:::i;:::-;66855:206;;;;;;;;;;;;;;;;;;;:8;:206::i;:::-;67080:23;;;;67071:109;;67104:1;67080:26;;;;;67071:109;;67108:15;67071:109;;;;;;;;;;;;;;;;;:8;:109::i;67193:2058::-;67388:29;67420:15;67388:47;;67485:21;67508:22;464:2:176;67456:95:120;;;;;:::i;:::-;17235:8:188;17223:21;;;17205:40;;17281:21;;;;17276:2;17261:18;;17254:49;17350:1;17339:21;17334:2;17319:18;;17312:49;17193:2;17178:18;67456:95:120;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;67446:7:120;:105;;-1:-1:-1;;;;;;67446:105:120;;-1:-1:-1;;;;;67446:105:120;;;;;;;;;;;;;67561:7;;;:28;67590:27;67615:2;67590:22;:27;:::i;:::-;67561:77;;-1:-1:-1;;;;;;67561:77:120;;;;;;;23056:8:188;23044:21;;;67561:77:120;;;23026:40:188;464:2:176;23082:18:188;;;23075:49;22999:18;;67561:77:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;67666:37;67706:7;;;;;;;;;-1:-1:-1;;;;;67706:7:120;-1:-1:-1;;;;;67706:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;67666:70;-1:-1:-1;67747:26:120;67783:46;67808:21;67783:22;:46;:::i;:::-;67747:83;-1:-1:-1;67840:27:120;67870:50;67897:22;464:2:176;67870:50:120;:::i;:::-;67840:80;;67964:67;67973:3;:16;;;67964:67;;67991:19;68013:1;67991:23;;;;:::i;:::-;67964:67;;;;;;;;;;;;;;;-1:-1:-1;;;67964:67:120;;;:8;:67::i;:::-;68041:168;68063:3;:24;;;68088:19;68063:45;;;;;;;;;:::i;:::-;;;;;68041:168;;68122:21;68041:168;;;;;;;;;;;;;;;;;;;:8;:168::i;:::-;68219:184;68241:3;:23;;;68265:19;68241:44;;;;;;;;;:::i;:::-;;;;;68219:184;;68299:15;68219:184;;;;;;;;;;;;;;;;;:8;:184::i;:::-;68448:59;68457:3;:17;;;68448:59;;68476:1;68448:59;;;;;;;;;;;;;-1:-1:-1;;;68448:59:120;;;:8;:59::i;:::-;68526:25;;;;68517:76;;68552:1;68526:28;;68517:76;68625:25;;;;68603:139;;68651:1;68625:28;;;;68603:139;;68655:21;68603:139;;;;;;;;;;;;;;;;;;;:8;:139::i;:::-;68761:25;;;;68752:77;;68787:1;68761:28;;;;68752:77;;68791:1;68752:77;;;;;;;;;;;;;;;;;:8;:77::i;:::-;68861:24;;;;:27;68839:167;;;;;;;;;;;;;;;;;;68902:21;;68839:167;68861:27;68839:167;;;:8;:167::i;:::-;69038:24;;;;69016:138;;69063:1;69038:27;;;;69016:138;;69067:15;69016:138;;;;;;;;;;;;;;;;;:8;:138::i;:::-;69173:24;;;;69164:80;;69198:1;69173:27;;69257:1898;69459:30;69492:61;;;;696:1:21;69492:61:120;:::i;:::-;69459:94;-1:-1:-1;69563:25:120;69591:51;464:2:176;69459:94:120;69591:51;:::i;:::-;69563:79;;69692:21;69715:22;464:2:176;69663:95:120;;;;;:::i;:::-;17235:8:188;17223:21;;;17205:40;;17281:21;;;;17276:2;17261:18;;17254:49;17350:1;17339:21;17334:2;17319:18;;17312:49;17193:2;17178:18;69663:95:120;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;69653:7:120;:105;;-1:-1:-1;;;;;;69653:105:120;;-1:-1:-1;;;;;69653:105:120;;;;;;;;;;;;;69768:7;;;:28;69866:2;1478:40;-1:-1:-1;696:1:21;1478:40:120;:::i;:::-;69811:51;;;;;;:::i;:::-;69810:58;;;;:::i;:::-;69768:159;;-1:-1:-1;;;;;;69768:159:120;;;;;;;;;;7539:25:188;;;;464:2:176;7580:18:188;;;7573:49;7512:18;;69768:159:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;69938:37;69978:7;;;;;;;;;-1:-1:-1;;;;;69978:7:120;-1:-1:-1;;;;;69978:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;69938:70;;70053:59;70062:3;:17;;;70053:59;;70081:1;70053:59;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;70053:59:120;;;:8;:59::i;:::-;70144:25;;;;70122:262;;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;70144:53;;;;;;;:::i;:::-;;;;;70122:262;;70268:31;;;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;70239:60;;;;:::i;:::-;70211:89;;464:2:176;70211:89:120;:::i;:::-;70122:262;;;;;;;;;;;;;;;;;:8;:262::i;:::-;70416:24;;;;70394:180;;1478:40;1517:1;696::21;1478:40:120;:::i;:::-;70416:52;;;;;;;:::i;70394:180::-;70648:7;;;;;-1:-1:-1;;;;;70648:7:120;:28;70677:27;70702:2;70677:22;:27;:::i;:::-;70648:77;;-1:-1:-1;;;;;;70648:77:120;;;;;;;23056:8:188;23044:21;;;70648:77:120;;;23026:40:188;464:2:176;23082:18:188;;;23075:49;22999:18;;70648:77:120;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;70759:7;;;;;;;;;-1:-1:-1;;;;;70759:7:120;-1:-1:-1;;;;;70759:28:120;;:30;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;70753:36;;70799:59;70808:3;:17;;;70799:59;;70827:1;70799:59;;;;;;;;;;;;;-1:-1:-1;;;;;;;;;;;70799:59:120;;;:8;:59::i;:::-;70877:25;;;;:28;70868:110;;;;;;;;;;;;;;;;;;70907:18;;70868:110;70877:28;70868:110;;;:8;:110::i;:::-;70997:24;;;;70988:110;;71022:1;70997:27;;5220:277:21;5398:47;5417:4;5423:9;5434:1;5437;5440:4;5398:18;:47::i;:::-;5455:35;5471:4;5477:9;5488:1;5455:15;:35::i;7377:845::-;7662:26;;7514:12;;-1:-1:-1;;;7662:26:21;;;;7647:41;;;;7643:573;;7734:17;;;;;;;7776:24;7734:17;7792:7;7776:9;:24::i;:::-;7766:34;;7876:27;7906:48;7928:4;7934:19;7906:21;:48::i;:::-;8021:14;;;;8044:19;;;8021:43;7996:68;;-1:-1:-1;8093:84:21;8112:4;8027:7;7996:68;8150:19;8171:5;8093:18;:84::i;:::-;-1:-1:-1;8201:4:21;;7377:845;-1:-1:-1;;;;;7377:845:21:o;71445:397:120:-;-1:-1:-1;;;;;;71550:10:120;71646:11;71563:2;71612;71646:11;:::i;:::-;71624:34;;71670:13;71685;71702:65;71737:4;71743:3;71748:4;71754;71760:6;71702:65;;:34;:65::i;:::-;71669:98;;;;71777:58;71797:7;71806;71815:4;71821:5;71828:6;71777:58;;:19;:58::i;71848:393::-;71966:1;71989;72013;71953:10;72046;71966:1;71989;72046:10;:::i;72247:394::-;72365:1;72388;72352:10;;72445:11;72352:10;72365:1;72445:11;:::i;72647:393::-;72765:1;72788;72812;72752:10;72845;72812:1;72788;72845:10;:::i;73046:393::-;73164:1;73187;73211;73151:10;73244;73187:1;73164;73244:10;:::i;73445:394::-;73563:1;73586;73610:2;73550:10;73644;73586:1;73610:2;73644:10;:::i;73845:402::-;73968:2;73992;;73955:10;74051:11;73992:2;73968;74051:11;:::i;74253:403::-;74377:2;74401;;74364:10;74460:11;74377:2;74401;74460:11;:::i;74662:402::-;74786:2;74810;74786;74773:10;74869;74786:2;74810;74869:10;:::i;75070:403::-;75195:2;75219;75195;75182:10;75278;75219:2;75195;75278:10;:::i;5864:493:21:-;6004:26;;-1:-1:-1;;;6004:26:21;;;;6081:57;6004:26;1418:2;6081:57;:::i;:::-;6057:21;:81;;;:127;;;-1:-1:-1;6158:26:21;;6057:127;6040:250;;;6256:23;;-1:-1:-1;;;6256:23:21;;;;;;;;;;;6040:250;-1:-1:-1;6299:51:21;;;;;;-1:-1:-1;;;6299:51:21;-1:-1:-1;;;;6299:51:21;;;;;;5864:493::o;1776:194:79:-;1881:10;1875:4;1868:24;1918:4;1912;1905:18;1949:4;1943;1936:18;15518:221:21;15604:9;15615;15649:28;15669:1;15672;15675;15649:19;:28::i;:::-;15636:41;;-1:-1:-1;15636:41:21;-1:-1:-1;15700:32:21;15720:1;15636:41;;15700:19;:32::i;:::-;15687:45;;;;-1:-1:-1;15518:221:21;-1:-1:-1;;;;;15518:221:21:o;1908:204:20:-;1997:14;2032:5;2036:1;2032;:5;:::i;:::-;2023:14;;2056:10;:49;;2095:10;2104:1;2095:6;:10;:::i;:::-;2056:49;;;2069:23;2082:6;2090:1;2069:12;:23::i;:::-;2047:58;1908:204;-1:-1:-1;;;;;1908:204:20:o;3020:1145:106:-;3101:13;3141:3;3134;:10;;3126:82;;;;-1:-1:-1;;;3126:82:106;;28202:2:188;3126:82:106;;;28184:21:188;28241:2;28221:18;;;28214:30;28280:34;28260:18;;;28253:62;28351:29;28331:18;;;28324:57;28398:19;;3126:82:106;;;;;;;;;3636:10;3653:1;3649;:5;:74;;3695:27;-1:-1:-1;;;3703:1:106;3695:27;:::i;:::-;3649:74;;;3689:1;3658:28;3675:11;;-1:-1:-1;;;3658:28:106;:::i;:::-;:32;;;;:::i;:::-;3636:87;;3733:12;3754:1;3748:3;:7;:80;;3798:29;-1:-1:-1;;;3806:3:106;3798:29;:::i;:::-;3748:80;;;3792:1;3759:30;3776:13;;-1:-1:-1;;;3759:30:106;:::i;:::-;:34;;;;:::i;:::-;3733:95;;3838:12;3859:1;3853:3;:7;:80;;3903:29;-1:-1:-1;;;3911:3:106;3903:29;:::i;:::-;3853:80;;;3897:1;3864:30;3881:13;;-1:-1:-1;;;3864:30:106;:::i;:::-;:34;;;;:::i;:::-;3838:95;;3944:9;3956:22;3963:2;3967:4;3973;3956:6;:22::i;:::-;3944:34;;-1:-1:-1;;;4075:1:106;:18;:83;;4139:18;-1:-1:-1;;;4139:1:106;:18;:::i;:::-;4075:83;;;4105:18;4122:1;-1:-1:-1;;;4105:18:106;:::i;:::-;4103:25;;:21;4127:1;4103:25;:::i;:::-;4066:92;3020:1145;-1:-1:-1;;;;;;;;3020:1145:106:o;9854:167::-;9944:70;10006:2;10010;9960:53;;;;;;;;;:::i;:::-;;;;-1:-1:-1;;9960:53:106;;;;;;;;;;;;;;-1:-1:-1;;;;;9960:53:106;-1:-1:-1;;;9960:53:106;;;9944:15;:70::i;33455:220:90:-;33556:5;33576:19;;;;;;33572:97;;33618:40;;-1:-1:-1;;;33618:40:90;;33648:2;33618:40;;;28995:36:188;29047:18;;;29040:34;;;28968:18;;33618:40:90;28816:264:188;33572:97:90;33455:220;;;:::o;18563:909:21:-;18873:17;;18792:5;;;;;;18845:46;;18873:17;;;;;;;18845:21;:46::i;:::-;18816:75;;18906:13;:69;;;;-1:-1:-1;18949:26:21;;-1:-1:-1;;;18949:26:21;;;;18923:52;;;;;18906:69;18902:284;;;19130:30;;;19107:54;;19083:78;18902:284;19218:34;;;;19217:204;;19364:25;;19316:105;;19342:20;;-1:-1:-1;;;19364:25:21;;;;19391:29;19316:25;:105::i;:::-;19217:204;;;19272:25;;;;;;;19217:204;19196:269;19435:20;;-1:-1:-1;18563:909:21;-1:-1:-1;;;;;;18563:909:21:o;20408:282::-;20530:44;;-1:-1:-1;;20584:48:21;20530:44;;;;;-1:-1:-1;;;;20584:48:21;;-1:-1:-1;;;20584:48:21;;;;;;;20648:35;;-1:-1:-1;9789:21:188;;;9771:40;;20648:35:21;;9759:2:188;9744:18;20648:35:21;;;;;;;20408:282;;;:::o;21208:1913::-;26839:21;:15;:21;21571;21522:25;;;21548:19;21522:46;;;;;;;:::i;:::-;;;;;;;;;;;;:70;;;;;;;;;;;;;;;;;;;;21650:16;21602:4;:24;;21627:19;21602:45;;;;;;;:::i;:::-;;;;;;;;;;;;:64;;;;;;;;;;;;;;;;;;21724:17;21764:56;21777:19;637:2;21764:12;:56::i;:::-;21744:76;;;;;;;;;-1:-1:-1;;21744:76:21;;;;;;;;-1:-1:-1;21836:31:21;;;;;21835:32;:50;;;;-1:-1:-1;21871:14:21;;21835:50;21831:119;;;21901:38;;-1:-1:-1;;21901:38:21;21935:4;21901:38;;;21831:119;22067:27;;-1:-1:-1;;;22067:27:21;;;;;22036:28;;22216:25;;;;22242:60;;22255:18;;;;;901:26;22242:12;:60::i;:::-;22216:87;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;22177:16;:126;22166:137;;22347:21;22335:33;;:8;:33;;;;:48;;;;22372:11;22335:48;22331:689;;;22434:18;;;;;;;22578:21;22527:26;;;22434:18;22527:48;;;;;;;:::i;:::-;;;;;;;;;;;;:72;;;;;;;;;;;;;;;;;;;;22667:16;22617:4;:25;;22643:20;22617:47;;;;;;;:::i;:::-;;;;;;;;;;;;:66;;;;;;;;;;;;;;;;;;22792:58;22805:20;696:1;22792:12;:58::i;:::-;22771:79;;-1:-1:-1;;22771:79:21;;;;;;;;;;;;;;;;;;-1:-1:-1;22771:79:21;22874:32;;;22873:33;:51;;;;-1:-1:-1;22910:14:21;;22873:51;22869:137;;;22948:39;;-1:-1:-1;;22948:39:21;;;;;22869:137;22385:635;22331:689;-1:-1:-1;;23091:23:21;;;;;;;;-1:-1:-1;;23091:23:21;;;;;;;;;;-1:-1:-1;;;;;21208:1913:21:o;25619:909::-;25814:32;;25703:5;;3685:1:30;;25814:32:21;;;;;;25857:186;;25973:58;25997:4;26003:27;25973:23;:58::i;:::-;901:26;926:1;696;901:26;:::i;:::-;25944:87;;;;:::i;:::-;25905:127;;25857:186;26069:13;;;;;;;;;;3542:2:30;26138:30:21;;;26204:23;;;;26263;;;;26305:22;;;;-1:-1:-1;26301:186:21;;;26363:12;26347:29;;26301:186;;;26411:12;26401:7;:22;;;26397:90;;;26459:12;26443:29;;26397:90;-1:-1:-1;26514:7:21;;25619:909;-1:-1:-1;;;;;;;25619:909:21:o;16009:199::-;16104:5;16128:4;:25;;16154:46;16167:5;839:1;637:2;815:25;;;;:::i;:::-;16154:12;:46::i;:::-;16128:73;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;16121:80;;16009:199;;;;:::o;15745:258::-;15823:9;15834;15872:1;15868:5;;:1;:5;;;:23;;15886:1;15889;15868:23;;;15877:1;15880;15868:23;15855:36;;-1:-1:-1;15855:36:21;-1:-1:-1;15905:7:21;;;;;;;;;15901:96;;;15934:1;15928:7;;15901:96;;;15962:1;15956:7;;:3;:7;;;15952:45;;;-1:-1:-1;15985:1:21;15952:45;15745:258;;;;;;:::o;1546:1263:106:-;1630:14;1671:3;1664;:10;;1656:85;;;;-1:-1:-1;;;1656:85:106;;29287:2:188;1656:85:106;;;29269:21:188;29326:2;29306:18;;;29299:30;29365:34;29345:18;;;29338:62;29436:32;29416:18;;;29409:60;29486:19;;1656:85:106;29085:426:188;1656:85:106;1975:3;1970:1;:8;;:20;;;;;1987:3;1982:1;:8;;1970:20;1966:34;;;-1:-1:-1;1999:1:106;1992:8;;1966:34;2011:12;2026:9;2032:3;2026;:9;:::i;:::-;:13;;2038:1;2026:13;:::i;:::-;2011:28;;2234:1;2229;:6;;:18;;;;;2246:1;2239:4;:8;2229:18;2225:38;;;2256:7;2262:1;2256:3;:7;:::i;:::-;2249:14;;;;;2225:38;2282:15;2296:1;-1:-1:-1;;2282:15:106;:::i;:::-;2277:1;:20;;:46;;;;-1:-1:-1;2308:15:106;2322:1;-1:-1:-1;;2308:15:106;:::i;:::-;2301:4;:22;2277:46;2273:82;;;2339:15;2353:1;-1:-1:-1;;2339:15:106;:::i;:::-;2332:23;;:3;:23;:::i;2273:82::-;2459:3;2455:1;:7;2451:352;;;2478:12;2493:7;2497:3;2493:1;:7;:::i;:::-;2478:22;-1:-1:-1;2514:11:106;2528;2535:4;2478:22;2528:11;:::i;:::-;2514:25;;2557:3;2564:1;2557:8;2553:24;;2574:3;2567:10;;;;;;;2553:24;2612:1;2600:9;2606:3;2600;:9;:::i;:::-;:13;;;;:::i;:::-;2591:22;;2464:160;;2451:352;;;2638:3;2634:1;:7;2630:173;;;2657:12;2672:7;2678:1;2672:3;:7;:::i;:::-;2657:22;-1:-1:-1;2693:11:106;2707;2714:4;2657:22;2707:11;:::i;:::-;2693:25;;2736:3;2743:1;2736:8;2732:24;;2753:3;2746:10;;;;;;;2732:24;2779:9;2785:3;2779;:9;:::i;:::-;:13;;2791:1;2779:13;:::i;:::-;2770:22;;2643:160;;2630:173;1646:1163;1546:1263;;;;;:::o;9016:133::-;9087:55;9134:7;9113:19;9087:55::i;20023:379:21:-;20198:5;20219:29;:34;;20252:1;20219:34;20215:73;;-1:-1:-1;;;20255:33:21;;20215:73;20306:89;20327:13;20342:21;20365:29;20306:89;;:20;:89::i;:::-;20299:96;20023:379;-1:-1:-1;;;;20023:379:21:o;16399:191::-;16486:5;16561:11;16541:12;16556:1;16541:16;16540:32;;;;;:::i;:::-;;;16399:191;-1:-1:-1;;;16399:191:21:o;16214:179::-;16292:7;16342:10;;:34;;16375:1;16367:5;:9;16342:34;;;-1:-1:-1;16355:9:21;16335:41;-1:-1:-1;16214:179:21:o;24491:496::-;24629:14;901:26;926:1;696;901:26;:::i;:::-;24846:35;;24896:27;24891:90;;24948:18;;:22;;24969:1;;24948:18;;;;;:22;:::i;:::-;24939:31;;;24491:496;-1:-1:-1;;;24491:496:21:o;9155:381:106:-;9253:14;;679:42;9427:2;9414:16;;9229:21;;9253:14;9414:16;679:42;9463:5;9452:68;9443:77;;9380:150;;9155:381;:::o;23736:342:21:-;23897:10;24047:12;24015:21;23992:20;:44;23991:69;;;;;;;:::i;:::-;;;23736:342;-1:-1:-1;;;;23736:342:21:o;-1:-1:-1:-;;;;;;;;:::o;14:637:188:-;204:2;216:21;;;286:13;;189:18;;;308:22;;;156:4;;387:15;;;361:2;346:18;;;156:4;430:195;444:6;441:1;438:13;430:195;;;509:13;;-1:-1:-1;;;;;505:39:188;493:52;;574:2;600:15;;;;565:12;;;;541:1;459:9;430:195;;;-1:-1:-1;642:3:188;;14:637;-1:-1:-1;;;;;14:637:188:o;656:289::-;698:3;736:5;730:12;763:6;758:3;751:19;819:6;812:4;805:5;801:16;794:4;789:3;785:14;779:47;871:1;864:4;855:6;850:3;846:16;842:27;835:38;934:4;927:2;923:7;918:2;910:6;906:15;902:29;897:3;893:39;889:50;882:57;;;656:289;;;;:::o;950:1628::-;1156:4;1204:2;1193:9;1189:18;1234:2;1223:9;1216:21;1257:6;1292;1286:13;1323:6;1315;1308:22;1361:2;1350:9;1346:18;1339:25;;1423:2;1413:6;1410:1;1406:14;1395:9;1391:30;1387:39;1373:53;;1461:2;1453:6;1449:15;1482:1;1492:1057;1506:6;1503:1;1500:13;1492:1057;;;-1:-1:-1;;1571:22:188;;;1567:36;1555:49;;1627:13;;1714:9;;-1:-1:-1;;;;;1710:35:188;1695:51;;1793:2;1785:11;;;1779:18;1679:2;1817:15;;;1810:27;;;1898:19;;1667:15;;;1930:24;;;2085:21;;;1988:2;2038:1;2034:16;;;2022:29;;2018:38;;;1976:15;;;;-1:-1:-1;2144:296:188;2160:8;2155:3;2152:17;2144:296;;;2266:2;2262:7;2253:6;2245;2241:19;2237:33;2230:5;2223:48;2298:42;2333:6;2322:8;2316:15;2298:42;:::i;:::-;2383:2;2369:17;;;;2288:52;;-1:-1:-1;2412:14:188;;;;;2188:1;2179:11;2144:296;;;-1:-1:-1;2463:6:188;;-1:-1:-1;;;2504:2:188;2527:12;;;;2492:15;;;;;-1:-1:-1;1528:1:188;1521:9;1492:1057;;;-1:-1:-1;2566:6:188;;950:1628;-1:-1:-1;;;;;;950:1628:188:o;2583:118::-;2670:5;2667:1;2656:20;2649:5;2646:31;2636:59;;2691:1;2688;2681:12;2706:243;2763:6;2816:2;2804:9;2795:7;2791:23;2787:32;2784:52;;;2832:1;2829;2822:12;2784:52;2871:9;2858:23;2890:29;2913:5;2890:29;:::i;2954:446::-;3006:3;3044:5;3038:12;3071:6;3066:3;3059:19;3103:4;3098:3;3094:14;3087:21;;3142:4;3135:5;3131:16;3165:1;3175:200;3189:6;3186:1;3183:13;3175:200;;;3254:13;;-1:-1:-1;;;;;;3250:40:188;3238:53;;3320:4;3311:14;;;;3348:17;;;;3211:1;3204:9;3175:200;;;-1:-1:-1;3391:3:188;;2954:446;-1:-1:-1;;;;2954:446:188:o;3405:1145::-;3625:4;3673:2;3662:9;3658:18;3703:2;3692:9;3685:21;3726:6;3761;3755:13;3792:6;3784;3777:22;3830:2;3819:9;3815:18;3808:25;;3892:2;3882:6;3879:1;3875:14;3864:9;3860:30;3856:39;3842:53;;3930:2;3922:6;3918:15;3951:1;3961:560;3975:6;3972:1;3969:13;3961:560;;;4068:2;4064:7;4052:9;4044:6;4040:22;4036:36;4031:3;4024:49;4102:6;4096:13;4148:2;4142:9;4179:2;4171:6;4164:18;4209:48;4253:2;4245:6;4241:15;4227:12;4209:48;:::i;:::-;4195:62;;4306:2;4302;4298:11;4292:18;4270:40;;4359:6;4351;4347:19;4342:2;4334:6;4330:15;4323:44;4390:51;4434:6;4418:14;4390:51;:::i;:::-;4380:61;-1:-1:-1;;;4476:2:188;4499:12;;;;4464:15;;;;;3997:1;3990:9;3961:560;;4555:782;4717:4;4765:2;4754:9;4750:18;4795:2;4784:9;4777:21;4818:6;4853;4847:13;4884:6;4876;4869:22;4922:2;4911:9;4907:18;4900:25;;4984:2;4974:6;4971:1;4967:14;4956:9;4952:30;4948:39;4934:53;;5022:2;5014:6;5010:15;5043:1;5053:255;5067:6;5064:1;5061:13;5053:255;;;5160:2;5156:7;5144:9;5136:6;5132:22;5128:36;5123:3;5116:49;5188:40;5221:6;5212;5206:13;5188:40;:::i;:::-;5178:50;-1:-1:-1;5263:2:188;5286:12;;;;5251:15;;;;;5089:1;5082:9;5053:255;;5342:1033;5546:4;5594:2;5583:9;5579:18;5624:2;5613:9;5606:21;5647:6;5682;5676:13;5713:6;5705;5698:22;5751:2;5740:9;5736:18;5729:25;;5813:2;5803:6;5800:1;5796:14;5785:9;5781:30;5777:39;5763:53;;5851:2;5843:6;5839:15;5872:1;5882:464;5896:6;5893:1;5890:13;5882:464;;;5961:22;;;-1:-1:-1;;5957:36:188;5945:49;;6017:13;;6062:9;;-1:-1:-1;;;;;6058:35:188;6043:51;;6141:2;6133:11;;;6127:18;6182:2;6165:15;;;6158:27;;;6127:18;6208:58;;6250:15;;6127:18;6208:58;:::i;:::-;6198:68;-1:-1:-1;;6301:2:188;6324:12;;;;6289:15;;;;;5918:1;5911:9;5882:464;;6572:354;6796:6;6784:19;;;;6766:38;;6852:8;6840:21;;;;6835:2;6820:18;;6813:49;6909:1;6898:21;6893:2;6878:18;;6871:49;6754:2;6739:18;;6572:354::o;6931:127::-;6992:10;6987:3;6983:20;6980:1;6973:31;7023:4;7020:1;7013:15;7047:4;7044:1;7037:15;7063:128;7130:9;;;7151:11;;;7148:37;;;7165:18;;:::i;7196:168::-;7269:9;;;7300;;7317:15;;;7311:22;;7297:37;7287:71;;7338:18;;:::i;7633:170::-;7730:10;7723:18;;;7703;;;7699:43;;7754:20;;7751:46;;;7777:18;;:::i;8100:134::-;8177:13;;8199:29;8177:13;8199:29;:::i;8239:247::-;8307:6;8360:2;8348:9;8339:7;8335:23;8331:32;8328:52;;;8376:1;8373;8366:12;8328:52;8408:9;8402:16;8427:29;8450:5;8427:29;:::i;8763:185::-;8859:1;8830:16;;;8848;;;;8826:39;8911:5;8880:16;;-1:-1:-1;;8898:20:188;;8877:42;8874:68;;;8922:18;;:::i;8953:127::-;9014:10;9009:3;9005:20;9002:1;8995:31;9045:4;9042:1;9035:15;9069:4;9066:1;9059:15;9085:273;9123:1;9164;9161;9150:16;9200:1;9197;9186:16;9221:3;9211:37;;9228:18;;:::i;:::-;-1:-1:-1;;9264:19:188;;-1:-1:-1;;9285:15:188;;9260:41;9257:67;;;9304:18;;:::i;:::-;9338:14;;;9085:273;-1:-1:-1;;;9085:273:188:o;9822:127::-;9883:10;9878:3;9874:20;9871:1;9864:31;9914:4;9911:1;9904:15;9938:4;9935:1;9928:15;9954:255;10026:2;10020:9;10068:6;10056:19;;10105:18;10090:34;;10126:22;;;10087:62;10084:88;;;10152:18;;:::i;:::-;10188:2;10181:22;9954:255;:::o;10214:275::-;10285:2;10279:9;10350:2;10331:13;;-1:-1:-1;;10327:27:188;10315:40;;10385:18;10370:34;;10406:22;;;10367:62;10364:88;;;10432:18;;:::i;:::-;10468:2;10461:22;10214:275;;-1:-1:-1;10214:275:188:o;10494:164::-;10570:13;;10619;;10612:21;10602:32;;10592:60;;10648:1;10645;10638:12;10663:160;10740:13;;10793:4;10782:16;;10772:27;;10762:55;;10813:1;10810;10803:12;10828:165;10906:13;;10959:8;10948:20;;10938:31;;10928:59;;10983:1;10980;10973:12;10998:164;11075:13;;11128:1;11117:20;;;11107:31;;11097:59;;11152:1;11149;11142:12;11167:613;11237:5;11290:3;11283:4;11275:6;11271:17;11267:27;11257:55;;11308:1;11305;11298:12;11257:55;11336:1;11397:4;11421:21;11397:4;11421:21;:::i;:::-;11410:32;-1:-1:-1;11492:17:188;;11410:32;11521:15;;;11518:35;;;11549:1;11546;11539:12;11518:35;11573:6;11588:161;11604:6;11599:3;11596:15;11588:161;;;11672:32;11700:3;11672:32;:::i;:::-;11660:45;;11734:4;11725:14;;;;11621;11588:161;;;-1:-1:-1;11767:7:188;;11167:613;-1:-1:-1;;;;;11167:613:188:o;11785:601::-;11844:5;11897:3;11890:4;11882:6;11878:17;11874:27;11864:55;;11915:1;11912;11905:12;11864:55;11943:1;12004:3;12027:21;12004:3;12027:21;:::i;:::-;12016:32;-1:-1:-1;12098:17:188;;12016:32;12127:15;;;12124:35;;;12155:1;12152;12145:12;12124:35;12179:6;12194:161;12210:6;12205:3;12202:15;12194:161;;;12278:32;12306:3;12278:32;:::i;:::-;12266:45;;12340:4;12331:14;;;;12227;12194:161;;12391:167;12469:13;;12522:10;12511:22;;12501:33;;12491:61;;12548:1;12545;12538:12;12563:615;12634:5;12687:3;12680:4;12672:6;12668:17;12664:27;12654:55;;12705:1;12702;12695:12;12654:55;12733:1;12794:4;12818:21;12794:4;12818:21;:::i;:::-;12807:32;-1:-1:-1;12889:17:188;;12807:32;12918:15;;;12915:35;;;12946:1;12943;12936:12;12915:35;12970:6;12985:162;13001:6;12996:3;12993:15;12985:162;;;13069:33;13098:3;13069:33;:::i;:::-;13057:46;;13132:4;13123:14;;;;13018;12985:162;;13183:603;13243:5;13296:3;13289:4;13281:6;13277:17;13273:27;13263:55;;13314:1;13311;13304:12;13263:55;13342:1;13403:3;13426:21;13403:3;13426:21;:::i;:::-;13415:32;-1:-1:-1;13497:17:188;;13415:32;13526:15;;;13523:35;;;13554:1;13551;13544:12;13523:35;13578:6;13593:162;13609:6;13604:3;13601:15;13593:162;;;13677:33;13706:3;13677:33;:::i;:::-;13665:46;;13740:4;13731:14;;;;13626;13593:162;;13791:1422;13891:6;13951:4;13939:9;13930:7;13926:23;13922:34;13968:2;13965:22;;;13983:1;13980;13973:12;13965:22;-1:-1:-1;14025:22:188;;:::i;:::-;14070:37;14097:9;14070:37;:::i;:::-;14063:5;14056:52;14140:46;14182:2;14171:9;14167:18;14140:46;:::i;:::-;14135:2;14128:5;14124:14;14117:70;14219:47;14262:2;14251:9;14247:18;14219:47;:::i;:::-;14214:2;14207:5;14203:14;14196:71;14299:47;14342:2;14331:9;14327:18;14299:47;:::i;:::-;14294:2;14287:5;14283:14;14276:71;14380:48;14423:3;14412:9;14408:19;14380:48;:::i;:::-;14374:3;14367:5;14363:15;14356:73;14462:48;14505:3;14494:9;14490:19;14462:48;:::i;:::-;14456:3;14449:5;14445:15;14438:73;14544:49;14588:3;14577:9;14573:19;14544:49;:::i;:::-;14538:3;14531:5;14527:15;14520:74;14627:49;14671:3;14660:9;14656:19;14627:49;:::i;:::-;14621:3;14614:5;14610:15;14603:74;14710:48;14753:3;14742:9;14738:19;14710:48;:::i;:::-;14704:3;14697:5;14693:15;14686:73;14792:74;14858:7;14852:3;14841:9;14837:19;14792:74;:::i;:::-;14786:3;14779:5;14775:15;14768:99;14903:64;14959:7;14952:4;14941:9;14937:20;14903:64;:::i;:::-;14894:6;14887:5;14883:18;14876:92;15004:76;15072:7;15065:4;15054:9;15050:20;15004:76;:::i;:::-;14995:6;14988:5;14984:18;14977:104;15117:65;15174:7;15167:4;15156:9;15152:20;15117:65;:::i;:::-;15108:6;15097:18;;15090:93;15101:5;13791:1422;-1:-1:-1;;;13791:1422:188:o;15218:237::-;15290:9;;;15257:7;15315:9;;-1:-1:-1;;;15326:18:188;;15311:34;15308:60;;;15348:18;;:::i;:::-;15421:1;15412:7;15407:16;15404:1;15401:23;15397:1;15390:9;15387:38;15377:72;;15429:18;;:::i;15460:237::-;15498:7;15575:1;15572;15561:16;15557:1;15554;15543:16;15539:39;15612:11;15609:1;15598:26;15587:37;;15655:11;15646:7;15643:24;15633:58;;15671:18;;:::i;:::-;15633:58;15460:237;;;;:::o;16168:184::-;16238:6;16291:2;16279:9;16270:7;16266:23;16262:32;16259:52;;;16307:1;16304;16297:12;16259:52;-1:-1:-1;16330:16:188;;16168:184;-1:-1:-1;16168:184:188:o;16357:238::-;16466:8;16441:16;;;16459;;;16437:39;16496:26;;;;16541:24;;;16531:58;;16569:18;;:::i;16795:202::-;-1:-1:-1;;;;;;16957:33:188;;;;16939:52;;16927:2;16912:18;;16795:202::o;17372:164::-;17467:8;17460:16;;;17442;;;17438:39;;17489:18;;17486:44;;;17510:18;;:::i;17541:174::-;17580:1;17614:8;17611:1;17607:16;17642:3;17632:37;;17649:18;;:::i;:::-;17705:3;17694:8;17691:1;17687:16;17683:26;17678:31;;;17541:174;;;;:::o;17720:182::-;17754:3;17801:5;17798:1;17787:20;17835:5;17831:10;17822:7;17819:23;17816:49;;17845:18;;:::i;:::-;17885:1;17881:15;;17720:182;-1:-1:-1;;17720:182:188:o;17907:380::-;17986:1;17982:12;;;;18029;;;18050:61;;18104:4;18096:6;18092:17;18082:27;;18050:61;18157:2;18149:6;18146:14;18126:18;18123:38;18120:161;;18203:10;18198:3;18194:20;18191:1;18184:31;18238:4;18235:1;18228:15;18266:4;18263:1;18256:15;18292:232;18399:6;18376:14;;;18392;;;18372:35;18427:24;;;;18470;;;18460:58;;18498:18;;:::i;18529:158::-;18622:6;18615:14;;;18599;;;18595:35;;18642:16;;18639:42;;;18661:18;;:::i;18692:127::-;18753:10;18748:3;18744:20;18741:1;18734:31;18784:4;18781:1;18774:15;18808:4;18805:1;18798:15;18824:155;18915:6;18892:14;;;18908;;;18888:35;;18935:15;;18932:41;;;18953:18;;:::i;19259:507::-;19341:6;19349;19357;19410:2;19398:9;19389:7;19385:23;19381:32;19378:52;;;19426:1;19423;19416:12;19378:52;19458:9;19452:16;19477:29;19500:5;19477:29;:::i;:::-;19575:2;19560:18;;19554:25;19525:5;;-1:-1:-1;19588:31:188;19554:25;19588:31;:::i;:::-;19690:2;19675:18;;19669:25;19638:7;;-1:-1:-1;19703:31:188;19669:25;19703:31;:::i;:::-;19753:7;19743:17;;;19259:507;;;;;:::o;19771:125::-;19836:9;;;19857:10;;;19854:36;;;19870:18;;:::i;19901:170::-;19940:1;19974:6;19971:1;19967:14;20000:3;19990:37;;20007:18;;:::i;:::-;20061:3;20052:6;20049:1;20045:14;20041:24;20036:29;;;19901:170;;;;:::o;20076:193::-;20115:1;20141;20131:35;;20146:18;;:::i;:::-;-1:-1:-1;;;20182:18:188;;-1:-1:-1;;20202:13:188;;20178:38;20175:64;;;20219:18;;:::i;:::-;-1:-1:-1;20253:10:188;;20076:193::o;20274:162::-;20305:1;20339:6;20336:1;20332:14;20365:3;20355:37;;20372:18;;:::i;:::-;20426:3;20417:6;20414:1;20410:14;20406:24;20401:29;;;20274:162;;;;:::o;20441:189::-;20539:1;20528:16;;;20510;;;;20506:39;-1:-1:-1;;20560:21:188;;20593:6;20583:17;;20557:44;20554:70;;;20604:18;;:::i;20635:180::-;20673:3;20717:6;20710:5;20706:18;20748:6;20739:7;20736:19;20733:45;;20758:18;;:::i;:::-;20807:1;20794:15;;20635:180;-1:-1:-1;;20635:180:188:o;20820:200::-;20886:9;;;20859:4;20914:9;;20942:10;;20954:12;;;20938:29;20977:12;;;20969:21;;20935:56;20932:82;;;20994:18;;:::i;21025:216::-;21089:9;;;21117:11;;;21064:3;21147:9;;21175:10;;21171:19;;21200:10;;21192:19;;21168:44;21165:70;;;21215:18;;:::i;:::-;21165:70;;21025:216;;;;:::o;21632:237::-;21670:7;21747:1;21744;21733:16;21729:1;21726;21715:16;21711:39;21784:11;21781:1;21770:26;21759:37;;21827:11;21818:7;21815:24;21805:58;;21843:18;;:::i;21874:112::-;21906:1;21932;21922:35;;21937:18;;:::i;:::-;-1:-1:-1;21971:9:188;;21874:112::o;22292:120::-;22332:1;22358;22348:35;;22363:18;;:::i;:::-;-1:-1:-1;22397:9:188;;22292:120::o;23135:161::-;23228:8;23203:16;;;23221;;;23199:39;;23250:17;;23247:43;;;23270:18;;:::i;23301:184::-;23339:3;23383:8;23376:5;23372:20;23416:8;23407:7;23404:21;23401:47;;23428:18;;:::i;23490:175::-;23527:3;23571:4;23564:5;23560:16;23600:4;23591:7;23588:17;23585:43;;23608:18;;:::i;24411:358::-;24612:6;24601:9;24594:25;24655:6;24650:2;24639:9;24635:18;24628:34;24698:2;24693;24682:9;24678:18;24671:30;24575:4;24718:45;24759:2;24748:9;24744:18;24736:6;24718:45;:::i;25141:193::-;25239:1;25228:16;;;25210;;;;25206:39;-1:-1:-1;;25260:23:188;;25295:8;25285:19;;25257:48;25254:74;;;25308:18;;:::i;25339:189::-;25435:1;25406:16;;;25424;;;;25402:39;25489:7;25456:18;;-1:-1:-1;;25476:22:188;;25453:46;25450:72;;;25502:18;;:::i;25713:756::-;25793:6;25846:2;25834:9;25825:7;25821:23;25817:32;25814:52;;;25862:1;25859;25852:12;25814:52;25895:9;25889:16;25928:18;25920:6;25917:30;25914:50;;;25960:1;25957;25950:12;25914:50;25983:22;;26036:4;26028:13;;26024:27;-1:-1:-1;26014:55:188;;26065:1;26062;26055:12;26014:55;26098:2;26092:9;26124:18;26116:6;26113:30;26110:56;;;26146:18;;:::i;:::-;26188:57;26235:2;26212:17;;-1:-1:-1;;26208:31:188;26241:2;26204:40;26188:57;:::i;:::-;26268:6;26261:5;26254:21;26316:7;26311:2;26302:6;26298:2;26294:15;26290:24;26287:37;26284:57;;;26337:1;26334;26327:12;26284:57;26385:6;26380:2;26376;26372:11;26367:2;26360:5;26356:14;26350:42;26437:1;26412:18;;;26432:2;26408:27;26401:38;;;;26416:5;25713:756;-1:-1:-1;;;;25713:756:188:o;27280:262::-;27397:16;27364:24;;;27390;;;27360:55;27435:34;;;;27488:24;;;27478:58;;27516:18;;:::i;27547:237::-;27585:7;27662:1;27659;27648:16;27644:1;27641;27630:16;27626:39;27699:11;27696:1;27685:26;27674:37;;27742:11;27733:7;27730:24;27720:58;;27758:18;;:::i;27789:206::-;27885:1;27856:16;;;27874;;;;27852:39;27947:16;27906:26;;-1:-1:-1;;27934:31:188;;27903:63;27900:89;;;27969:18;;:::i;28428:383::-;28625:2;28614:9;28607:21;28588:4;28651:45;28692:2;28681:9;28677:18;28669:6;28651:45;:::i;:::-;28744:9;28736:6;28732:22;28727:2;28716:9;28712:18;28705:50;28772:33;28798:6;28790;28772:33;:::i;29516:151::-;29606:4;29599:12;;;29585;;;29581:31;;29624:14;;29621:40;;;29641:18;;:::i", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "setUp()": "0a9254e4", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "testBlockUpdateBeforeInitialization()": "f5720e62", "testGeometricTWAP_AfterOneLapAroundLongTermBuffer()": "dd21739d", "testGeometricTWAP_AfterOneLapAroundMidTermBuffer()": "e5add082", "testGeometricTWAP_AfterOneLongTermInterval()": "dc356a59", "testGeometricTWAP_AfterTwoUpdates()": "7a85ad2a", "testGeometricTWAP_ChangeLongTermIntervalBeforeFullCycle()": "9c5f5859", "testGeometricTWAP_DecrementLongTermConfigToLowerThanCurrentIndex()": "87c0c356", "testGeometricTWAP_FullyLoadBufferAfterIntervalChange()": "f4c9599d", "testGeometricTWAP_InitializeWithOneUpdate()": "3665e663", "testGeometricTWAP_InvalidLongTermIntervalConfig()": "6a1082d9", "testGeometricTWAP_InvalidMidTermIntervalConfig()": "1323792d", "testGeometricTWAP_LendingStateTickFirstCallNotAvailable()": "7414aa5c", "testGeometricTWAP_LendingStateTickSameBlockCallUnchanged()": "70c74f48", "testGeometricTWAP_LendingStateTick_NotAvailable()": "d0bbca17", "testGeometricTWAP_LendingStateUpdateForOneBlock()": "8f0065dc", "testGeometricTWAP_LendingStateUpdateForTwoBlocks()": "0287507e", "testGeometricTWAP_LongTermBufferBlocksWithMissingBlocks()": "68983628", "testGeometricTWAP_LongTermTickChangesAfterConfigMovedUp()": "0fa509fc", "testGeometricTWAP_MidTermIntervalConfigs()": "b3ad5685", "testGeometricTWAP_ModifyLongTermIntervalWithMissingBlocks()": "6a9dcdae", "testGeometricTWAP_NegativeTicks()": "98a8b953", "testGeometricTWAP_NineDaysLongTermBuffer()": "05c2747e", "testGeometricTWAP_OneMidTermRoundMissedBlock()": "e4bbbe24", "testGeometricTWAP_Outlier_MaxTickDelta()": "8c2cc9db", "testGeometricTWAP_Outlier_PriceGoesDown()": "1820eb1a", "testGeometricTWAP_Outlier_PriceGoesUp()": "0b0596c9", "testGeometricTWAP_PartialLoadBufferAfterIntervalChange()": "4958069a", "testGeometricTWAP_PositiveTicks()": "b0cf19e4", "testGeometricTWAP_PriceMovementDown()": "d17b294d", "testGeometricTWAP_PriceMovementUp()": "8b951745", "testGeometricTWAP_ReturnsSameValueAfterConfigMovedDown()": "5d565c64", "testGeometricTWAP_SimpleTicksWithMissingBlocks()": "71bbc3a8", "testGeometricTWAP_TickRange_LongTermIndexAsFactor()": "5b0c0a4c", "testGeometricTWAP_TickRange_LongTermIndexAsFactorWithMissingBlocks()": "709b8561", "testGeometricTWAP_TickRange_MaxTickBound_LongMin_Fuzz(int16)": "73f6f392", "testGeometricTWAP_TickRange_MaxTickBound_MidMin_Fuzz(int16)": "f2cb6e47", "testGeometricTWAP_TickRange_MinAndMaxTickBound()": "e26a8c7e", "testGeometricTWAP_TickRange_MinTickBound_LongMax_Fuzz(int16)": "f86bfbf2", "testGeometricTWAP_TickRange_MinTickBound_MidMax_Fuzz(int16)": "669fe02d", "testGeometricTWAP_TickRange_PriceDownwards()": "3a25a3e2", "testGeometricTWAP_TickRange_PriceMovementSharplyDown()": "6dbf985f", "testGeometricTWAP_TickRange_PriceMovementSharplyUp()": "91c4f407", "testGeometricTWAP_TickRange_PriceUpwards()": "5c3e5bf0", "testGeometricTWAP_TickRange_VerifyTicksAfterIntervalChange()": "b0941e1a", "testGeometricTWAP_TickRanges()": "f41966d4", "testGeometricTWAP_WithOneMissedBlock()": "772ac9b6", "testGeometricTWAP_WithOneMissedBlockAroundLongTermCycle()": "35e214c3", "testGeometricTWAP_WithOneMissedBlockAroundMidTermCycle()": "4424b15b", "testGeometricTWAP_firstBlock()": "690e878d", "testMinimumLongTermTimeUpdateConfigInit()": "1567c691", "testMinimumLongTermTimeUpdateConfigUpdate()": "f4501420", "testOverFlowOfCumulativeTick()": "bbf7ff38"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"InvalidIntervalConfig\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"bits\",\"type\":\"uint8\"},{\"internalType\":\"int256\",\"name\":\"value\",\"type\":\"int256\"}],\"name\":\"SafeCastOverflowedIntDowncast\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int16\",\"name\":\"lendingStateTick\",\"type\":\"int16\"}],\"name\":\"UpdateLendingTick\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"setUp\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testBlockUpdateBeforeInitialization\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_AfterOneLapAroundLongTermBuffer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_AfterOneLapAroundMidTermBuffer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_AfterOneLongTermInterval\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_AfterTwoUpdates\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_ChangeLongTermIntervalBeforeFullCycle\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_DecrementLongTermConfigToLowerThanCurrentIndex\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_FullyLoadBufferAfterIntervalChange\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_InitializeWithOneUpdate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_InvalidLongTermIntervalConfig\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_InvalidMidTermIntervalConfig\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_LendingStateTickFirstCallNotAvailable\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_LendingStateTickSameBlockCallUnchanged\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_LendingStateTick_NotAvailable\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_LendingStateUpdateForOneBlock\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_LendingStateUpdateForTwoBlocks\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_LongTermBufferBlocksWithMissingBlocks\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_LongTermTickChangesAfterConfigMovedUp\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_MidTermIntervalConfigs\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_ModifyLongTermIntervalWithMissingBlocks\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_NegativeTicks\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_NineDaysLongTermBuffer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_OneMidTermRoundMissedBlock\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_Outlier_MaxTickDelta\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_Outlier_PriceGoesDown\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_Outlier_PriceGoesUp\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_PartialLoadBufferAfterIntervalChange\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_PositiveTicks\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_PriceMovementDown\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_PriceMovementUp\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_ReturnsSameValueAfterConfigMovedDown\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_SimpleTicksWithMissingBlocks\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_TickRange_LongTermIndexAsFactor\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_TickRange_LongTermIndexAsFactorWithMissingBlocks\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"long\",\"type\":\"int16\"}],\"name\":\"testGeometricTWAP_TickRange_MaxTickBound_LongMin_Fuzz\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"mid\",\"type\":\"int16\"}],\"name\":\"testGeometricTWAP_TickRange_MaxTickBound_MidMin_Fuzz\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_TickRange_MinAndMaxTickBound\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"long\",\"type\":\"int16\"}],\"name\":\"testGeometricTWAP_TickRange_MinTickBound_LongMax_Fuzz\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"int16\",\"name\":\"mid\",\"type\":\"int16\"}],\"name\":\"testGeometricTWAP_TickRange_MinTickBound_MidMax_Fuzz\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_TickRange_PriceDownwards\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_TickRange_PriceMovementSharplyDown\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_TickRange_PriceMovementSharplyUp\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_TickRange_PriceUpwards\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_TickRange_VerifyTicksAfterIntervalChange\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_TickRanges\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_WithOneMissedBlock\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_WithOneMissedBlockAroundLongTermCycle\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_WithOneMissedBlockAroundMidTermCycle\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testGeometricTWAP_firstBlock\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testMinimumLongTermTimeUpdateConfigInit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testMinimumLongTermTimeUpdateConfigUpdate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"testOverFlowOfCumulativeTick\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"This contract contains unit tests for GeometricTWAPSpec. The tests attempt to cover:          1. Verification of DEFAULT_TICK_VALUE ranges and calculations in various scenarios based on time elapsed                between recorded observations.          2. Confirmation of the behavior of the GeometricTWAP contract in different conditions.\",\"errors\":{\"SafeCastOverflowedIntDowncast(uint8,int256)\":[{\"details\":\"Value doesn't fit in an int of `bits` size.\"}]},\"events\":{\"UpdateLendingTick(int16)\":{\"details\":\"Emitted when `lendingStateTick` is updated\",\"params\":{\"lendingStateTick\":\"The updated value for lending state tick\"}}},\"kind\":\"dev\",\"methods\":{\"testOverFlowOfCumulativeTick()\":{\"details\":\"There is no assertion here, but the test runs for the 2000 years to      verify there is no overflow in the cumulative tick or timestamp.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"testGeometricTWAP_TickRange_MaxTickBound_LongMin_Fuzz(int16)\":{\"notice\":\"Considering   ```math   currentTick + delta + \\\\frac{(LONG_TERM_ARRAY_LAST_INDEX - factor) \\\\cdot LOG_BASE_OF_ROOT_TWO}{LONG_TERM_ARRAY_LAST_INDEX} < TickMath.MAX_TICK   ``` with $factor = 5$, $delta = spot - long$,   ```math   2 \\\\cdot spot - TickMath.MAX_TICK + \\\\frac{(LONG_TERM_ARRAY_LAST_INDEX - factor) \\\\cdot LOG_BASE_OF_ROOT_TWO}{LONG_TERM_ARRAY_LAST_INDEX} < long\"},\"testGeometricTWAP_TickRange_MinAndMaxTickBound()\":{\"notice\":\"delta > TickMath.MAX_TICK - currentTick delta > currentTick - TickMath.MIN_TICK factor = 8 The result is `TickMath.MIN_TICK` & `TickMath.MAX_TICK` which would pass both the if conditions for tick range bounds check.\"},\"testGeometricTWAP_TickRange_MinTickBound_LongMax_Fuzz(int16)\":{\"notice\":\"Considering,   ```math   TickMath.MIN_TICK < currentTick - delta - \\\\frac{(LONG_TERM_ARRAY_LAST_INDEX - factor) \\\\cdot LOG_BASE_OF_ROOT_TWO}{LONG_TERM_ARRAY_LAST_INDEX}   ``` with $factor = 3$, $delta = long - spot$,   ```math   long < 2 \\\\cdot spot - \\\\frac{(LONG_TERM_ARRAY_LAST_INDEX - factor) \\\\cdot LOG_BASE_OF_ROOT_TWO}{LONG_TERM_ARRAY_LAST_INDEX} - TickMath.MIN_TICK   ``` Here we test long where the above does not hold.\"}},\"notice\":\"Unit tests for GeometricTWAPSpec.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/GeometricTWAPSpecTests.sol\":\"GeometricTWAPSpecTests\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/AmmalgamPair.sol\":{\"keccak256\":\"0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4\",\"urls\":[\"bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2\",\"dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS\"]},\"contracts/SaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76\",\"dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE\"]},\"contracts/factories/AmmalgamFactory.sol\":{\"keccak256\":\"0xe0d9baf63d9538a7ecb8bd24ea61a8cdf6fc9c1e9eb028f343548adeb8b93e4e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://bfca24db47fbbaeef8bc36996cbfed78eb48771ac71d2800f081fb58a8e8c92b\",\"dweb:/ipfs/QmUfYEwfmrjSmchqXi58SnpSina4qKQvD2Jbk5RqYsaoCa\"]},\"contracts/interfaces/IAmmalgamPair.sol\":{\"keccak256\":\"0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795\",\"dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw\"]},\"contracts/interfaces/ISaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20\",\"dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR\"]},\"contracts/interfaces/callbacks/IAmmalgamCallee.sol\":{\"keccak256\":\"0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d\",\"dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1\"]},\"contracts/interfaces/callbacks/ITransferValidator.sol\":{\"keccak256\":\"0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4\",\"dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8\"]},\"contracts/interfaces/factories/IAmmalgamFactory.sol\":{\"keccak256\":\"0x1c80089901e8d7d7451775b5eaa92092eb2b65319cb92fa7884281bae49f52b8\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://bf1201399bb9d5046e0e788ff88394b2468771096a2a0d3500af542923e84628\",\"dweb:/ipfs/QmeUaPyQpbPbP5fyPUT2FfzeDgHcdyQAn1DaNg9uCuGoj9\"]},\"contracts/interfaces/factories/IFactoryCallback.sol\":{\"keccak256\":\"0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b\",\"dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT\"]},\"contracts/interfaces/factories/INewTokensFactory.sol\":{\"keccak256\":\"0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b\",\"dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/GeometricTWAP.sol\":{\"keccak256\":\"0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa\",\"dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz\"]},\"contracts/libraries/Interest.sol\":{\"keccak256\":\"0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194\",\"dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX\"]},\"contracts/libraries/Liquidation.sol\":{\"keccak256\":\"0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877\",\"dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW\"]},\"contracts/libraries/QuadraticSwapFees.sol\":{\"keccak256\":\"0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb\",\"dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3\"]},\"contracts/libraries/Saturation.sol\":{\"keccak256\":\"0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20\",\"dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE\"]},\"contracts/libraries/TickMath.sol\":{\"keccak256\":\"0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20\",\"dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG\"]},\"contracts/libraries/Uint16Set.sol\":{\"keccak256\":\"0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06\",\"dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy\"]},\"contracts/libraries/Validation.sol\":{\"keccak256\":\"0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778\",\"dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"contracts/tokens/TokenController.sol\":{\"keccak256\":\"0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159\",\"dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn\"]},\"lib/mangrove-core/lib/core/BitLib.sol\":{\"keccak256\":\"0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8\",\"dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr\"]},\"lib/morpho-blue/src/libraries/MathLib.sol\":{\"keccak256\":\"0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08\",\"dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd\"]},\"lib/openzeppelin-contracts/contracts/access/AccessControl.sol\":{\"keccak256\":\"0xb64ecf1154f183412bcde47168f3af245e4120846346a0b3872c631e361156d2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://85331049a60659bc4489733ccd3cbeb177b65691122a8cb637bf9267ab25e23d\",\"dweb:/ipfs/QmbGpDcdwKTirzSCoZfE4rHG7jBSWsE4K2iSb6UCYXtLJv\"]},\"lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x5643a5cadd1278581308b20becb48a50946c159fc31c29fc407ea9a61fc865d1\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c7d79f305a239207a24fa2174897c8ea8ff1e81cb790d440fd54c89a0e85f63e\",\"dweb:/ipfs/QmT847eeAMnRN3DaG1zsKNMn7qipNAidqv1REnKexPkrfA\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol\":{\"keccak256\":\"0xb541b133f2d85eb37ae866cb21123be93bd3671b6840c47f951da52d3a704548\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://22443f43ece107eaf453aa9a41a59410cece5429c2779c794fbd2c8b5aa29d38\",\"dweb:/ipfs/Qmbum2jwLYuT9aZ2fr9NMLwWFsVavonrGm2VnbAL9hP2jn\"]},\"lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol\":{\"keccak256\":\"0x3f91c79d6f55db9e4fc36e1cfe6a483a7b0f5be60fecbd979555071673746d47\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9b1e3c64cbeb2757a2a1a45c69f7f3984a93b0eadd1016341b64f9d94f89d7c4\",\"dweb:/ipfs/QmP1Mj14U4vMTFa2rv2nodMbWSCov2ac9Md8W2aUcgYdKX\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41\",\"dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb\",\"dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol\":{\"keccak256\":\"0xad94c8d7246a50210f7bcb54e5b91fc9f1c6e137263ac972ca5dd0f7f6d4d49d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6938e96fbb0cf3d961788b6c3522400e255d8057d1b9f7e08a50e0b48486b007\",\"dweb:/ipfs/QmNXG3MPzDXjHJ9iWDYCz4vi9RBTgVBnZjndnfBwMfhkyD\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol\":{\"keccak256\":\"0xd92910b862581523ad4e9b99f0bf738f4e62700a5e305953c7fda7db2cfd0f73\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://799f3f0f30822ac806800bbe7fe63b9991490c4e1d9edb75f5993d9350320819\",\"dweb:/ipfs/QmT8T4SokW6YxpDJQiafpeYNRGtjC5gFHxRqKTRXRyP6zB\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol\":{\"keccak256\":\"0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215\",\"dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol\":{\"keccak256\":\"0xc8960b7d3e504e98883de33856a917a473c05034cd61880df2a60b5c47c214fe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://80542373fa695b68d65b4d7222e852df5bda035a82e86ee559336c93b2bf7051\",\"dweb:/ipfs/QmZgH14DPTnKfA5gMSTMiUa6ExuqFfAozmEtLXiWc1iDiw\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol\":{\"keccak256\":\"0xa602c8beaae2d9e2ab1ce585a54547a6d4da32d32e4d002d20ccba55b19258d8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ac6553b5b07788a0bb67cc53596837d795280233a9a5cb3a9b3e1fde56822f78\",\"dweb:/ipfs/QmVoHXoma4ZbPKVRJJRosvhipa4rtCMU9QQvWHWKiRUxvi\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318\",\"dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79\",\"dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol\":{\"keccak256\":\"0x2659248df25e34000ed214b3dc8da2160bc39874c992b477d9e2b1b3283dc073\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c345af1b0e7ea28d1216d6a04ab28f5534a5229b9edf9ca3cd0e84950ae58d26\",\"dweb:/ipfs/QmY63jtSrYpLRe8Gj1ep2vMDCKxGNNG3hnNVKBVnrs2nmA\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol\":{\"keccak256\":\"0x4d43ed4b9ff9e4c671274976d59a58dbcc7b69bd7ac11b1710f5b7607cf15b74\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0b47b42998f675cb6a51f2e74ef5906a6fa63ec6718f3fd56ee035d6f77143f9\",\"dweb:/ipfs/QmREnAXqPJBvAwfWfDzaFhNfSRWF4Jdy9ZrpHLw1KdQweY\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol\":{\"keccak256\":\"0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834\",\"dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol\":{\"keccak256\":\"0x62dc9346044aabf22d78541bd495aa6ca05a7f5100aed26196ba35d40b59fcb5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5221df4501c74cd4493fee1a0f0788e02c4dc78c3c601e9f557f557c5a53ea92\",\"dweb:/ipfs/QmZpzyYY9dKLrgvYhXSHT93jwqb1UGvtGNMQk5dpECY5pa\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5\",\"dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Nonces.sol\":{\"keccak256\":\"0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e\",\"dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/Pausable.sol\":{\"keccak256\":\"0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c\",\"dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8\"]},\"lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol\":{\"keccak256\":\"0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35\",\"dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/Strings.sol\":{\"keccak256\":\"0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211\",\"dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4\",\"dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol\":{\"keccak256\":\"0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f\",\"dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol\":{\"keccak256\":\"0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59\",\"dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol\":{\"keccak256\":\"0x41ddfafe0d00dc22e35119d41cb0ca93673960689d35710fd12875139e64bd9f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://49d90142e15cdc4ca00de16e1882fa0a0daad8b46403628beb90c67a3efe4fc4\",\"dweb:/ipfs/QmNizYnFNcGixHxsknEccr2cQWyyQBqFF7h2bXLmefQz6M\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03\",\"dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ\"]},\"lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol\":{\"keccak256\":\"0x743aa2d21f6c26885e0aa6a1c84f7f7bc58fbd6df6bab32bed23f1a41f50454a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a651d38b4261840d3744e571edf2b59455352a8c7dac5d35b019afefa343ea3b\",\"dweb:/ipfs/QmSy3UkTCQDYTjKtGwtqPRrXaofcqtVZxaF6j1dV44wqvr\"]},\"lib/openzeppelin-contracts/contracts/utils/types/Time.sol\":{\"keccak256\":\"0x36776530f012618bc7526ceb28e77b85e582cb12d9b9466a71d4bd6bf952e4cc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f867d046908497287d8a67643dd5d7e38c4027af4ab0a74ffbe1d6790c383c6\",\"dweb:/ipfs/QmQ7s9gMP1nkwThFmoDifnGgpUMsMe5q5ZrAxGDsNnRGza\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Base.sol\":{\"keccak256\":\"0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224\",\"dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe\",\"dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1\",\"dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e\",\"dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c\",\"dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"test/GeometricTWAPSpecTests.sol\":{\"keccak256\":\"0x5ac0b32a1e22e050aea7852384ce67b6c06a56b54d493bd2a4d13badac94ba02\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://abafb29dbb887de3503576b9845b6ff64035fe0189bdb05fd76f983b5c672f16\",\"dweb:/ipfs/QmUrMEnRcwy24UDCjkRSYwVB2NiKRhGpZAd4LZ7tJxaKDo\"]},\"test/shared/GeometricTWAPTestFixture.sol\":{\"keccak256\":\"0x66a316a13c74698cbb99bd7f5681b6879eb300c7c2331095458be9d6b39bc386\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://cb294f187ac85de17d052e9cc9e91bfbf13c1d9d50fcaa8231fd0e3c9e4be280\",\"dweb:/ipfs/QmbZyjygUHe6Ss5sZrHjUm5jRBLSCLqCFRURjkAsGWaupu\"]},\"test/shared/StubErc20.sol\":{\"keccak256\":\"0xf3508dc98ae444d142d9993c52cebd856aba40c3e53d64bfeb63e71d190b12ee\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0cc01d254b6d5569d1cb426250db9df1b01afde9dd7b52e1efa0691112fcd918\",\"dweb:/ipfs/QmPnL9wFpSKXprrEFS9kkC2WzK2kAgWSH1snom1wiorCxn\"]},\"test/shared/utilities.sol\":{\"keccak256\":\"0xc64b147bbe73bf59fdec4202c5b7c5dbcadd7550f4b2ea2390ea689e194d7cb8\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://ab03a14b75d4b9df7795eeefd7e6d4a1d7af7b58ce948741cdd5d056a2c30416\",\"dweb:/ipfs/QmShemddxGaLyTGtC3yLdMtdHf9Gj3H8rjf2umzbFmP6aG\"]},\"test/utils/DepletedAssetUtils.sol\":{\"keccak256\":\"0x2273187d5eb782fb341d44265bd6e8afcef18ab3cfabcb4a0b77a75f15298c42\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://04d0a060b217603f6c7e38efc49be20932f08c56e46b1f9cea54eb722595458e\",\"dweb:/ipfs/QmdJHgaFbbtGDHPpHEFucTvrj4p4LT1piMPjtbrWBMXzAR\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "type": "error", "name": "InvalidIntervalConfig"}, {"inputs": [{"internalType": "uint8", "name": "bits", "type": "uint8"}, {"internalType": "int256", "name": "value", "type": "int256"}], "type": "error", "name": "SafeCastOverflowedIntDowncast"}, {"inputs": [{"internalType": "int16", "name": "lendingStateTick", "type": "int16", "indexed": false}], "type": "event", "name": "UpdateLendingTick", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "setUp"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testBlockUpdateBeforeInitialization"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_AfterOneLapAroundLongTermBuffer"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_AfterOneLapAroundMidTermBuffer"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_AfterOneLongTermInterval"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_AfterTwoUpdates"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_ChangeLongTermIntervalBeforeFullCycle"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_DecrementLongTermConfigToLowerThanCurrentIndex"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_FullyLoadBufferAfterIntervalChange"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_InitializeWithOneUpdate"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_InvalidLongTermIntervalConfig"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_InvalidMidTermIntervalConfig"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_LendingStateTickFirstCallNotAvailable"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_LendingStateTickSameBlockCallUnchanged"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_LendingStateTick_NotAvailable"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_LendingStateUpdateForOneBlock"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_LendingStateUpdateForTwoBlocks"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_LongTermBufferBlocksWithMissingBlocks"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_LongTermTickChangesAfterConfigMovedUp"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_MidTermIntervalConfigs"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_ModifyLongTermIntervalWithMissingBlocks"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_NegativeTicks"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_NineDaysLongTermBuffer"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_OneMidTermRoundMissedBlock"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_Outlier_MaxTickDelta"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_Outlier_PriceGoesDown"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_Outlier_PriceGoesUp"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_PartialLoadBufferAfterIntervalChange"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_PositiveTicks"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_PriceMovementDown"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_PriceMovementUp"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_ReturnsSameValueAfterConfigMovedDown"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_SimpleTicksWithMissingBlocks"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_TickRange_LongTermIndexAsFactor"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_TickRange_LongTermIndexAsFactorWithMissingBlocks"}, {"inputs": [{"internalType": "int16", "name": "long", "type": "int16"}], "stateMutability": "pure", "type": "function", "name": "testGeometricTWAP_TickRange_MaxTickBound_LongMin_Fuzz"}, {"inputs": [{"internalType": "int16", "name": "mid", "type": "int16"}], "stateMutability": "pure", "type": "function", "name": "testGeometricTWAP_TickRange_MaxTickBound_MidMin_Fuzz"}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "testGeometricTWAP_TickRange_MinAndMaxTickBound"}, {"inputs": [{"internalType": "int16", "name": "long", "type": "int16"}], "stateMutability": "pure", "type": "function", "name": "testGeometricTWAP_TickRange_MinTickBound_LongMax_Fuzz"}, {"inputs": [{"internalType": "int16", "name": "mid", "type": "int16"}], "stateMutability": "pure", "type": "function", "name": "testGeometricTWAP_TickRange_MinTickBound_MidMax_Fuzz"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_TickRange_PriceDownwards"}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "testGeometricTWAP_TickRange_PriceMovementSharplyDown"}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "testGeometricTWAP_TickRange_PriceMovementSharplyUp"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_TickRange_PriceUpwards"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_TickRange_VerifyTicksAfterIntervalChange"}, {"inputs": [], "stateMutability": "pure", "type": "function", "name": "testGeometricTWAP_TickRanges"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_WithOneMissedBlock"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_WithOneMissedBlockAroundLongTermCycle"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_WithOneMissedBlockAroundMidTermCycle"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testGeometricTWAP_firstBlock"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testMinimumLongTermTimeUpdateConfigInit"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testMinimumLongTermTimeUpdateConfigUpdate"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "testOverFlowOfCumulativeTick"}], "devdoc": {"kind": "dev", "methods": {"testOverFlowOfCumulativeTick()": {"details": "There is no assertion here, but the test runs for the 2000 years to      verify there is no overflow in the cumulative tick or timestamp."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"testGeometricTWAP_TickRange_MaxTickBound_LongMin_Fuzz(int16)": {"notice": "Considering   ```math   currentTick + delta + \\frac{(LONG_TERM_ARRAY_LAST_INDEX - factor) \\cdot LOG_BASE_OF_ROOT_TWO}{LONG_TERM_ARRAY_LAST_INDEX} < TickMath.MAX_TICK   ``` with $factor = 5$, $delta = spot - long$,   ```math   2 \\cdot spot - TickMath.MAX_TICK + \\frac{(LONG_TERM_ARRAY_LAST_INDEX - factor) \\cdot LOG_BASE_OF_ROOT_TWO}{LONG_TERM_ARRAY_LAST_INDEX} < long"}, "testGeometricTWAP_TickRange_MinAndMaxTickBound()": {"notice": "delta > TickMath.MAX_TICK - currentTick delta > currentTick - TickMath.MIN_TICK factor = 8 The result is `TickMath.MIN_TICK` & `TickMath.MAX_TICK` which would pass both the if conditions for tick range bounds check."}, "testGeometricTWAP_TickRange_MinTickBound_LongMax_Fuzz(int16)": {"notice": "Considering,   ```math   TickMath.MIN_TICK < currentTick - delta - \\frac{(LONG_TERM_ARRAY_LAST_INDEX - factor) \\cdot LOG_BASE_OF_ROOT_TWO}{LONG_TERM_ARRAY_LAST_INDEX}   ``` with $factor = 3$, $delta = long - spot$,   ```math   long < 2 \\cdot spot - \\frac{(LONG_TERM_ARRAY_LAST_INDEX - factor) \\cdot LOG_BASE_OF_ROOT_TWO}{LONG_TERM_ARRAY_LAST_INDEX} - TickMath.MIN_TICK   ``` Here we test long where the above does not hold."}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/GeometricTWAPSpecTests.sol": "GeometricTWAPSpecTests"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/AmmalgamPair.sol": {"keccak256": "0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4", "urls": ["bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2", "dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS"], "license": null}, "contracts/SaturationAndGeometricTWAPState.sol": {"keccak256": "0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419", "urls": ["bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76", "dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE"], "license": "GPL-3.0-only"}, "contracts/factories/AmmalgamFactory.sol": {"keccak256": "0xe0d9baf63d9538a7ecb8bd24ea61a8cdf6fc9c1e9eb028f343548adeb8b93e4e", "urls": ["bzz-raw://bfca24db47fbbaeef8bc36996cbfed78eb48771ac71d2800f081fb58a8e8c92b", "dweb:/ipfs/QmUfYEwfmrjSmchqXi58SnpSina4qKQvD2Jbk5RqYsaoCa"], "license": "GPL-3.0-only"}, "contracts/interfaces/IAmmalgamPair.sol": {"keccak256": "0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337", "urls": ["bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795", "dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw"], "license": "GPL-3.0-only"}, "contracts/interfaces/ISaturationAndGeometricTWAPState.sol": {"keccak256": "0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c", "urls": ["bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20", "dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/IAmmalgamCallee.sol": {"keccak256": "0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339", "urls": ["bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d", "dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/ITransferValidator.sol": {"keccak256": "0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56", "urls": ["bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4", "dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IAmmalgamFactory.sol": {"keccak256": "0x1c80089901e8d7d7451775b5eaa92092eb2b65319cb92fa7884281bae49f52b8", "urls": ["bzz-raw://bf1201399bb9d5046e0e788ff88394b2468771096a2a0d3500af542923e84628", "dweb:/ipfs/QmeUaPyQpbPbP5fyPUT2FfzeDgHcdyQAn1DaNg9uCuGoj9"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IFactoryCallback.sol": {"keccak256": "0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52", "urls": ["bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b", "dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/INewTokensFactory.sol": {"keccak256": "0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9", "urls": ["bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b", "dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/GeometricTWAP.sol": {"keccak256": "0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e", "urls": ["bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa", "dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz"], "license": "GPL-3.0-only"}, "contracts/libraries/Interest.sol": {"keccak256": "0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646", "urls": ["bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194", "dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX"], "license": "GPL-3.0-only"}, "contracts/libraries/Liquidation.sol": {"keccak256": "0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63", "urls": ["bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877", "dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW"], "license": "GPL-3.0-only"}, "contracts/libraries/QuadraticSwapFees.sol": {"keccak256": "0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb", "urls": ["bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb", "dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3"], "license": "GPL-3.0-only"}, "contracts/libraries/Saturation.sol": {"keccak256": "0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860", "urls": ["bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20", "dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE"], "license": "GPL-3.0-only"}, "contracts/libraries/TickMath.sol": {"keccak256": "0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12", "urls": ["bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20", "dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG"], "license": "GPL-2.0-or-later"}, "contracts/libraries/Uint16Set.sol": {"keccak256": "0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f", "urls": ["bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06", "dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy"], "license": "GPL-3.0-only"}, "contracts/libraries/Validation.sol": {"keccak256": "0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463", "urls": ["bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778", "dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "contracts/tokens/TokenController.sol": {"keccak256": "0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6", "urls": ["bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159", "dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn"], "license": "GPL-3.0-only"}, "lib/mangrove-core/lib/core/BitLib.sol": {"keccak256": "0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3", "urls": ["bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8", "dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr"], "license": "MIT"}, "lib/morpho-blue/src/libraries/MathLib.sol": {"keccak256": "0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a", "urls": ["bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08", "dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd"], "license": "GPL-2.0-or-later"}, "lib/openzeppelin-contracts/contracts/access/AccessControl.sol": {"keccak256": "0xb64ecf1154f183412bcde47168f3af245e4120846346a0b3872c631e361156d2", "urls": ["bzz-raw://85331049a60659bc4489733ccd3cbeb177b65691122a8cb637bf9267ab25e23d", "dweb:/ipfs/QmbGpDcdwKTirzSCoZfE4rHG7jBSWsE4K2iSb6UCYXtLJv"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x5643a5cadd1278581308b20becb48a50946c159fc31c29fc407ea9a61fc865d1", "urls": ["bzz-raw://c7d79f305a239207a24fa2174897c8ea8ff1e81cb790d440fd54c89a0e85f63e", "dweb:/ipfs/QmT847eeAMnRN3DaG1zsKNMn7qipNAidqv1REnKexPkrfA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol": {"keccak256": "0xb541b133f2d85eb37ae866cb21123be93bd3671b6840c47f951da52d3a704548", "urls": ["bzz-raw://22443f43ece107eaf453aa9a41a59410cece5429c2779c794fbd2c8b5aa29d38", "dweb:/ipfs/Qmbum2jwLYuT9aZ2fr9NMLwWFsVavonrGm2VnbAL9hP2jn"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol": {"keccak256": "0x3f91c79d6f55db9e4fc36e1cfe6a483a7b0f5be60fecbd979555071673746d47", "urls": ["bzz-raw://9b1e3c64cbeb2757a2a1a45c69f7f3984a93b0eadd1016341b64f9d94f89d7c4", "dweb:/ipfs/QmP1Mj14U4vMTFa2rv2nodMbWSCov2ac9Md8W2aUcgYdKX"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da", "urls": ["bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41", "dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b", "urls": ["bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb", "dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol": {"keccak256": "0xad94c8d7246a50210f7bcb54e5b91fc9f1c6e137263ac972ca5dd0f7f6d4d49d", "urls": ["bzz-raw://6938e96fbb0cf3d961788b6c3522400e255d8057d1b9f7e08a50e0b48486b007", "dweb:/ipfs/QmNXG3MPzDXjHJ9iWDYCz4vi9RBTgVBnZjndnfBwMfhkyD"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol": {"keccak256": "0xd92910b862581523ad4e9b99f0bf738f4e62700a5e305953c7fda7db2cfd0f73", "urls": ["bzz-raw://799f3f0f30822ac806800bbe7fe63b9991490c4e1d9edb75f5993d9350320819", "dweb:/ipfs/QmT8T4SokW6YxpDJQiafpeYNRGtjC5gFHxRqKTRXRyP6zB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol": {"keccak256": "0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad", "urls": ["bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215", "dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol": {"keccak256": "0xc8960b7d3e504e98883de33856a917a473c05034cd61880df2a60b5c47c214fe", "urls": ["bzz-raw://80542373fa695b68d65b4d7222e852df5bda035a82e86ee559336c93b2bf7051", "dweb:/ipfs/QmZgH14DPTnKfA5gMSTMiUa6ExuqFfAozmEtLXiWc1iDiw"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol": {"keccak256": "0xa602c8beaae2d9e2ab1ce585a54547a6d4da32d32e4d002d20ccba55b19258d8", "urls": ["bzz-raw://ac6553b5b07788a0bb67cc53596837d795280233a9a5cb3a9b3e1fde56822f78", "dweb:/ipfs/QmVoHXoma4ZbPKVRJJRosvhipa4rtCMU9QQvWHWKiRUxvi"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9", "urls": ["bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318", "dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db", "urls": ["bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79", "dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol": {"keccak256": "0x2659248df25e34000ed214b3dc8da2160bc39874c992b477d9e2b1b3283dc073", "urls": ["bzz-raw://c345af1b0e7ea28d1216d6a04ab28f5534a5229b9edf9ca3cd0e84950ae58d26", "dweb:/ipfs/QmY63jtSrYpLRe8Gj1ep2vMDCKxGNNG3hnNVKBVnrs2nmA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol": {"keccak256": "0x4d43ed4b9ff9e4c671274976d59a58dbcc7b69bd7ac11b1710f5b7607cf15b74", "urls": ["bzz-raw://0b47b42998f675cb6a51f2e74ef5906a6fa63ec6718f3fd56ee035d6f77143f9", "dweb:/ipfs/QmREnAXqPJBvAwfWfDzaFhNfSRWF4Jdy9ZrpHLw1KdQweY"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol": {"keccak256": "0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8", "urls": ["bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834", "dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol": {"keccak256": "0x62dc9346044aabf22d78541bd495aa6ca05a7f5100aed26196ba35d40b59fcb5", "urls": ["bzz-raw://5221df4501c74cd4493fee1a0f0788e02c4dc78c3c601e9f557f557c5a53ea92", "dweb:/ipfs/QmZpzyYY9dKLrgvYhXSHT93jwqb1UGvtGNMQk5dpECY5pa"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2", "urls": ["bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5", "dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Nonces.sol": {"keccak256": "0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f", "urls": ["bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e", "dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Pausable.sol": {"keccak256": "0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f", "urls": ["bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c", "dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol": {"keccak256": "0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402", "urls": ["bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35", "dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"keccak256": "0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52", "urls": ["bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211", "dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5", "urls": ["bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4", "dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol": {"keccak256": "0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e", "urls": ["bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f", "dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol": {"keccak256": "0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631", "urls": ["bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59", "dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol": {"keccak256": "0x41ddfafe0d00dc22e35119d41cb0ca93673960689d35710fd12875139e64bd9f", "urls": ["bzz-raw://49d90142e15cdc4ca00de16e1882fa0a0daad8b46403628beb90c67a3efe4fc4", "dweb:/ipfs/QmNizYnFNcGixHxsknEccr2cQWyyQBqFF7h2bXLmefQz6M"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3", "urls": ["bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03", "dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol": {"keccak256": "0x743aa2d21f6c26885e0aa6a1c84f7f7bc58fbd6df6bab32bed23f1a41f50454a", "urls": ["bzz-raw://a651d38b4261840d3744e571edf2b59455352a8c7dac5d35b019afefa343ea3b", "dweb:/ipfs/QmSy3UkTCQDYTjKtGwtqPRrXaofcqtVZxaF6j1dV44wqvr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/types/Time.sol": {"keccak256": "0x36776530f012618bc7526ceb28e77b85e582cb12d9b9466a71d4bd6bf952e4cc", "urls": ["bzz-raw://9f867d046908497287d8a67643dd5d7e38c4027af4ab0a74ffbe1d6790c383c6", "dweb:/ipfs/QmQ7s9gMP1nkwThFmoDifnGgpUMsMe5q5ZrAxGDsNnRGza"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Base.sol": {"keccak256": "0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c", "urls": ["bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224", "dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol": {"keccak256": "0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270", "urls": ["bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe", "dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol": {"keccak256": "0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b", "urls": ["bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1", "dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Test.sol": {"keccak256": "0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe", "urls": ["bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e", "dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol": {"keccak256": "0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354", "urls": ["bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c", "dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC"], "license": "MIT OR Apache-2.0"}, "lib/openzeppelin-contracts/lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "test/GeometricTWAPSpecTests.sol": {"keccak256": "0x5ac0b32a1e22e050aea7852384ce67b6c06a56b54d493bd2a4d13badac94ba02", "urls": ["bzz-raw://abafb29dbb887de3503576b9845b6ff64035fe0189bdb05fd76f983b5c672f16", "dweb:/ipfs/QmUrMEnRcwy24UDCjkRSYwVB2NiKRhGpZAd4LZ7tJxaKDo"], "license": "GPL-3.0-only"}, "test/shared/GeometricTWAPTestFixture.sol": {"keccak256": "0x66a316a13c74698cbb99bd7f5681b6879eb300c7c2331095458be9d6b39bc386", "urls": ["bzz-raw://cb294f187ac85de17d052e9cc9e91bfbf13c1d9d50fcaa8231fd0e3c9e4be280", "dweb:/ipfs/QmbZyjygUHe6Ss5sZrHjUm5jRBLSCLqCFRURjkAsGWaupu"], "license": "GPL-3.0-only"}, "test/shared/StubErc20.sol": {"keccak256": "0xf3508dc98ae444d142d9993c52cebd856aba40c3e53d64bfeb63e71d190b12ee", "urls": ["bzz-raw://0cc01d254b6d5569d1cb426250db9df1b01afde9dd7b52e1efa0691112fcd918", "dweb:/ipfs/QmPnL9wFpSKXprrEFS9kkC2WzK2kAgWSH1snom1wiorCxn"], "license": "MIT"}, "test/shared/utilities.sol": {"keccak256": "0xc64b147bbe73bf59fdec4202c5b7c5dbcadd7550f4b2ea2390ea689e194d7cb8", "urls": ["bzz-raw://ab03a14b75d4b9df7795eeefd7e6d4a1d7af7b58ce948741cdd5d056a2c30416", "dweb:/ipfs/QmShemddxGaLyTGtC3yLdMtdHf9Gj3H8rjf2umzbFmP6aG"], "license": "GPL-3.0-only"}, "test/utils/DepletedAssetUtils.sol": {"keccak256": "0x2273187d5eb782fb341d44265bd6e8afcef18ab3cfabcb4a0b77a75f15298c42", "urls": ["bzz-raw://04d0a060b217603f6c7e38efc49be20932f08c56e46b1f9cea54eb722595458e", "dweb:/ipfs/QmdJHgaFbbtGDHPpHEFucTvrj4p4LT1piMPjtbrWBMXzAR"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 120}