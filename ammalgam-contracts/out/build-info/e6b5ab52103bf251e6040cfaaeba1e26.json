{"id": "e6b5ab52103bf251e6040cfaaeba1e26", "source_id_to_path": {"0": "contracts/AmmalgamPair.sol", "1": "contracts/SaturationAndGeometricTWAPState.sol", "2": "contracts/factories/AmmalgamFactory.sol", "3": "contracts/factories/ERC20DebtLiquidityTokenFactory.sol", "4": "contracts/factories/ERC20LiquidityTokenFactory.sol", "5": "contracts/factories/ERC4626DebtTokenFactory.sol", "6": "contracts/factories/ERC4626DepositTokenFactory.sol", "7": "contracts/factories/NewTokensFactory.sol", "8": "contracts/interfaces/IAmmalgamPair.sol", "9": "contracts/interfaces/ISaturationAndGeometricTWAPState.sol", "10": "contracts/interfaces/callbacks/IAmmalgamCallee.sol", "11": "contracts/interfaces/callbacks/ITransferValidator.sol", "12": "contracts/interfaces/factories/IAmmalgamFactory.sol", "13": "contracts/interfaces/factories/IFactoryCallback.sol", "14": "contracts/interfaces/factories/INewTokensFactory.sol", "15": "contracts/interfaces/factories/ITokenFactory.sol", "16": "contracts/interfaces/tokens/IAmmalgamERC20.sol", "17": "contracts/interfaces/tokens/IERC20DebtToken.sol", "18": "contracts/interfaces/tokens/IPluginRegistry.sol", "19": "contracts/interfaces/tokens/ITokenController.sol", "20": "contracts/libraries/Convert.sol", "21": "contracts/libraries/GeometricTWAP.sol", "22": "contracts/libraries/Interest.sol", "23": "contracts/libraries/Liquidation.sol", "24": "contracts/libraries/QuadraticSwapFees.sol", "25": "contracts/libraries/Saturation.sol", "26": "contracts/libraries/TickMath.sol", "27": "contracts/libraries/TokenSymbol.sol", "28": "contracts/libraries/Uint16Set.sol", "29": "contracts/libraries/Validation.sol", "30": "contracts/libraries/constants.sol", "31": "contracts/tokens/ERC20Base.sol", "32": "contracts/tokens/ERC20DebtBase.sol", "33": "contracts/tokens/ERC20DebtLiquidityToken.sol", "34": "contracts/tokens/ERC20LiquidityToken.sol", "35": "contracts/tokens/ERC4626DebtToken.sol", "36": "contracts/tokens/ERC4626DepositToken.sol", "37": "contracts/tokens/PluginRegistry.sol", "38": "contracts/tokens/TokenController.sol", "39": "contracts/utils/deployHelper.sol", "40": "lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol", "41": "lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol", "42": "lib/1inch/solidity-utils/contracts/libraries/RevertReasonParser.sol", "43": "lib/1inch/solidity-utils/contracts/libraries/StringUtil.sol", "44": "lib/1inch/token-plugins/contracts/ERC20Hooks.sol", "45": "lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol", "46": "lib/1inch/token-plugins/contracts/interfaces/IHook.sol", "47": "lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol", "48": "lib/ExcessivelySafeCall/src/ExcessivelySafeCall.sol", "49": "lib/mangrove-core/lib/core/BitLib.sol", "50": "lib/morpho-blue/src/libraries/MathLib.sol", "51": "lib/openzeppelin-contracts/contracts/access/AccessControl.sol", "52": "lib/openzeppelin-contracts/contracts/access/IAccessControl.sol", "53": "lib/openzeppelin-contracts/contracts/access/Ownable.sol", "54": "lib/openzeppelin-contracts/contracts/governance/utils/IVotes.sol", "55": "lib/openzeppelin-contracts/contracts/governance/utils/Votes.sol", "56": "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol", "57": "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol", "58": "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol", "59": "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashBorrower.sol", "60": "lib/openzeppelin-contracts/contracts/interfaces/IERC3156FlashLender.sol", "61": "lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol", "62": "lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol", "63": "lib/openzeppelin-contracts/contracts/interfaces/IERC5805.sol", "64": "lib/openzeppelin-contracts/contracts/interfaces/IERC6372.sol", "65": "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol", "66": "lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol", "67": "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol", "68": "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol", "69": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Burnable.sol", "70": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20FlashMint.sol", "71": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol", "72": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Votes.sol", "73": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol", "74": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol", "75": "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol", "76": "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol", "77": "lib/openzeppelin-contracts/contracts/utils/Context.sol", "78": "lib/openzeppelin-contracts/contracts/utils/Nonces.sol", "79": "lib/openzeppelin-contracts/contracts/utils/Panic.sol", "80": "lib/openzeppelin-contracts/contracts/utils/Pausable.sol", "81": "lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol", "82": "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol", "83": "lib/openzeppelin-contracts/contracts/utils/Strings.sol", "84": "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol", "85": "lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol", "86": "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol", "87": "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol", "88": "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol", "89": "lib/openzeppelin-contracts/contracts/utils/math/Math.sol", "90": "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol", "91": "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol", "92": "lib/openzeppelin-contracts/contracts/utils/structs/Checkpoints.sol", "93": "lib/openzeppelin-contracts/contracts/utils/types/Time.sol", "94": "lib/openzeppelin-contracts/lib/forge-std/src/Base.sol", "95": "lib/openzeppelin-contracts/lib/forge-std/src/Script.sol", "96": "lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol", "97": "lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol", "98": "lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol", "99": "lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol", "100": "lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol", "101": "lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol", "102": "lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol", "103": "lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol", "104": "lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol", "105": "lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol", "106": "lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol", "107": "lib/openzeppelin-contracts/lib/forge-std/src/Test.sol", "108": "lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol", "109": "lib/openzeppelin-contracts/lib/forge-std/src/console.sol", "110": "lib/openzeppelin-contracts/lib/forge-std/src/console2.sol", "111": "lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol", "112": "lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol", "113": "script/ForkDeploy.s.sol", "114": "script/TestnetDeploy.s.sol", "115": "test/AmmalgamFactoryTest.sol", "116": "test/ConvertSharesAndAssetsUnitTests.sol", "117": "test/GeometricTWAPInteractionFuzzTests.sol", "118": "test/GeometricTWAPInteractionTests.sol", "119": "test/GeometricTWAPSpecFuzzTests.sol", "120": "test/GeometricTWAPSpecTests.sol", "121": "test/InterestTests/InterestBorrowRepayLiquidityFuzz.sol", "122": "test/InterestTests/InterestBorrowRepayLiquidityTests.sol", "123": "test/InterestTests/InterestFixture.sol", "124": "test/InterestTests/InterestGetReserveAtTickFuzz.sol", "125": "test/InterestTests/InterestLiquidityTests.sol", "126": "test/InterestTests/InterestSpecTests.sol", "127": "test/InterestTests/InterestUnitTests.sol", "128": "test/InterestTests/InterestXIntegrationTests.sol", "129": "test/InterestTests/InterestXYSpecTests.sol", "130": "test/InterestTests/InterestYIntegrationTests.sol", "131": "test/LiquidationTests.sol", "132": "test/PairTests/ActiveLiquidityScalerTest.sol", "133": "test/PairTests/AmmalgamFuzzTest.sol", "134": "test/PairTests/BorrowLiquidityTests.sol", "135": "test/PairTests/BorrowTests.sol", "136": "test/PairTests/BorrowUnderflowTest.sol", "137": "test/PairTests/DebtDelegationTests.sol", "138": "test/PairTests/DepletedAssetTests/ComputeCurveTests.sol", "139": "test/PairTests/DepletedAssetTests/DepletedAssetDepositTests.sol", "140": "test/PairTests/DepletedAssetTests/DepletedAssetMintTests.sol", "141": "test/PairTests/DepletedAssetTests/DepletedAssetRepayLiquidity.sol", "142": "test/PairTests/DepletedAssetTests/DepletedAssetRepayTests.sol", "143": "test/PairTests/DepletedAssetTests/DepletedAssetSwapTests.sol", "144": "test/PairTests/DepositTests.sol", "145": "test/PairTests/ExternalLiquidityTest.sol", "146": "test/PairTests/FlashBorrowLiquidityTests.sol", "147": "test/PairTests/FlashBorrowTests.sol", "148": "test/PairTests/LeverageTests.sol", "149": "test/PairTests/LoanToValueIntegrationTests.sol", "150": "test/PairTests/LoanToValueTests.sol", "151": "test/PairTests/MaxBorrowLimitTests.sol", "152": "test/PairTests/MaxWithdrawLimitTests.sol", "153": "test/PairTests/MintFeeTest.sol", "154": "test/PairTests/RepayLiquidityTests.sol", "155": "test/PairTests/RepayTests.sol", "156": "test/PairTests/SwapFeeVsBorrowedInterestTests.sol", "157": "test/PairTests/SwapTests/QuadraticSwapFeeIntegrationTests.sol", "158": "test/PairTests/SwapTests/QuadraticSwapFeesTests.sol", "159": "test/PairTests/WithdrawTests.sol", "160": "test/Saturation/SaturationIntegrationTests.sol", "161": "test/Saturation/SaturationTestUtils.sol", "162": "test/Saturation/SaturationUnitTests.sol", "163": "test/TickMathFuzzTest.sol", "164": "test/TickMathSpecTests.sol", "165": "test/TokenSymbolTest.sol", "166": "test/TokenTests/AmmalgamERC20Test.sol", "167": "test/TokenTests/DebtTokenTests.sol", "168": "test/TokenTests/ERC20MetaDataTests.sol", "169": "test/TokenTests/ERC4626DepositTest.sol", "170": "test/TransientStorageTests.sol", "171": "test/UniLegacyPairTests.sol", "172": "test/example/PeripheralDelegationContractExample.sol", "173": "test/shared/DelegationTestFixture.sol", "174": "test/shared/ERC1155Receiver.sol", "175": "test/shared/FactoryPairTestFixture.sol", "176": "test/shared/GeometricTWAPTestFixture.sol", "177": "test/shared/StubErc20.sol", "178": "test/shared/StubValidator.sol", "179": "test/shared/TestERC20.sol", "180": "test/shared/utilities.sol", "181": "test/stubs/ReturnBombAttackStub.sol", "182": "test/stubs/TokenWithBytes32SymbolStub.sol", "183": "test/stubs/TokenWithNoSymbolStub.sol", "184": "test/stubs/TokenWithStringCAPSSymbolStub.sol", "185": "test/stubs/TokenWithStringSymbolStub.sol", "186": "test/utils/DepletedAssetUtils.sol", "187": "test/utils/constants.sol"}, "language": "Solidity"}