{"abi": [{"type": "constructor", "inputs": [{"name": "_pair", "type": "address", "internalType": "contract IAmmalgamPair"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "IS_TEST", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "approveDebtTransferAndDelegation", "inputs": [{"name": "onBehalfOf", "type": "address", "internalType": "address"}, {"name": "delegatee", "type": "address", "internalType": "address"}, {"name": "tokenType", "type": "uint256", "internalType": "uint256"}, {"name": "approveAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "inputs": [{"name": "onBehalfOf", "type": "address", "internalType": "address"}, {"name": "delegatee", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "amountX", "type": "uint256", "internalType": "uint256"}, {"name": "amountY", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "delegateBorrowLiquidity", "inputs": [{"name": "onBehalfOf", "type": "address", "internalType": "address"}, {"name": "delegatee", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "delegationContract", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IDebtDelegation"}], "stateMutability": "view"}, {"type": "function", "name": "excludeArtifacts", "inputs": [], "outputs": [{"name": "excludedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeContracts", "inputs": [], "outputs": [{"name": "excludedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "excludeSelectors", "inputs": [], "outputs": [{"name": "excludedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "excludeSenders", "inputs": [], "outputs": [{"name": "excludedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "failed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "getDebtToken", "inputs": [{"name": "tokenType", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "contract IERC20DebtToken"}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifactSelectors", "inputs": [], "outputs": [{"name": "targetedArtifactSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzArtifactSelector[]", "components": [{"name": "artifact", "type": "string", "internalType": "string"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetArtifacts", "inputs": [], "outputs": [{"name": "targetedArtifacts_", "type": "string[]", "internalType": "string[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetContracts", "inputs": [], "outputs": [{"name": "targetedContracts_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "targetInterfaces", "inputs": [], "outputs": [{"name": "targetedInterfaces_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzInterface[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "artifacts", "type": "string[]", "internalType": "string[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSelectors", "inputs": [], "outputs": [{"name": "targetedSelectors_", "type": "tuple[]", "internalType": "struct StdInvariant.FuzzSelector[]", "components": [{"name": "addr", "type": "address", "internalType": "address"}, {"name": "selectors", "type": "bytes4[]", "internalType": "bytes4[]"}]}], "stateMutability": "view"}, {"type": "function", "name": "targetSenders", "inputs": [], "outputs": [{"name": "targetedSenders_", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "verifyDelegationContractIsEmpty", "inputs": [{"name": "tokenType", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "view"}, {"type": "event", "name": "log", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_address", "inputs": [{"name": "", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_array", "inputs": [{"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_bytes", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_bytes32", "inputs": [{"name": "", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_int", "inputs": [{"name": "", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_address", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256[]", "indexed": false, "internalType": "uint256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256[]", "indexed": false, "internalType": "int256[]"}], "anonymous": false}, {"type": "event", "name": "log_named_array", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "address[]", "indexed": false, "internalType": "address[]"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "log_named_bytes32", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_decimal_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "decimals", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_named_int", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "log_named_string", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_named_uint", "inputs": [{"name": "key", "type": "string", "indexed": false, "internalType": "string"}, {"name": "val", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "log_string", "inputs": [{"name": "", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "log_uint", "inputs": [{"name": "", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "logs", "inputs": [{"name": "", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}], "bytecode": {"object": "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", "sourceMap": "427:1657:173:-:0;;;3126:44:97;;;3166:4;-1:-1:-1;;3126:44:97;;;;;;;;1016:26:107;;;;;;;;;;;552:148:173;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;609:4;:12;;-1:-1:-1;;;;;;609:12:173;;-1:-1:-1;;;;;609:12:173;;;;;;652:41;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;631:18:173;:62;;-1:-1:-1;;;;;;631:62:173;-1:-1:-1;;;;;631:62:173;;;;;;;;;;-1:-1:-1;427:1657:173;;;;;;;;;;:::o;14:312:188:-;106:6;159:2;147:9;138:7;134:23;130:32;127:52;;;175:1;172;165:12;127:52;201:16;;-1:-1:-1;;;;;246:31:188;;236:42;;226:70;;292:1;289;282:12;226:70;315:5;14:312;-1:-1:-1;;;14:312:188:o;:::-;427:1657:173;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "427:1657:173:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;504:41;;;;;-1:-1:-1;;;;;504:41:173;;;;;;-1:-1:-1;;;;;204:32:188;;;186:51;;174:2;159:18;504:41:173;;;;;;;;2907:134:100;;;:::i;:::-;;;;;;;:::i;3823:151::-;;;:::i;:::-;;;;;;;:::i;3684:133::-;;;:::i;3385:141::-;;;:::i;877:428:173:-;;;;;;:::i;:::-;;:::i;:::-;;1590:217;;;;;;:::i;:::-;;:::i;3193:186:100:-;;;:::i;:::-;;;;;;;:::i;3047:140::-;;;:::i;:::-;;;;;;;:::i;3532:146::-;;;:::i;:::-;;;;;;;:::i;2754:147::-;;;:::i;1311:273:173:-;;;;;;:::i;:::-;;:::i;2459:141:100:-;;;:::i;1243:204:96:-;;;:::i;:::-;;;8609:14:188;;8602:22;8584:41;;8572:2;8557:18;1243:204:96;8444:187:188;706:165:173;;;;;;:::i;:::-;;:::i;2606:142:100:-;;;:::i;1016:26:107:-;;;;;;;;;1813:269:173;;;;;;:::i;:::-;;:::i;2907:134:100:-;2954:33;3018:16;2999:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2999:35:100;;;;;;;;;;;;;;;;;;;;;;;2907:134;:::o;3823:151::-;3872:42;3948:19;3926:41;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3926:41:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3823:151;:::o;3684:133::-;3730:33;3794:16;3775:35;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3775:35:100;;;;;;;;;;;;;;;;;;;;;;3684:133;:::o;3385:141::-;3433:35;3501:18;3480:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3480:39:100;;;;;;;;;;;;;;;;;;;;;;3385:141;:::o;877:428:173:-;1055:25;1083:23;1096:9;1083:12;:23::i;:::-;1117:25;;-1:-1:-1;;;1117:25:173;;-1:-1:-1;;;;;204:32:188;;1117:25:173;;;186:51:188;1055::173;;-1:-1:-1;1117:13:173;;;;159:18:188;;1117:25:173;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1182:18:173;;1152:65;;-1:-1:-1;;;1152:65:173;;-1:-1:-1;;;;;1182:18:173;;;1152:65;;;9866:51:188;9933:18;;;9926:34;;;1152:21:173;;;;-1:-1:-1;1152:21:173;;-1:-1:-1;9839:18:188;;1152:65:173;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;1227:18:173;;:47;;-1:-1:-1;;;1227:47:173;;-1:-1:-1;;;;;204:32:188;;;1227:47:173;;;186:51:188;1227:18:173;;;;:36;;159:18:188;;1227:47:173;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;317:28:94;309:37;;-1:-1:-1;;;;;1284:12:173;;:14;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1045:260;877:428;;;;:::o;1590:217::-;1707:19;;-1:-1:-1;;;1707:19:173;;-1:-1:-1;;;;;204:32:188;;1707:19:173;;;186:51:188;1707:8:173;;;;159:18:188;;1707:19:173;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1736:18:173;;1787:4;;1736:64;;-1:-1:-1;;;1736:64:173;;-1:-1:-1;;;;;10524:32:188;;;1736:64:173;;;10506:51:188;10593:32;;;10573:18;;;10566:60;1736:18:173;1787:4;;;;;10642:18:188;;;10635:60;10711:18;;;10704:34;;;1736:18:173;;;-1:-1:-1;1736:34:173;;-1:-1:-1;10478:19:188;;1736:64:173;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;1590:217;;;;:::o;3193:186:100:-;3249:56;3346:26;3317:55;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3317:55:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3047:140;3095:34;3162:18;3141:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3532:146;3580:40;3653:18;3632:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;3632:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2754:147;2803:40;2876:18;2855:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2855:39:100;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1311:273:173;1483:19;;-1:-1:-1;;;1483:19:173;;-1:-1:-1;;;;;204:32:188;;1483:19:173;;;186:51:188;1483:8:173;;;;159:18:188;;1483:19:173;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;1512:18:173;;1554:4;;1512:65;;-1:-1:-1;;;1512:65:173;;-1:-1:-1;;;;;11396:32:188;;;1512:65:173;;;11378:51:188;11465:32;;;11445:18;;;11438:60;1512:18:173;1554:4;;;;;11514:18:188;;;11507:60;11583:18;;;11576:34;;;11626:19;;;11619:35;;;1512:18:173;;;-1:-1:-1;1512:25:173;;-1:-1:-1;11350:19:188;;1512:65:173;;;;;;;;;;;;;;;;;;;2459:141:100;2508:34;2575:18;2554:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1243:204:96;1302:7;;1282:4;;1302:7;;1298:143;;;-1:-1:-1;1332:7:96;;;;;1243:204::o;1298:143::-;1377:39;;-1:-1:-1;;;1377:39:96;;:7;:39;;;9866:51:188;;;-1:-1:-1;;;9933:18:188;;;9926:34;1428:1:96;;1377:7;;9839:18:188;;1377:39:96;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;:53;;1370:60;;1243:204;:::o;706:165:173:-;840:4;;:22;;-1:-1:-1;;;840:22:173;;;;;12279:25:188;;;782:15:173;;840:4;;;-1:-1:-1;;;;;840:4:173;;:11;;12252:18:188;;840:22:173;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;809:55;706:165;-1:-1:-1;;706:165:173:o;2606:142:100:-;2655:35;2723:18;2702:39;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2702:39:100;;;;;;;;;;;;;;;;;;;;;;2606:142;:::o;1813:269:173:-;1909:25;1937:23;1950:9;1937:12;:23::i;:::-;2007:18;;1979:48;;-1:-1:-1;;;1979:48:173;;-1:-1:-1;;;;;2007:18:173;;;1979:48;;;186:51:188;1909::173;;-1:-1:-1;1970:105:173;;1979:19;;;;;;159:18:188;;1979:48:173;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2029:1;1970:105;;;;;;;;;;;;;;;;;:8;:105::i;:::-;1899:183;1813:269;:::o;2386:134:96:-;2484:29;;-1:-1:-1;;;2484:29:96;;:11;;;;:29;;2496:4;;2502:5;;2509:3;;2484:29;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2386:134;;;:::o;248:637:188:-;438:2;450:21;;;520:13;;423:18;;;542:22;;;390:4;;621:15;;;595:2;580:18;;;390:4;664:195;678:6;675:1;672:13;664:195;;;743:13;;-1:-1:-1;;;;;739:39:188;727:52;;808:2;834:15;;;;799:12;;;;775:1;693:9;664:195;;;-1:-1:-1;876:3:188;;248:637;-1:-1:-1;;;;;248:637:188:o;890:289::-;932:3;970:5;964:12;997:6;992:3;985:19;1053:6;1046:4;1039:5;1035:16;1028:4;1023:3;1019:14;1013:47;1105:1;1098:4;1089:6;1084:3;1080:16;1076:27;1069:38;1168:4;1161:2;1157:7;1152:2;1144:6;1140:15;1136:29;1131:3;1127:39;1123:50;1116:57;;;890:289;;;;:::o;1184:1628::-;1390:4;1438:2;1427:9;1423:18;1468:2;1457:9;1450:21;1491:6;1526;1520:13;1557:6;1549;1542:22;1595:2;1584:9;1580:18;1573:25;;1657:2;1647:6;1644:1;1640:14;1629:9;1625:30;1621:39;1607:53;;1695:2;1687:6;1683:15;1716:1;1726:1057;1740:6;1737:1;1734:13;1726:1057;;;-1:-1:-1;;1805:22:188;;;1801:36;1789:49;;1861:13;;1948:9;;-1:-1:-1;;;;;1944:35:188;1929:51;;2027:2;2019:11;;;2013:18;1913:2;2051:15;;;2044:27;;;2132:19;;1901:15;;;2164:24;;;2319:21;;;2222:2;2272:1;2268:16;;;2256:29;;2252:38;;;2210:15;;;;-1:-1:-1;2378:296:188;2394:8;2389:3;2386:17;2378:296;;;2500:2;2496:7;2487:6;2479;2475:19;2471:33;2464:5;2457:48;2532:42;2567:6;2556:8;2550:15;2532:42;:::i;:::-;2617:2;2603:17;;;;2522:52;;-1:-1:-1;2646:14:188;;;;;2422:1;2413:11;2378:296;;;-1:-1:-1;2697:6:188;;-1:-1:-1;;;2738:2:188;2761:12;;;;2726:15;;;;;-1:-1:-1;1762:1:188;1755:9;1726:1057;;;-1:-1:-1;2800:6:188;;1184:1628;-1:-1:-1;;;;;;1184:1628:188:o;2817:131::-;-1:-1:-1;;;;;2892:31:188;;2882:42;;2872:70;;2938:1;2935;2928:12;2872:70;2817:131;:::o;2953:629::-;3039:6;3047;3055;3063;3116:3;3104:9;3095:7;3091:23;3087:33;3084:53;;;3133:1;3130;3123:12;3084:53;3172:9;3159:23;3191:31;3216:5;3191:31;:::i;:::-;3241:5;-1:-1:-1;3298:2:188;3283:18;;3270:32;3311:33;3270:32;3311:33;:::i;:::-;2953:629;;3363:7;;-1:-1:-1;;;;3443:2:188;3428:18;;3415:32;;3546:2;3531:18;3518:32;;2953:629::o;3587:650::-;3673:6;3681;3689;3697;3750:3;3738:9;3729:7;3725:23;3721:33;3718:53;;;3767:1;3764;3757:12;3718:53;3806:9;3793:23;3825:31;3850:5;3825:31;:::i;:::-;3875:5;-1:-1:-1;3932:2:188;3917:18;;3904:32;3945:33;3904:32;3945:33;:::i;:::-;3997:7;-1:-1:-1;4056:2:188;4041:18;;4028:32;4069:33;4028:32;4069:33;:::i;:::-;3587:650;;;;-1:-1:-1;4121:7:188;;4201:2;4186:18;4173:32;;-1:-1:-1;;3587:650:188:o;4242:446::-;4294:3;4332:5;4326:12;4359:6;4354:3;4347:19;4391:4;4386:3;4382:14;4375:21;;4430:4;4423:5;4419:16;4453:1;4463:200;4477:6;4474:1;4471:13;4463:200;;;4542:13;;-1:-1:-1;;;;;;4538:40:188;4526:53;;4608:4;4599:14;;;;4636:17;;;;4499:1;4492:9;4463:200;;;-1:-1:-1;4679:3:188;;4242:446;-1:-1:-1;;;;4242:446:188:o;4693:1145::-;4913:4;4961:2;4950:9;4946:18;4991:2;4980:9;4973:21;5014:6;5049;5043:13;5080:6;5072;5065:22;5118:2;5107:9;5103:18;5096:25;;5180:2;5170:6;5167:1;5163:14;5152:9;5148:30;5144:39;5130:53;;5218:2;5210:6;5206:15;5239:1;5249:560;5263:6;5260:1;5257:13;5249:560;;;5356:2;5352:7;5340:9;5332:6;5328:22;5324:36;5319:3;5312:49;5390:6;5384:13;5436:2;5430:9;5467:2;5459:6;5452:18;5497:48;5541:2;5533:6;5529:15;5515:12;5497:48;:::i;:::-;5483:62;;5594:2;5590;5586:11;5580:18;5558:40;;5647:6;5639;5635:19;5630:2;5622:6;5618:15;5611:44;5678:51;5722:6;5706:14;5678:51;:::i;:::-;5668:61;-1:-1:-1;;;5764:2:188;5787:12;;;;5752:15;;;;;5285:1;5278:9;5249:560;;5843:782;6005:4;6053:2;6042:9;6038:18;6083:2;6072:9;6065:21;6106:6;6141;6135:13;6172:6;6164;6157:22;6210:2;6199:9;6195:18;6188:25;;6272:2;6262:6;6259:1;6255:14;6244:9;6240:30;6236:39;6222:53;;6310:2;6302:6;6298:15;6331:1;6341:255;6355:6;6352:1;6349:13;6341:255;;;6448:2;6444:7;6432:9;6424:6;6420:22;6416:36;6411:3;6404:49;6476:40;6509:6;6500;6494:13;6476:40;:::i;:::-;6466:50;-1:-1:-1;6551:2:188;6574:12;;;;6539:15;;;;;6377:1;6370:9;6341:255;;6630:1033;6834:4;6882:2;6871:9;6867:18;6912:2;6901:9;6894:21;6935:6;6970;6964:13;7001:6;6993;6986:22;7039:2;7028:9;7024:18;7017:25;;7101:2;7091:6;7088:1;7084:14;7073:9;7069:30;7065:39;7051:53;;7139:2;7131:6;7127:15;7160:1;7170:464;7184:6;7181:1;7178:13;7170:464;;;7249:22;;;-1:-1:-1;;7245:36:188;7233:49;;7305:13;;7350:9;;-1:-1:-1;;;;;7346:35:188;7331:51;;7429:2;7421:11;;;7415:18;7470:2;7453:15;;;7446:27;;;7415:18;7496:58;;7538:15;;7415:18;7496:58;:::i;:::-;7486:68;-1:-1:-1;;7589:2:188;7612:12;;;;7577:15;;;;;7206:1;7199:9;7170:464;;7668:771;7763:6;7771;7779;7787;7795;7848:3;7836:9;7827:7;7823:23;7819:33;7816:53;;;7865:1;7862;7855:12;7816:53;7904:9;7891:23;7923:31;7948:5;7923:31;:::i;:::-;7973:5;-1:-1:-1;8030:2:188;8015:18;;8002:32;8043:33;8002:32;8043:33;:::i;:::-;8095:7;-1:-1:-1;8154:2:188;8139:18;;8126:32;8167:33;8126:32;8167:33;:::i;:::-;7668:771;;;;-1:-1:-1;8219:7:188;;8299:2;8284:18;;8271:32;;-1:-1:-1;8402:3:188;8387:19;8374:33;;7668:771;-1:-1:-1;;7668:771:188:o;8636:226::-;8695:6;8748:2;8736:9;8727:7;8723:23;8719:32;8716:52;;;8764:1;8761;8754:12;8716:52;-1:-1:-1;8809:23:188;;8636:226;-1:-1:-1;8636:226:188:o;9099:380::-;9178:1;9174:12;;;;9221;;;9242:61;;9296:4;9288:6;9284:17;9274:27;;9242:61;9349:2;9341:6;9338:14;9318:18;9315:38;9312:161;;9395:10;9390:3;9386:20;9383:1;9376:31;9430:4;9427:1;9420:15;9458:4;9455:1;9448:15;9312:161;;9099:380;;;:::o;9971:277::-;10038:6;10091:2;10079:9;10070:7;10066:23;10062:32;10059:52;;;10107:1;10104;10097:12;10059:52;10139:9;10133:16;10192:5;10185:13;10178:21;10171:5;10168:32;10158:60;;10214:1;10211;10204:12;10158:60;10237:5;9971:277;-1:-1:-1;;;9971:277:188:o;10749:343::-;10828:6;10836;10889:2;10877:9;10868:7;10864:23;10860:32;10857:52;;;10905:1;10902;10895:12;10857:52;-1:-1:-1;;10950:16:188;;11056:2;11041:18;;;11035:25;10950:16;;11035:25;;-1:-1:-1;10749:343:188:o;11944:184::-;12014:6;12067:2;12055:9;12046:7;12042:23;12038:32;12035:52;;;12083:1;12080;12073:12;12035:52;-1:-1:-1;12106:16:188;;11944:184;-1:-1:-1;11944:184:188:o;12315:274::-;12408:6;12461:2;12449:9;12440:7;12436:23;12432:32;12429:52;;;12477:1;12474;12467:12;12429:52;12509:9;12503:16;12528:31;12553:5;12528:31;:::i;12829:362::-;13034:6;13023:9;13016:25;13077:6;13072:2;13061:9;13057:18;13050:34;13120:2;13115;13104:9;13100:18;13093:30;12997:4;13140:45;13181:2;13170:9;13166:18;13158:6;13140:45;:::i;:::-;13132:53;12829:362;-1:-1:-1;;;;;12829:362:188:o", "linkReferences": {}}, "methodIdentifiers": {"IS_TEST()": "fa7626d4", "approveDebtTransferAndDelegation(address,address,uint256,uint256)": "40e6058a", "delegateBorrow(address,address,address,uint256,uint256)": "b0e529a0", "delegateBorrowLiquidity(address,address,address,uint256)": "4b7f481b", "delegationContract()": "14706d20", "excludeArtifacts()": "b5508aa9", "excludeContracts()": "e20c9f71", "excludeSelectors()": "b0464fdc", "excludeSenders()": "1ed7831c", "failed()": "ba414fa6", "getDebtToken(uint256)": "c574dc8a", "targetArtifactSelectors()": "66d9a9a0", "targetArtifacts()": "85226c81", "targetContracts()": "3f7286f4", "targetInterfaces()": "2ade3880", "targetSelectors()": "916a17c6", "targetSenders()": "3e5e3c23", "verifyDelegationContractIsEmpty(uint256)": "fc92a614"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"contract IAmmalgamPair\",\"name\":\"_pair\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"log_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"log_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"log_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"\",\"type\":\"int256\"}],\"name\":\"log_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"val\",\"type\":\"address\"}],\"name\":\"log_named_address\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256[]\",\"name\":\"val\",\"type\":\"uint256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256[]\",\"name\":\"val\",\"type\":\"int256[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"val\",\"type\":\"address[]\"}],\"name\":\"log_named_array\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"val\",\"type\":\"bytes\"}],\"name\":\"log_named_bytes\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"val\",\"type\":\"bytes32\"}],\"name\":\"log_named_bytes32\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"decimals\",\"type\":\"uint256\"}],\"name\":\"log_named_decimal_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"val\",\"type\":\"int256\"}],\"name\":\"log_named_int\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"val\",\"type\":\"string\"}],\"name\":\"log_named_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"key\",\"type\":\"string\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"val\",\"type\":\"uint256\"}],\"name\":\"log_named_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"log_string\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"log_uint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"logs\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"IS_TEST\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalfOf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"delegatee\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"approveAmount\",\"type\":\"uint256\"}],\"name\":\"approveDebtTransferAndDelegation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalfOf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"delegatee\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountX\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountY\",\"type\":\"uint256\"}],\"name\":\"delegateBorrow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalfOf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"delegatee\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"delegateBorrowLiquidity\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"delegationContract\",\"outputs\":[{\"internalType\":\"contract IDebtDelegation\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"excludedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"excludedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"excludeSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"excludedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"failed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"}],\"name\":\"getDebtToken\",\"outputs\":[{\"internalType\":\"contract IERC20DebtToken\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifactSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"string\",\"name\":\"artifact\",\"type\":\"string\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzArtifactSelector[]\",\"name\":\"targetedArtifactSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetArtifacts\",\"outputs\":[{\"internalType\":\"string[]\",\"name\":\"targetedArtifacts_\",\"type\":\"string[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetContracts\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedContracts_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetInterfaces\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"string[]\",\"name\":\"artifacts\",\"type\":\"string[]\"}],\"internalType\":\"struct StdInvariant.FuzzInterface[]\",\"name\":\"targetedInterfaces_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSelectors\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"addr\",\"type\":\"address\"},{\"internalType\":\"bytes4[]\",\"name\":\"selectors\",\"type\":\"bytes4[]\"}],\"internalType\":\"struct StdInvariant.FuzzSelector[]\",\"name\":\"targetedSelectors_\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"targetSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"targetedSenders_\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"}],\"name\":\"verifyDelegationContractIsEmpty\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/shared/DelegationTestFixture.sol\":\"DelegationTestFixture\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/interfaces/IAmmalgamPair.sol\":{\"keccak256\":\"0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795\",\"dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw\"]},\"contracts/interfaces/callbacks/IAmmalgamCallee.sol\":{\"keccak256\":\"0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d\",\"dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1\"]},\"contracts/interfaces/callbacks/ITransferValidator.sol\":{\"keccak256\":\"0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4\",\"dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/IERC20DebtToken.sol\":{\"keccak256\":\"0xc50c6be17633c8ac677b4eaac7c05a6de1f1c938237179b59ad5e65bcfbcb03a\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://6c75327e01d70a0c22662a9a8214aa64e45c517146971f8636e5aa5bf06e7696\",\"dweb:/ipfs/QmV2ydBQ5S9ZBtRuPgBMBdvd2Hcnn8quCGEMhhAAuic15b\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41\",\"dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb\",\"dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5\",\"dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Base.sol\":{\"keccak256\":\"0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224\",\"dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol\":{\"keccak256\":\"0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe\",\"dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1\",\"dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol\":{\"keccak256\":\"0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41\",\"dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol\":{\"keccak256\":\"0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6\",\"dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol\":{\"keccak256\":\"0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391\",\"dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol\":{\"keccak256\":\"0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974\",\"dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol\":{\"keccak256\":\"0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92\",\"dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol\":{\"keccak256\":\"0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57\",\"dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol\":{\"keccak256\":\"0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8\",\"dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol\":{\"keccak256\":\"0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3\",\"dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol\":{\"keccak256\":\"0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138\",\"dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Test.sol\":{\"keccak256\":\"0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e\",\"dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c\",\"dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console.sol\":{\"keccak256\":\"0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57\",\"dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/console2.sol\":{\"keccak256\":\"0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d\",\"dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol\":{\"keccak256\":\"0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0\",\"dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2\"]},\"lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol\":{\"keccak256\":\"0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab\",\"dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3\"]},\"test/example/PeripheralDelegationContractExample.sol\":{\"keccak256\":\"0xf212fd0b2dd3a358c826623bd320e3aa0630892b9f6ba777b8126d3e2cfcfb14\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://2ad79b2d7eb1b46c69383b9e9090bf2031ff779e46637d850b881db3dc84d797\",\"dweb:/ipfs/QmTPx8qw1zdYXRzjBnmuzMCt8yiErwFiLBk47xnbTm1erP\"]},\"test/shared/DelegationTestFixture.sol\":{\"keccak256\":\"0xa3bbfac7e34ca00d0583804a98e492f256ab7bb5e49e5ffed6fd8b2bf1b5b2e5\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://e6c4b917f87135b244a08400d9f88c995960f3b040354348654778952bfb35ec\",\"dweb:/ipfs/QmTWbFsQmoLne1FZUtaMCaU6FSLT8XxGbWc3Nfz1KFYyNE\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "contract IAmmalgamPair", "name": "_pair", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "", "type": "address", "indexed": false}], "type": "event", "name": "log_address", "anonymous": false}, {"inputs": [{"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_array", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "log_bytes", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_bytes32", "anonymous": false}, {"inputs": [{"internalType": "int256", "name": "", "type": "int256", "indexed": false}], "type": "event", "name": "log_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address", "name": "val", "type": "address", "indexed": false}], "type": "event", "name": "log_named_address", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256[]", "name": "val", "type": "uint256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256[]", "name": "val", "type": "int256[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "address[]", "name": "val", "type": "address[]", "indexed": false}], "type": "event", "name": "log_named_array", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes", "name": "val", "type": "bytes", "indexed": false}], "type": "event", "name": "log_named_bytes", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "bytes32", "name": "val", "type": "bytes32", "indexed": false}], "type": "event", "name": "log_named_bytes32", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "decimals", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_decimal_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "int256", "name": "val", "type": "int256", "indexed": false}], "type": "event", "name": "log_named_int", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "string", "name": "val", "type": "string", "indexed": false}], "type": "event", "name": "log_named_string", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "key", "type": "string", "indexed": false}, {"internalType": "uint256", "name": "val", "type": "uint256", "indexed": false}], "type": "event", "name": "log_named_uint", "anonymous": false}, {"inputs": [{"internalType": "string", "name": "", "type": "string", "indexed": false}], "type": "event", "name": "log_string", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256", "indexed": false}], "type": "event", "name": "log_uint", "anonymous": false}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes", "indexed": false}], "type": "event", "name": "logs", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "IS_TEST", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "onBehalfOf", "type": "address"}, {"internalType": "address", "name": "delegatee", "type": "address"}, {"internalType": "uint256", "name": "tokenType", "type": "uint256"}, {"internalType": "uint256", "name": "approveAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approveDebtTransferAndDelegation"}, {"inputs": [{"internalType": "address", "name": "onBehalfOf", "type": "address"}, {"internalType": "address", "name": "delegatee", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amountX", "type": "uint256"}, {"internalType": "uint256", "name": "amountY", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"inputs": [{"internalType": "address", "name": "onBehalfOf", "type": "address"}, {"internalType": "address", "name": "delegatee", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "delegateBorrowLiquidity"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "delegationContract", "outputs": [{"internalType": "contract IDebtDelegation", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeArtifacts", "outputs": [{"internalType": "string[]", "name": "excludedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeContracts", "outputs": [{"internalType": "address[]", "name": "excludedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "excludedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "excludeSenders", "outputs": [{"internalType": "address[]", "name": "excludedSenders_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "failed", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenType", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getDebtToken", "outputs": [{"internalType": "contract IERC20DebtToken", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifactSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzArtifactSelector[]", "name": "targetedArtifactSelectors_", "type": "tuple[]", "components": [{"internalType": "string", "name": "artifact", "type": "string"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetArtifacts", "outputs": [{"internalType": "string[]", "name": "targetedArtifacts_", "type": "string[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetContracts", "outputs": [{"internalType": "address[]", "name": "targetedContracts_", "type": "address[]"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetInterfaces", "outputs": [{"internalType": "struct StdInvariant.FuzzInterface[]", "name": "targetedInterfaces_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "string[]", "name": "artifacts", "type": "string[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSelectors", "outputs": [{"internalType": "struct StdInvariant.FuzzSelector[]", "name": "targetedSelectors_", "type": "tuple[]", "components": [{"internalType": "address", "name": "addr", "type": "address"}, {"internalType": "bytes4[]", "name": "selectors", "type": "bytes4[]"}]}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "targetSenders", "outputs": [{"internalType": "address[]", "name": "targetedSenders_", "type": "address[]"}]}, {"inputs": [{"internalType": "uint256", "name": "tokenType", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "verifyDelegationContractIsEmpty"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/shared/DelegationTestFixture.sol": "DelegationTestFixture"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/interfaces/IAmmalgamPair.sol": {"keccak256": "0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337", "urls": ["bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795", "dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/IAmmalgamCallee.sol": {"keccak256": "0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339", "urls": ["bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d", "dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/ITransferValidator.sol": {"keccak256": "0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56", "urls": ["bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4", "dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IERC20DebtToken.sol": {"keccak256": "0xc50c6be17633c8ac677b4eaac7c05a6de1f1c938237179b59ad5e65bcfbcb03a", "urls": ["bzz-raw://6c75327e01d70a0c22662a9a8214aa64e45c517146971f8636e5aa5bf06e7696", "dweb:/ipfs/QmV2ydBQ5S9ZBtRuPgBMBdvd2Hcnn8quCGEMhhAAuic15b"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da", "urls": ["bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41", "dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b", "urls": ["bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb", "dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2", "urls": ["bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5", "dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Base.sol": {"keccak256": "0x4ff1a785311017d1eedb1b4737956fa383067ad34eb439abfec1d989754dde1c", "urls": ["bzz-raw://f553622969b9fdb930246704a4c10dfaee6b1a4468c142fa7eb9dc292a438224", "dweb:/ipfs/QmcxqHnqdQsMVtgsfH9VNLmZ3g7GhgNagfq7yvNCDcCHFK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdAssertions.sol": {"keccak256": "0x4584f551c3a875c26423c1e50a77179bc2f9c9c1ee6d0f0c6be0f89ca5ee8270", "urls": ["bzz-raw://ef118876a862b60ba8a6612b8b3f3a6c226e11a0071a2e9695f021586d1bfdbe", "dweb:/ipfs/QmXd2vD91PkvoFWasFkgbDi89PctmgsHVxGvGNyhjmRK2b"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdChains.sol": {"keccak256": "0xcd3e64ec9ffa19a2c0715bbdaf7ddf28887cc418e079bec4373fd6a3f9961a7b", "urls": ["bzz-raw://e981a2ab738590928e9efa5f3d95a408c718eb12d73a113d7675f3ed55a026a1", "dweb:/ipfs/QmTgSEkWWsBRy32goRCaUkraSgpZHtgbZoKC3iEFNz5RDc"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdCheats.sol": {"keccak256": "0x0fa6ec03602648b62cce41aab2096e6b7e052f2846075d967b6958dd586db746", "urls": ["bzz-raw://cd84e2ca9c1eaed6b76768cc12bb8c1af8289170ea8b7706f58d516460d79c41", "dweb:/ipfs/QmQ7BK7co6DE4eWUqMyv11s5eHYkS1tyx8tDSZGZVtf2aK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdError.sol": {"keccak256": "0xbf477b11a42d7611696956546bcfaa29317d1166bf65e402344599c05943fc77", "urls": ["bzz-raw://bc2e117d1135e030862b96a6526a43feb38d396cc79857f1fb696d4eff0e5fd6", "dweb:/ipfs/QmdSuQ5RrQudTLsNmWXGEeVJX8gR5U9XPm6m4dwwuQnJrj"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdInvariant.sol": {"keccak256": "0x4dab3021edfa9511bbdd80c48f060ef62eaf457f99eaf841f561fc2557d9a08d", "urls": ["bzz-raw://07668628673174cf8f27f8f4e1f862bab775013ec247eb34f698c5005f229391", "dweb:/ipfs/QmPJsiiYwmCZXMsHmQv5tg8VF3CAhNdat7WnKLTWZZH2v5"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdJson.sol": {"keccak256": "0xbc0132abe1c2accc2867c0f03667afffdf92f3e95a581bb03c9557eaa38ea500", "urls": ["bzz-raw://eb6fab37dc73c219cfbb7b4f4998bcf7677ca5397a867e850f40232192073974", "dweb:/ipfs/QmUHsbVdp9SKmgek7ZfPcLTKrpZFXpqaqt4sVejzxGEQL3"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdMath.sol": {"keccak256": "0xd90ad4fd8aeaeb8929964e686e769fdedd5eded3fc3815df194a0ab9f91a3fb2", "urls": ["bzz-raw://7919b70f636c7b805223992f28ad1ad0145d6c1385b5931a3589aface5fe6c92", "dweb:/ipfs/QmY7FRaULwoGgFteF8GawjQJRfasNgpWnU2aiMsFrYpuTC"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStorage.sol": {"keccak256": "0xb91ab24383a5872b894fc93325eef1add6cbbf981628f18e860068bf88bb7dcc", "urls": ["bzz-raw://2651e33b9ac8fc5803ed0a43078c1bf4fa90a0b0347aafd95776b02daccdcc57", "dweb:/ipfs/QmbdRyPuGJdZgnCuMnp7c1WsBo1Spf7j1KMmws1Z5rd4BQ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdStyle.sol": {"keccak256": "0x43e2a8a9b9c2574dabe74f11adf6f782df218f463540e3b5b563609fe108597d", "urls": ["bzz-raw://51363ca97404cf4128e1141428949768c31929e75e014b02c85e887fbbb4f1b8", "dweb:/ipfs/QmVhtbQc2fU4rRmbcfBtz34mAgG4BAZBsbna1Ca4SkoPsK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdToml.sol": {"keccak256": "0x58a72c765ed3f7ff6b105509689658795b8a3739b8931772a497155878381861", "urls": ["bzz-raw://b4a3746f4fabaeb980bd77d9e091d3904ee38a6c0e191bfa8ba6874c6f8558a3", "dweb:/ipfs/QmUfFDMEn461FgGEXt5HicyGD54sc28sLaQ9JRWDMBKed8"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/StdUtils.sol": {"keccak256": "0x7274081e11c05164fd8eadde4de8305c033e58a43008dea58065f3170ccf0737", "urls": ["bzz-raw://e9801614b6c9d3e472982e6cc68f5f1ad03682f84eafb686be65633c7132d138", "dweb:/ipfs/QmcQSUcrm2A7XuektnxJjvYGmZtBeA6LKPxCXRqdXBX776"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Test.sol": {"keccak256": "0x3b4bb409a156dee9ce261458117fe9f81080ca844a8a26c07c857c46d155effe", "urls": ["bzz-raw://5792c69fe24bdc063a14e08fe68275007fdb1e5e7e343840a77938cb7e95a64e", "dweb:/ipfs/QmcAMhaurUwzhytJFYix4vRNeZeV8g27b8LnV3t7dvYtiK"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/Vm.sol": {"keccak256": "0x44bfadcf5a89b8058f80258f2259585c740f9cc45669a0579f4f2753ff2c6354", "urls": ["bzz-raw://bbc366c8b3499d5030e3b2e45bac23770531f2f5243a0e80e3d5a66b6f9a312c", "dweb:/ipfs/QmNxDEB3BaVnKzNaWedtdMshhvCEddB1AsdJZcsQx6jdtC"], "license": "MIT OR Apache-2.0"}, "lib/openzeppelin-contracts/lib/forge-std/src/console.sol": {"keccak256": "0x4bbf47eb762cef93729d6ef15e78789957147039b113e5d4df48e3d3fd16d0f5", "urls": ["bzz-raw://af9e3a7c3d82fb5b10b57ca4d1a82f2acbef80c077f6f6ef0cc0187c7bfd9f57", "dweb:/ipfs/QmR9VzmnBDJpgiDP6CHT6truehukF9HpYvuP6kRiJbDwPP"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/console2.sol": {"keccak256": "0x3b8fe79f48f065a4e4d35362171304a33784c3a90febae5f2787805a438de12f", "urls": ["bzz-raw://61de63af08803549299e68b6e6e88d40f3c5afac450e4ee0a228c66a61ba003d", "dweb:/ipfs/QmWVoQ5rrVxnczD4ZZoPbD4PC9Z3uExJtzjD4awTqd14MZ"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/interfaces/IMulticall3.sol": {"keccak256": "0x7aac1389150499a922d1f9ef5749c908cef127cb2075b92fa17e9cb611263d0a", "urls": ["bzz-raw://d95ebb7c7c463e08ebc12dab639945752fb2480acfc6e86da32f72732a7fd0c0", "dweb:/ipfs/QmNXK8P8oPWwajsQHvAHw3JPyQidPLCGQN3hWu1Lk6PBL2"], "license": "MIT"}, "lib/openzeppelin-contracts/lib/forge-std/src/safeconsole.sol": {"keccak256": "0xbef9786cb49d3eade757bad87568c49c8c8f35721f0193c95ffb055d9e466e11", "urls": ["bzz-raw://3bafd2b0b2d28068d329f95ea8a1fbce3719c257fcb863fc01abcbafd8d531ab", "dweb:/ipfs/QmUeaFjKWTVDBsHVfSob4mwt6A5hTnKDz22HaUXeZhypa3"], "license": "MIT"}, "test/example/PeripheralDelegationContractExample.sol": {"keccak256": "0xf212fd0b2dd3a358c826623bd320e3aa0630892b9f6ba777b8126d3e2cfcfb14", "urls": ["bzz-raw://2ad79b2d7eb1b46c69383b9e9090bf2031ff779e46637d850b881db3dc84d797", "dweb:/ipfs/QmTPx8qw1zdYXRzjBnmuzMCt8yiErwFiLBk47xnbTm1erP"], "license": "GPL-3.0-only"}, "test/shared/DelegationTestFixture.sol": {"keccak256": "0xa3bbfac7e34ca00d0583804a98e492f256ab7bb5e49e5ffed6fd8b2bf1b5b2e5", "urls": ["bzz-raw://e6c4b917f87135b244a08400d9f88c995960f3b040354348654778952bfb35ec", "dweb:/ipfs/QmTWbFsQmoLne1FZUtaMCaU6FSLT8XxGbWc3Nfz1KFYyNE"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 173}