{"abi": [{"type": "function", "name": "ammalgamBorrowCallV1", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "borrowedXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "borrowedYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "borrowedXShares", "type": "uint256", "internalType": "uint256"}, {"name": "borrowedYShares", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "ammalgamBorrowLiquidityCallV1", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "borrowedXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "borrowedYAssets", "type": "uint256", "internalType": "uint256"}, {"name": "borrowedLShares", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "ammalgamLiquidateCallV1", "inputs": [{"name": "repayXInXAssets", "type": "uint256", "internalType": "uint256"}, {"name": "repayYInYAssets", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "ammalgamSwapCallV1", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "amountX", "type": "uint256", "internalType": "uint256"}, {"name": "amountY", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "approveDelegation", "inputs": [{"name": "delegatee", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "borrow", "inputs": [{"name": "onBehalfOf", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "pair", "type": "address", "internalType": "contract IAmmalgamPair"}, {"name": "amountX", "type": "uint256", "internalType": "uint256"}, {"name": "amountY", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "borrowLiquidity", "inputs": [{"name": "onBehalfOf", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "pair", "type": "address", "internalType": "contract IAmmalgamPair"}, {"name": "amountLShares", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "delegationAllowance", "inputs": [{"name": "onBehalfOf", "type": "address", "internalType": "address"}, {"name": "delegatee", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "0x6080604052348015600e575f5ffd5b50610acf8061001c5f395ff3fe608060405234801561000f575f5ffd5b5060043610610085575f3560e01c80639ae299d2116100585780639ae299d21461011c578063b0054dd414610165578063b9a2ff5414610178578063f0a494941461018a575f5ffd5b80634e8eced1146100895780634f2799a9146100a05780635e305d79146100b35780636ea70f3a146100ef575b5f5ffd5b61009e610097366004610734565b5050505050565b005b61009e6100ae366004610799565b61019d565b61009e6100c1366004610807565b335f908152602081815260408083206001600160a01b0394909416835292905220805460ff19166001179055565b6101026100fd366004610829565b61027e565b604080519283526020830191909152015b60405180910390f35b61015561012a366004610877565b6001600160a01b039182165f9081526020818152604080832093909416825291909152205460ff1690565b6040519015158152602001610113565b61009e6101733660046108ae565b610341565b61009e610186366004610905565b5050565b61009e610198366004610925565b6103dd565b5f806101ab83850185610877565b915091505f5f5f6101bb33610544565b919450925090508715610271576040516327b2595f60e11b8152600360048201525f906001600160a01b03851690634f64b2be90602401602060405180830381865afa15801561020d573d5f5f3e3d5ffd5b505050506040513d601f19601f82011682018060405250810190610231919061099c565b90506102476001600160a01b038216878b6105b3565b61025b6001600160a01b038416868d6105b3565b61026f6001600160a01b038316868c6105b3565b505b5050505050505050505050565b5f5f5f86866040516020016102a99291906001600160a01b0392831681529116602082015260400190565b60405160208183030381529060405290506102c487336105f2565b6040516321c9ae5360e21b81526001600160a01b03861690638726b94c906102f4903090889086906004016109e5565b60408051808303815f875af115801561030f573d5f5f3e3d5ffd5b505050506040513d601f19601f820116820180604052508101906103339190610a14565b925092505094509492505050565b604080516001600160a01b038781166020830152861681830152815180820383018152606090910190915261037686336105f2565b604051638b04990760e01b81526001600160a01b03851690638b049907906103a8903090879087908790600401610a36565b5f604051808303815f87803b1580156103bf575f5ffd5b505af11580156103d1573d5f5f3e3d5ffd5b50505050505050505050565b5f806103eb83850185610877565b915091505f5f5f6103fb33610544565b91945092509050881561049c576040516327b2595f60e11b81526004808201525f906001600160a01b03851690634f64b2be90602401602060405180830381865afa15801561044c573d5f5f3e3d5ffd5b505050506040513d601f19601f82011682018060405250810190610470919061099c565b90506104866001600160a01b038216878c6105b3565b61049a6001600160a01b038416868e6105b3565b505b871561026f576040516327b2595f60e11b8152600560048201525f906001600160a01b03851690634f64b2be90602401602060405180830381865afa1580156104e7573d5f5f3e3d5ffd5b505050506040513d601f19601f8201168201806040525081019061050b919061099c565b90506105216001600160a01b038216878b6105b3565b6105356001600160a01b038316868d6105b3565b50505050505050505050505050565b5f5f5f839250826001600160a01b031663bd27dc9f6040518163ffffffff1660e01b81526004016040805180830381865afa158015610585573d5f5f3e3d5ffd5b505050506040513d601f19601f820116820180604052508101906105a99190610a6c565b9395909450915050565b6105c08383836001610676565b6105ed57604051635274afe760e01b81526001600160a01b03841660048201526024015b60405180910390fd5b505050565b816001600160a01b0316816001600160a01b031614610186576001600160a01b038083165f908152602081815260408083209385168352929052205460ff166101865760405162461bcd60e51b815260206004820152601660248201527511195b1959d85d1a5bdb881b9bdd08185b1b1bddd95960521b60448201526064016105e4565b60405163a9059cbb60e01b5f8181526001600160a01b038616600452602485905291602083604481808b5af1925060015f511483166106cc5783831516156106c0573d5f823e3d81fd5b5f873b113d1516831692505b60405250949350505050565b6001600160a01b03811681146106ec575f5ffd5b50565b5f5f83601f8401126106ff575f5ffd5b50813567ffffffffffffffff811115610716575f5ffd5b60208301915083602082850101111561072d575f5ffd5b9250929050565b5f5f5f5f5f60808688031215610748575f5ffd5b8535610753816106d8565b94506020860135935060408601359250606086013567ffffffffffffffff81111561077c575f5ffd5b610788888289016106ef565b969995985093965092949392505050565b5f5f5f5f5f5f60a087890312156107ae575f5ffd5b86356107b9816106d8565b9550602087013594506040870135935060608701359250608087013567ffffffffffffffff8111156107e9575f5ffd5b6107f589828a016106ef565b979a9699509497509295939492505050565b5f60208284031215610817575f5ffd5b8135610822816106d8565b9392505050565b5f5f5f5f6080858703121561083c575f5ffd5b8435610847816106d8565b93506020850135610857816106d8565b92506040850135610867816106d8565b9396929550929360600135925050565b5f5f60408385031215610888575f5ffd5b8235610893816106d8565b915060208301356108a3816106d8565b809150509250929050565b5f5f5f5f5f60a086880312156108c2575f5ffd5b85356108cd816106d8565b945060208601356108dd816106d8565b935060408601356108ed816106d8565b94979396509394606081013594506080013592915050565b5f5f60408385031215610916575f5ffd5b50508035926020909101359150565b5f5f5f5f5f5f5f60c0888a03121561093b575f5ffd5b8735610946816106d8565b96506020880135955060408801359450606088013593506080880135925060a088013567ffffffffffffffff81111561097d575f5ffd5b6109898a828b016106ef565b989b979a50959850939692959293505050565b5f602082840312156109ac575f5ffd5b8151610822816106d8565b5f81518084528060208401602086015e5f602082860101526020601f19601f83011685010191505092915050565b60018060a01b0384168152826020820152606060408201525f610a0b60608301846109b7565b95945050505050565b5f5f60408385031215610a25575f5ffd5b505080516020909101519092909150565b60018060a01b0385168152836020820152826040820152608060608201525f610a6260808301846109b7565b9695505050505050565b5f5f60408385031215610a7d575f5ffd5b8251610a88816106d8565b60208401519092506108a3816106d856fea26469706673582212207c8673d42b34d83d8c7e7c842206b4a783e12743309bfb0f3b4ba999940c236e64736f6c634300081c0033", "sourceMap": "1042:3723:172:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "1042:3723:172:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4641:122;;;;;;:::i;:::-;;;;;;;;;3319:697;;;;;;:::i;:::-;;:::i;1533:134::-;;;;;;:::i;:::-;1631:10;1619:11;:23;;;;;;;;;;;-1:-1:-1;;;;;1619:34:172;;;;;;;;;;:41;;-1:-1:-1;;1619:41:172;1656:4;1619:41;;;1533:134;2017:368;;;;;;:::i;:::-;;:::i;:::-;;;;3306:25:188;;;3362:2;3347:18;;3340:34;;;;3279:18;2017:368:172;;;;;;;;1365:162;;;;;;:::i;:::-;-1:-1:-1;;;;;1486:23:172;;;1463:4;1486:23;;;;;;;;;;;:34;;;;;;;;;;;;;;;1365:162;;;;3943:14:188;;3936:22;3918:41;;3906:2;3891:18;1365:162:172;3778:187:188;1673:338:172;;;;;;:::i;:::-;;:::i;4022:106::-;;;;;;:::i;:::-;;;;2391:922;;;;;;:::i;:::-;;:::i;3319:697::-;3540:18;;3574:36;;;;3585:4;3574:36;:::i;:::-;3539:71;;;;3622:18;3642:13;3657;3674:25;3688:10;3674:13;:25::i;:::-;3621:78;;-1:-1:-1;3621:78:172;-1:-1:-1;3621:78:172;-1:-1:-1;3714:19:172;;3710:300;;3802:21;;-1:-1:-1;;;3802:21:172;;342:1:19;3802:21:172;;;6706:25:188;3749:26:172;;-1:-1:-1;;;;;3802:11:172;;;;;6679:18:188;;3802:21:172;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3749:76;-1:-1:-1;3839:52:172;-1:-1:-1;;;;;3839:23:172;;3863:10;3875:15;3839:23;:52::i;:::-;3905:40;-1:-1:-1;;;;;3905:19:172;;3925:2;3929:15;3905:19;:40::i;:::-;3959;-1:-1:-1;;;;;3959:19:172;;3979:2;3983:15;3959:19;:40::i;:::-;3735:275;3710:300;3529:487;;;;;3319:697;;;;;;:::o;2017:368::-;2182:7;2191;2210:17;2241:10;2253:2;2230:26;;;;;;;;-1:-1:-1;;;;;7213:32:188;;;7195:51;;7282:32;;7277:2;7262:18;;7255:60;7183:2;7168:18;;7021:300;2230:26:172;;;;;;;;;;;;;2210:46;;2266:39;2282:10;2294;2266:15;:39::i;:::-;2322:56;;-1:-1:-1;;;2322:56:172;;-1:-1:-1;;;;;2322:20:172;;;;;:56;;2351:4;;2358:13;;2373:4;;2322:56;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2315:63;;;;;2017:368;;;;;;;:::o;1673:338::-;1869:26;;;-1:-1:-1;;;;;7213:32:188;;;1869:26:172;;;7195:51:188;7282:32;;7262:18;;;7255:60;1869:26:172;;;;;;;;;7168:18:188;;;;1869:26:172;;;1905:39;7213:32:188;1933:10:172;1905:15;:39::i;:::-;1954:50;;-1:-1:-1;;;1954:50:172;;-1:-1:-1;;;;;1954:11:172;;;;;:50;;1974:4;;1981:7;;1990;;1999:4;;1954:50;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1839:172;1673:338;;;;;:::o;2391:922::-;2636:18;;2670:36;;;;2681:4;2670:36;:::i;:::-;2635:71;;;;2718:18;2738:13;2753;2770:25;2784:10;2770:13;:25::i;:::-;2717:78;;-1:-1:-1;2717:78:172;-1:-1:-1;2717:78:172;-1:-1:-1;2810:19:172;;2806:246;;2898:21;;-1:-1:-1;;;2898:21:172;;373:1:19;2898:21:172;;;6706:25:188;2845:26:172;;-1:-1:-1;;;;;2898:11:172;;;;;6679:18:188;;2898:21:172;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;2845:76;-1:-1:-1;2935:52:172;-1:-1:-1;;;;;2935:23:172;;2959:10;2971:15;2935:23;:52::i;:::-;3001:40;-1:-1:-1;;;;;3001:19:172;;3021:2;3025:15;3001:19;:40::i;:::-;2831:221;2806:246;3065:19;;3061:246;;3153:21;;-1:-1:-1;;;3153:21:172;;404:1:19;3153:21:172;;;6706:25:188;3100:26:172;;-1:-1:-1;;;;;3153:11:172;;;;;6679:18:188;;3153:21:172;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3100:76;-1:-1:-1;3190:52:172;-1:-1:-1;;;;;3190:23:172;;3214:10;3226:15;3190:23;:52::i;:::-;3256:40;-1:-1:-1;;;;;3256:19:172;;3276:2;3280:15;3256:19;:40::i;:::-;3086:221;2625:688;;;;;2391:922;;;;;;;:::o;4415:220::-;4489:18;4509:13;4524;4570:5;4549:27;;4605:4;-1:-1:-1;;;;;4605:21:172;;:23;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4415:220;;4586:42;;-1:-1:-1;4415:220:172;-1:-1:-1;;4415:220:172:o;1219:204:76:-;1306:37;1320:5;1327:2;1331:5;1338:4;1306:13;:37::i;:::-;1301:116;;1366:40;;-1:-1:-1;;;1366:40:76;;-1:-1:-1;;;;;9406:32:188;;1366:40:76;;;9388:51:188;9361:18;;1366:40:76;;;;;;;;1301:116;1219:204;;;:::o;4134:224:172:-;4238:10;-1:-1:-1;;;;;4225:23:172;:9;-1:-1:-1;;;;;4225:23:172;;4221:131;;-1:-1:-1;;;;;1486:23:172;;;1463:4;1486:23;;;;;;;;;;;:34;;;;;;;;;;;;4264:77;;;;-1:-1:-1;;;4264:77:172;;9652:2:188;4264:77:172;;;9634:21:188;9691:2;9671:18;;;9664:30;-1:-1:-1;;;9710:18:188;;;9703:52;9772:18;;4264:77:172;9450:346:188;8368:1235:76;8595:4;8589:11;-1:-1:-1;;;8462:12:76;8613:22;;;-1:-1:-1;;;;;8661:24:76;;8655:4;8648:38;8706:4;8699:19;;;8462:12;8776:4;8462:12;8767:4;8462:12;;8754:5;8747;8742:39;8731:50;;8993:1;8986:4;8980:11;8977:18;8968:7;8964:32;8954:603;;9125:6;9115:7;9108:15;9104:28;9101:162;;;9178:16;9175:1;9170:3;9155:40;9228:16;9223:3;9216:29;9101:162;9539:1;9531:5;9519:18;9516:25;9497:16;9490:24;9486:56;9477:7;9473:70;9462:81;;8954:603;9577:4;9570:17;-1:-1:-1;8368:1235:76;;-1:-1:-1;;;;8368:1235:76:o;14:131:188:-;-1:-1:-1;;;;;89:31:188;;79:42;;69:70;;135:1;132;125:12;69:70;14:131;:::o;150:347::-;201:8;211:6;265:3;258:4;250:6;246:17;242:27;232:55;;283:1;280;273:12;232:55;-1:-1:-1;306:20:188;;349:18;338:30;;335:50;;;381:1;378;371:12;335:50;418:4;410:6;406:17;394:29;;470:3;463:4;454:6;446;442:19;438:30;435:39;432:59;;;487:1;484;477:12;432:59;150:347;;;;;:::o;502:785::-;599:6;607;615;623;631;684:3;672:9;663:7;659:23;655:33;652:53;;;701:1;698;691:12;652:53;740:9;727:23;759:31;784:5;759:31;:::i;:::-;809:5;-1:-1:-1;887:2:188;872:18;;859:32;;-1:-1:-1;990:2:188;975:18;;962:32;;-1:-1:-1;1071:2:188;1056:18;;1043:32;1098:18;1087:30;;1084:50;;;1130:1;1127;1120:12;1084:50;1169:58;1219:7;1210:6;1199:9;1195:22;1169:58;:::i;:::-;502:785;;;;-1:-1:-1;502:785:188;;-1:-1:-1;1246:8:188;;1143:84;502:785;-1:-1:-1;;;502:785:188:o;1292:906::-;1398:6;1406;1414;1422;1430;1438;1491:3;1479:9;1470:7;1466:23;1462:33;1459:53;;;1508:1;1505;1498:12;1459:53;1547:9;1534:23;1566:31;1591:5;1566:31;:::i;:::-;1616:5;-1:-1:-1;1694:2:188;1679:18;;1666:32;;-1:-1:-1;1797:2:188;1782:18;;1769:32;;-1:-1:-1;1900:2:188;1885:18;;1872:32;;-1:-1:-1;1981:3:188;1966:19;;1953:33;2009:18;1998:30;;1995:50;;;2041:1;2038;2031:12;1995:50;2080:58;2130:7;2121:6;2110:9;2106:22;2080:58;:::i;:::-;1292:906;;;;-1:-1:-1;1292:906:188;;-1:-1:-1;1292:906:188;;2157:8;;1292:906;-1:-1:-1;;;1292:906:188:o;2203:247::-;2262:6;2315:2;2303:9;2294:7;2290:23;2286:32;2283:52;;;2331:1;2328;2321:12;2283:52;2370:9;2357:23;2389:31;2414:5;2389:31;:::i;:::-;2439:5;2203:247;-1:-1:-1;;;2203:247:188:o;2455:672::-;2563:6;2571;2579;2587;2640:3;2628:9;2619:7;2615:23;2611:33;2608:53;;;2657:1;2654;2647:12;2608:53;2696:9;2683:23;2715:31;2740:5;2715:31;:::i;:::-;2765:5;-1:-1:-1;2822:2:188;2807:18;;2794:32;2835:33;2794:32;2835:33;:::i;:::-;2887:7;-1:-1:-1;2946:2:188;2931:18;;2918:32;2959:33;2918:32;2959:33;:::i;:::-;2455:672;;;;-1:-1:-1;3011:7:188;;3091:2;3076:18;3063:32;;-1:-1:-1;;2455:672:188:o;3385:388::-;3453:6;3461;3514:2;3502:9;3493:7;3489:23;3485:32;3482:52;;;3530:1;3527;3520:12;3482:52;3569:9;3556:23;3588:31;3613:5;3588:31;:::i;:::-;3638:5;-1:-1:-1;3695:2:188;3680:18;;3667:32;3708:33;3667:32;3708:33;:::i;:::-;3760:7;3750:17;;;3385:388;;;;;:::o;3970:793::-;4087:6;4095;4103;4111;4119;4172:3;4160:9;4151:7;4147:23;4143:33;4140:53;;;4189:1;4186;4179:12;4140:53;4228:9;4215:23;4247:31;4272:5;4247:31;:::i;:::-;4297:5;-1:-1:-1;4354:2:188;4339:18;;4326:32;4367:33;4326:32;4367:33;:::i;:::-;4419:7;-1:-1:-1;4478:2:188;4463:18;;4450:32;4491:33;4450:32;4491:33;:::i;:::-;3970:793;;;;-1:-1:-1;4543:7:188;;4623:2;4608:18;;4595:32;;-1:-1:-1;4726:3:188;4711:19;4698:33;;3970:793;-1:-1:-1;;3970:793:188:o;4768:346::-;4836:6;4844;4897:2;4885:9;4876:7;4872:23;4868:32;4865:52;;;4913:1;4910;4903:12;4865:52;-1:-1:-1;;4958:23:188;;;5078:2;5063:18;;;5050:32;;-1:-1:-1;4768:346:188:o;5119:1027::-;5234:6;5242;5250;5258;5266;5274;5282;5335:3;5323:9;5314:7;5310:23;5306:33;5303:53;;;5352:1;5349;5342:12;5303:53;5391:9;5378:23;5410:31;5435:5;5410:31;:::i;:::-;5460:5;-1:-1:-1;5538:2:188;5523:18;;5510:32;;-1:-1:-1;5641:2:188;5626:18;;5613:32;;-1:-1:-1;5744:2:188;5729:18;;5716:32;;-1:-1:-1;5847:3:188;5832:19;;5819:33;;-1:-1:-1;5929:3:188;5914:19;;5901:33;5957:18;5946:30;;5943:50;;;5989:1;5986;5979:12;5943:50;6028:58;6078:7;6069:6;6058:9;6054:22;6028:58;:::i;:::-;5119:1027;;;;-1:-1:-1;5119:1027:188;;-1:-1:-1;5119:1027:188;;;;6002:84;;-1:-1:-1;;;5119:1027:188:o;6742:274::-;6835:6;6888:2;6876:9;6867:7;6863:23;6859:32;6856:52;;;6904:1;6901;6894:12;6856:52;6936:9;6930:16;6955:31;6980:5;6955:31;:::i;7326:288::-;7367:3;7405:5;7399:12;7432:6;7427:3;7420:19;7488:6;7481:4;7474:5;7470:16;7463:4;7458:3;7454:14;7448:47;7540:1;7533:4;7524:6;7519:3;7515:16;7511:27;7504:38;7603:4;7596:2;7592:7;7587:2;7579:6;7575:15;7571:29;7566:3;7562:39;7558:50;7551:57;;;7326:288;;;;:::o;7619:385::-;7851:1;7847;7842:3;7838:11;7834:19;7826:6;7822:32;7811:9;7804:51;7891:6;7886:2;7875:9;7871:18;7864:34;7934:2;7929;7918:9;7914:18;7907:30;7785:4;7954:44;7994:2;7983:9;7979:18;7971:6;7954:44;:::i;:::-;7946:52;7619:385;-1:-1:-1;;;;;7619:385:188:o;8009:343::-;8088:6;8096;8149:2;8137:9;8128:7;8124:23;8120:32;8117:52;;;8165:1;8162;8155:12;8117:52;-1:-1:-1;;8210:16:188;;8316:2;8301:18;;;8295:25;8210:16;;8295:25;;-1:-1:-1;8009:343:188:o;8357:458::-;8617:1;8613;8608:3;8604:11;8600:19;8592:6;8588:32;8577:9;8570:51;8657:6;8652:2;8641:9;8637:18;8630:34;8700:6;8695:2;8684:9;8680:18;8673:34;8743:3;8738:2;8727:9;8723:18;8716:31;8551:4;8764:45;8804:3;8793:9;8789:19;8781:6;8764:45;:::i;:::-;8756:53;8357:458;-1:-1:-1;;;;;;8357:458:188:o;8820:417::-;8931:6;8939;8992:2;8980:9;8971:7;8967:23;8963:32;8960:52;;;9008:1;9005;8998:12;8960:52;9040:9;9034:16;9059:31;9084:5;9059:31;:::i;:::-;9159:2;9144:18;;9138:25;9109:5;;-1:-1:-1;9172:33:188;9138:25;9172:33;:::i", "linkReferences": {}}, "methodIdentifiers": {"ammalgamBorrowCallV1(address,uint256,uint256,uint256,uint256,bytes)": "f0a49494", "ammalgamBorrowLiquidityCallV1(address,uint256,uint256,uint256,bytes)": "4f2799a9", "ammalgamLiquidateCallV1(uint256,uint256)": "b9a2ff54", "ammalgamSwapCallV1(address,uint256,uint256,bytes)": "4e8eced1", "approveDelegation(address)": "5e305d79", "borrow(address,address,address,uint256,uint256)": "b0054dd4", "borrowLiquidity(address,address,address,uint256)": "6ea70f3a", "delegationAllowance(address,address)": "9ae299d2"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"SafeERC20FailedOperation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"borrowedXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrowedYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrowedXShares\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrowedYShares\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"ammalgamBorrowCallV1\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"borrowedXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrowedYAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"borrowedLShares\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"ammalgamBorrowLiquidityCallV1\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"repayXInXAssets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"repayYInYAssets\",\"type\":\"uint256\"}],\"name\":\"ammalgamLiquidateCallV1\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountX\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountY\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"ammalgamSwapCallV1\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"delegatee\",\"type\":\"address\"}],\"name\":\"approveDelegation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalfOf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"contract IAmmalgamPair\",\"name\":\"pair\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountX\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"amountY\",\"type\":\"uint256\"}],\"name\":\"borrow\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalfOf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"contract IAmmalgamPair\",\"name\":\"pair\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amountLShares\",\"type\":\"uint256\"}],\"name\":\"borrowLiquidity\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"onBehalfOf\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"delegatee\",\"type\":\"address\"}],\"name\":\"delegationAllowance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"SafeERC20FailedOperation(address)\":[{\"details\":\"An operation with an ERC-20 token failed.\"}]},\"kind\":\"dev\",\"methods\":{\"ammalgamLiquidateCallV1(uint256,uint256)\":{\"params\":{\"repayXInXAssets\":\"The amount of token X the liquidator should transfer to the pair.\",\"repayYInYAssets\":\"The amount of token Y the liquidator should transfer to the pair.\"}}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"ammalgamLiquidateCallV1(uint256,uint256)\":{\"notice\":\"Handles a liquidate call in the Ammalgam protocol. The callback is expected to transfer repayXInXAssets and repayYInYAssets from the liquidator to the pair.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/example/PeripheralDelegationContractExample.sol\":\"PeripheralDelegationContractExample\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/interfaces/IAmmalgamPair.sol\":{\"keccak256\":\"0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795\",\"dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw\"]},\"contracts/interfaces/callbacks/IAmmalgamCallee.sol\":{\"keccak256\":\"0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d\",\"dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1\"]},\"contracts/interfaces/callbacks/ITransferValidator.sol\":{\"keccak256\":\"0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4\",\"dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/IERC20DebtToken.sol\":{\"keccak256\":\"0xc50c6be17633c8ac677b4eaac7c05a6de1f1c938237179b59ad5e65bcfbcb03a\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://6c75327e01d70a0c22662a9a8214aa64e45c517146971f8636e5aa5bf06e7696\",\"dweb:/ipfs/QmV2ydBQ5S9ZBtRuPgBMBdvd2Hcnn8quCGEMhhAAuic15b\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41\",\"dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb\",\"dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5\",\"dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"test/example/PeripheralDelegationContractExample.sol\":{\"keccak256\":\"0xf212fd0b2dd3a358c826623bd320e3aa0630892b9f6ba777b8126d3e2cfcfb14\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://2ad79b2d7eb1b46c69383b9e9090bf2031ff779e46637d850b881db3dc84d797\",\"dweb:/ipfs/QmTPx8qw1zdYXRzjBnmuzMCt8yiErwFiLBk47xnbTm1erP\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "type": "error", "name": "SafeERC20FailedOperation"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "borrowedXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "borrowedYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "borrowedXShares", "type": "uint256"}, {"internalType": "uint256", "name": "borrowedYShares", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "ammalgamBorrowCallV1"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "borrowedXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "borrowedYAssets", "type": "uint256"}, {"internalType": "uint256", "name": "borrowedLShares", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "ammalgamBorrowLiquidityCallV1"}, {"inputs": [{"internalType": "uint256", "name": "repayXInXAssets", "type": "uint256"}, {"internalType": "uint256", "name": "repayYInYAssets", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "ammalgamLiquidateCallV1"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "amountX", "type": "uint256"}, {"internalType": "uint256", "name": "amountY", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "ammalgamSwapCallV1"}, {"inputs": [{"internalType": "address", "name": "delegatee", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "approveDelegation"}, {"inputs": [{"internalType": "address", "name": "onBehalfOf", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "contract IAmmalgamPair", "name": "pair", "type": "address"}, {"internalType": "uint256", "name": "amountX", "type": "uint256"}, {"internalType": "uint256", "name": "amountY", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "borrow"}, {"inputs": [{"internalType": "address", "name": "onBehalfOf", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "contract IAmmalgamPair", "name": "pair", "type": "address"}, {"internalType": "uint256", "name": "amountLShares", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "borrowLiquidity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "onBehalfOf", "type": "address"}, {"internalType": "address", "name": "delegatee", "type": "address"}], "stateMutability": "view", "type": "function", "name": "delegationAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {"ammalgamLiquidateCallV1(uint256,uint256)": {"params": {"repayXInXAssets": "The amount of token X the liquidator should transfer to the pair.", "repayYInYAssets": "The amount of token Y the liquidator should transfer to the pair."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"ammalgamLiquidateCallV1(uint256,uint256)": {"notice": "Handles a liquidate call in the Ammalgam protocol. The callback is expected to transfer repayXInXAssets and repayYInYAssets from the liquidator to the pair."}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/example/PeripheralDelegationContractExample.sol": "PeripheralDelegationContractExample"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/interfaces/IAmmalgamPair.sol": {"keccak256": "0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337", "urls": ["bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795", "dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/IAmmalgamCallee.sol": {"keccak256": "0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339", "urls": ["bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d", "dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/ITransferValidator.sol": {"keccak256": "0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56", "urls": ["bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4", "dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IERC20DebtToken.sol": {"keccak256": "0xc50c6be17633c8ac677b4eaac7c05a6de1f1c938237179b59ad5e65bcfbcb03a", "urls": ["bzz-raw://6c75327e01d70a0c22662a9a8214aa64e45c517146971f8636e5aa5bf06e7696", "dweb:/ipfs/QmV2ydBQ5S9ZBtRuPgBMBdvd2Hcnn8quCGEMhhAAuic15b"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da", "urls": ["bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41", "dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b", "urls": ["bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb", "dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2", "urls": ["bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5", "dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "test/example/PeripheralDelegationContractExample.sol": {"keccak256": "0xf212fd0b2dd3a358c826623bd320e3aa0630892b9f6ba777b8126d3e2cfcfb14", "urls": ["bzz-raw://2ad79b2d7eb1b46c69383b9e9090bf2031ff779e46637d850b881db3dc84d797", "dweb:/ipfs/QmTPx8qw1zdYXRzjBnmuzMCt8yiErwFiLBk47xnbTm1erP"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 172}