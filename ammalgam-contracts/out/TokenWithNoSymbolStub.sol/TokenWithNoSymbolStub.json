{"abi": [], "bytecode": {"object": "0x6080604052348015600e575f5ffd5b50603e80601a5f395ff3fe60806040525f5ffdfea2646970667358221220903377b67e81caf39680db551e0c55a1cbd427a57e0c6f89fa2e3675f390e05564736f6c634300081c0033", "sourceMap": "67:33:183:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x60806040525f5ffdfea2646970667358221220903377b67e81caf39680db551e0c55a1cbd427a57e0c6f89fa2e3675f390e05564736f6c634300081c0033", "sourceMap": "67:33:183:-:0;;;;;", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/stubs/TokenWithNoSymbolStub.sol\":\"TokenWithNoSymbolStub\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"test/stubs/TokenWithNoSymbolStub.sol\":{\"keccak256\":\"0x88414d53422f006bc77cc30b4485742768760e6d38b924f2202bcb582fc72dea\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5d781795d8cf06a77735c9d8588a7a3f90deed9ef601e4aadf97ff200def7339\",\"dweb:/ipfs/QmTcuNgf2ymjUP2yv79u1KUEh2UkPUCjzgiicMfXawQQES\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/stubs/TokenWithNoSymbolStub.sol": "TokenWithNoSymbolStub"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"test/stubs/TokenWithNoSymbolStub.sol": {"keccak256": "0x88414d53422f006bc77cc30b4485742768760e6d38b924f2202bcb582fc72dea", "urls": ["bzz-raw://5d781795d8cf06a77735c9d8588a7a3f90deed9ef601e4aadf97ff200def7339", "dweb:/ipfs/QmTcuNgf2ymjUP2yv79u1KUEh2UkPUCjzgiicMfXawQQES"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 183}