{"abi": [{"type": "constructor", "inputs": [{"name": "_feeToSetter", "type": "address", "internalType": "address"}, {"name": "_tokenFactory", "type": "address", "internalType": "address"}, {"name": "_pairFactory", "type": "address", "internalType": "address"}, {"name": "_pluginRegistry", "type": "address", "internalType": "address"}, {"name": "_saturationAndGeometricTWAPState", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "allPairs", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "allPairsLength", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "createPair", "inputs": [{"name": "tokenA", "type": "address", "internalType": "address"}, {"name": "tokenB", "type": "address", "internalType": "address"}], "outputs": [{"name": "pair", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "feeTo", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "feeToSetter", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "generateTokensWithinFactory", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IERC20"}, {"name": "", "type": "address", "internalType": "contract IERC20"}, {"name": "", "type": "address[6]", "internalType": "contract IAmmalgamERC20[6]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "getPair", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pairFactory", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "pluginRegistry", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "saturationAndGeometricTWAPState", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ISaturationAndGeometricTWAPState"}], "stateMutability": "view"}, {"type": "function", "name": "setFeeTo", "inputs": [{"name": "newFeeTo", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setFeeToSetter", "inputs": [{"name": "newFeeToSetter", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "tokenFactory", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "event", "name": "LendingTokensCreated", "inputs": [{"name": "pair", "type": "address", "indexed": true, "internalType": "address"}, {"name": "depositL", "type": "address", "indexed": false, "internalType": "address"}, {"name": "depositX", "type": "address", "indexed": false, "internalType": "address"}, {"name": "depositY", "type": "address", "indexed": false, "internalType": "address"}, {"name": "borrowL", "type": "address", "indexed": false, "internalType": "address"}, {"name": "borrowX", "type": "address", "indexed": false, "internalType": "address"}, {"name": "borrowY", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewFeeTo", "inputs": [{"name": "feeTo", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewFeeToSetter", "inputs": [{"name": "feeToSetter", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PairCreated", "inputs": [{"name": "tokenX", "type": "address", "indexed": true, "internalType": "address"}, {"name": "tokenY", "type": "address", "indexed": true, "internalType": "address"}, {"name": "pair", "type": "address", "indexed": false, "internalType": "address"}, {"name": "allPairsLength", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "BytecodeLengthZero", "inputs": []}, {"type": "error", "name": "FailedOnDeploy", "inputs": []}, {"type": "error", "name": "FeeToIsZeroAddress", "inputs": []}, {"type": "error", "name": "FeeToSetterIsZeroAddress", "inputs": []}, {"type": "error", "name": "Forbidden", "inputs": []}, {"type": "error", "name": "IdenticalAddresses", "inputs": []}, {"type": "error", "name": "NewTokensFailed", "inputs": []}, {"type": "error", "name": "PairExists", "inputs": []}, {"type": "error", "name": "ZeroAddress", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "721:5157:2:-:0;;;1702:826;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;1908:28:2;;1904:92;;1959:26;;-1:-1:-1;;;1959:26:2;;;;;;;;;;;1904:92;-1:-1:-1;;;;;2009:29:2;;;;:61;;-1:-1:-1;;;;;;2042:28:2;;;2009:61;:96;;;-1:-1:-1;;;;;;2074:31:2;;;2009:96;2005:147;;;2128:13;;-1:-1:-1;;;2128:13:2;;;;;;;;;;;2005:147;-1:-1:-1;;;;;2165:48:2;;2161:99;;2236:13;;-1:-1:-1;;;2236:13:2;;;;;;;;;;;2161:99;2269:11;:26;;-1:-1:-1;;;;;;2269:26:2;-1:-1:-1;;;;;2269:26:2;;;;;;2305:28;;;;;2343:26;;;;;2379:32;;;;2421:100;;;721:5157;;14:177:188;93:13;;-1:-1:-1;;;;;135:31:188;;125:42;;115:70;;181:1;178;171:12;115:70;14:177;;;:::o;196:550::-;302:6;310;318;326;334;387:3;375:9;366:7;362:23;358:33;355:53;;;404:1;401;394:12;355:53;427:40;457:9;427:40;:::i;:::-;417:50;;486:49;531:2;520:9;516:18;486:49;:::i;:::-;476:59;;554:49;599:2;588:9;584:18;554:49;:::i;:::-;544:59;;622:49;667:2;656:9;652:18;622:49;:::i;:::-;612:59;;690:50;735:3;724:9;720:19;690:50;:::i;:::-;680:60;;196:550;;;;;;;;:::o;:::-;721:5157:2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "721:5157:2:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;902:20;;;;;-1:-1:-1;;;;;902:20:2;;;;;;-1:-1:-1;;;;;178:32:188;;;160:51;;148:2;133:18;902:20:2;;;;;;;;928:26;;;;;-1:-1:-1;;;;;928:26:2;;;1173:25;;;;;;:::i;:::-;;:::i;4552:800::-;;;:::i;:::-;;;;;;;;;:::i;857:39::-;;;;;2534:97;2609:8;:15;2534:97;;1295:25:188;;;1283:2;1268:18;2534:97:2;1149:177:188;5596:280:2;;;;;;:::i;:::-;;:::i;:::-;;960:81;;;;;2637:1630;;;;;;:::i;:::-;;:::i;815:36::-;;;;;1105:62;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1105:62:2;;;772:37;;;;;5358:232;;;;;;:::i;:::-;;:::i;1173:25::-;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;1173:25:2;;-1:-1:-1;1173:25:2;:::o;4552:800::-;4609:6;4617;4625:24;;:::i;:::-;4661:50;4714:11;:9;:11::i;:::-;4923:14;;4939;;;;4847:107;;-1:-1:-1;;;4847:107:2;;4895:10;4847:107;;;2592:51:188;-1:-1:-1;;;;;4907:14:2;2679:32:188;;2659:18;;;2652:60;2748:32;;;2728:18;;;2721:60;2817:32;;;2797:18;;;2790:60;4923:14:2;;-1:-1:-1;;;4865:12:2;4847:47;;;;;;2564:19:188;;4847:107:2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;4801:153;-1:-1:-1;5059:10:2;5025:246;4801:153;5098:1;5091:9;;;;5123:6;5130:1;5123:9;;;;5155:6;5162:1;5155:9;;;;5187:6;5194:1;5187:9;;;;5219:6;5226:1;5219:9;;;;5251:6;5258:1;5251:9;;;;5025:246;;;;;;;;;;-1:-1:-1;;;;;4422:32:188;;;4404:51;;4491:32;;;4486:2;4471:18;;4464:60;4560:32;;;4555:2;4540:18;;4533:60;4629:32;;;4624:2;4609:18;;4602:60;4699:32;;4693:3;4678:19;;4671:61;4769:32;;;4442:3;4748:19;;4741:61;4391:3;4376:19;;4117:691;5025:246:2;;;;;;;;5297:14;;5321;;;;;5297;;5321;;-1:-1:-1;5338:6:2;-1:-1:-1;4552:800:2;-1:-1:-1;4552:800:2:o;5596:280::-;1623:11;;-1:-1:-1;;;;;1623:11:2;1609:10;:25;1605:74;;1657:11;;-1:-1:-1;;;1657:11:2;;;;;;;;;;;1605:74;-1:-1:-1;;;;;5697:30:2;::::1;5693:94;;5750:26;;-1:-1:-1::0;;;5750:26:2::1;;;;;;;;;;;5693:94;5796:11;:28:::0;;-1:-1:-1;;;;;;5796:28:2::1;-1:-1:-1::0;;;;;5796:28:2;::::1;::::0;;::::1;::::0;;;5839:30:::1;::::0;::::1;::::0;-1:-1:-1;;5839:30:2::1;5596:280:::0;:::o;2637:1630::-;2707:12;2745:6;-1:-1:-1;;;;;2735:16:2;:6;-1:-1:-1;;;;;2735:16:2;;2731:74;;2774:20;;-1:-1:-1;;;2774:20:2;;;;;;;;;;;2731:74;2815:14;2831;2858:6;-1:-1:-1;;;;;2849:15:2;:6;-1:-1:-1;;;;;2849:15:2;;:53;;2887:6;2895;2849:53;;;2868:6;2876;2849:53;2814:88;;-1:-1:-1;2814:88:2;-1:-1:-1;;;;;;2917:22:2;;2913:73;;2962:13;;-1:-1:-1;;;2962:13:2;;;;;;;;;;;2913:73;-1:-1:-1;;;;;3000:15:2;;;2144:1:30;3000:15:2;;;:7;:15;;;;;;;;:23;;;;;;;;;;;;:39;2996:131;;3104:12;;-1:-1:-1;;;3104:12:2;;;;;;;;;;;2996:131;3162:32;;;4990:2:188;4986:15;;;-1:-1:-1;;4982:53:188;;;3162:32:2;;;;4970:66:188;;;;5070:15;;;;5066:53;5052:12;;;5045:75;3162:32:2;;;;;;;;;5136:12:188;;;3162:32:2;;;3152:43;;;;;;;;;3215:92;;;;;-1:-1:-1;;;;;3215:92:2;;;;;;;;;;;;;;;;3293:12;3215:92;;;;;;;;;3206:6;:101;;-1:-1:-1;;;;;;3206:101:2;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3628:62;;;;;;1295:25:188;;;3628:62:2;;;;;;;;;;-1:-1:-1;1268:18:188;;;3628:62:2;;;;;;;-1:-1:-1;;;;;3628:62:2;-1:-1:-1;;;3628:62:2;;;3603:88;;-1:-1:-1;;;;3603:11:2;:24;;;;:88;;3628:62;3603:88;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;3555:136;;;;3790:4;3779:27;;;;;;;;;;;;:::i;:::-;3772:34;;3821:7;3820:8;:32;;;-1:-1:-1;;;;;;3832:20:2;;;3820:32;3816:86;;;3875:16;;-1:-1:-1;;;3875:16:2;;;;;;;;;;;3816:86;3919:6;3912:13;;-1:-1:-1;;;;;;3912:13:2;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;4002:15:2;;;-1:-1:-1;4002:15:2;;;:7;:15;;;;;;;;:23;;;;;;;;;;;;:30;;;;;;;;;;;;4042:15;;;;;;:23;;;;;;;;:30;;;;;;;;4127:8;:19;;-1:-1:-1;4127:19:2;;;;;;;;;;;;;;;;;;;;;;4195:15;;4161:50;;6085:51:188;;;6152:18;;;6145:34;;;;4002:23:2;:15;;4161:50;;6058:18:188;4161:50:2;;;;;;;2721:1546;;;;;2637:1630;;;;:::o;5358:232::-;1623:11;;-1:-1:-1;;;;;1623:11:2;1609:10;:25;1605:74;;1657:11;;-1:-1:-1;;;1657:11:2;;;;;;;;;;;1605:74;-1:-1:-1;;;;;5447:24:2;::::1;5443:82;;5494:20;;-1:-1:-1::0;;;5494:20:2::1;;;;;;;;;;;5443:82;5534:5;:16:::0;;-1:-1:-1;;;;;;5534:16:2::1;-1:-1:-1::0;;;;;5534:16:2;::::1;::::0;;::::1;::::0;;5565:18:::1;::::0;5534:16;;5565:18:::1;::::0;::::1;5358:232:::0;:::o;4273:273::-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;4374:6:2;:13;-1:-1:-1;;;;;4374:13:2;:29;;:62;;-1:-1:-1;4407:13:2;;-1:-1:-1;;;;;4407:13:2;:29;4374:62;:96;;;-1:-1:-1;4440:14:2;;-1:-1:-1;;;;;4440:14:2;:30;4374:96;4370:147;;;4493:13;;-1:-1:-1;;;4493:13:2;;;;;;;;;;;4370:147;-1:-1:-1;4526:13:2;;;;;;;;4533:6;4526:13;-1:-1:-1;;;;;4526:13:2;;;;;;;;;;;;;;;;;;;;;;;;4273:273::o;-1:-1:-1:-;;;;;;;;;;;;;;;;;;;;;;;;:::o;222:180:188:-;281:6;334:2;322:9;313:7;309:23;305:32;302:52;;;350:1;347;340:12;302:52;-1:-1:-1;373:23:188;;222:180;-1:-1:-1;222:180:188:o;407:737::-;-1:-1:-1;;;;;729:32:188;;;711:51;;798:32;;793:2;778:18;;771:60;698:3;683:19;;866:2;851:18;;911:6;656:4;945:193;959:4;956:1;953:11;945:193;;;1022:13;;-1:-1:-1;;;;;1018:39:188;1006:52;;1087:2;1078:12;;;;1113:15;;;;1054:1;972:9;945:193;;;949:3;;;407:737;;;;;;:::o;1331:131::-;-1:-1:-1;;;;;1406:31:188;;1396:42;;1386:70;;1452:1;1449;1442:12;1386:70;1331:131;:::o;1467:247::-;1526:6;1579:2;1567:9;1558:7;1554:23;1550:32;1547:52;;;1595:1;1592;1585:12;1547:52;1634:9;1621:23;1653:31;1678:5;1653:31;:::i;:::-;1703:5;1467:247;-1:-1:-1;;;1467:247:188:o;1968:388::-;2036:6;2044;2097:2;2085:9;2076:7;2072:23;2068:32;2065:52;;;2113:1;2110;2103:12;2065:52;2152:9;2139:23;2171:31;2196:5;2171:31;:::i;:::-;2221:5;-1:-1:-1;2278:2:188;2263:18;;2250:32;2291:33;2250:32;2291:33;:::i;:::-;2343:7;2333:17;;;1968:388;;;;;:::o;2861:127::-;2922:10;2917:3;2913:20;2910:1;2903:31;2953:4;2950:1;2943:15;2977:4;2974:1;2967:15;2993:154;3088:13;;3110:31;3088:13;3110:31;:::i;:::-;2993:154;;;:::o;3152:828::-;3268:6;3321:3;3309:9;3300:7;3296:23;3292:33;3289:53;;;3338:1;3335;3328:12;3289:53;3387:7;3380:4;3369:9;3365:20;3361:34;3351:62;;3409:1;3406;3399:12;3351:62;3442:2;3436:9;3484:3;3476:6;3472:16;3554:6;3542:10;3539:22;3518:18;3506:10;3503:34;3500:62;3497:88;;;3565:18;;:::i;:::-;3601:2;3594:22;3636:6;3680:3;3665:19;;3696;;;3693:39;;;3728:1;3725;3718:12;3693:39;3752:9;3770:179;3786:6;3781:3;3778:15;3770:179;;;3854:50;3900:3;3854:50;:::i;:::-;3842:63;;3934:4;3925:14;;;;3803;3770:179;;;-1:-1:-1;3968:6:188;;3152:828;-1:-1:-1;;;;;3152:828:188:o;5341:301::-;5470:3;5508:6;5502:13;5554:6;5547:4;5539:6;5535:17;5530:3;5524:37;5616:1;5580:16;;5605:13;;;-1:-1:-1;5580:16:188;5341:301;-1:-1:-1;5341:301:188:o;5647:259::-;5725:6;5778:2;5766:9;5757:7;5753:23;5749:32;5746:52;;;5794:1;5791;5784:12;5746:52;5826:9;5820:16;5845:31;5870:5;5845:31;:::i", "linkReferences": {}, "immutableReferences": {"3961": [{"start": 545, "length": 32}, {"start": 754, "length": 32}, {"start": 1503, "length": 32}], "3963": [{"start": 455, "length": 32}, {"start": 1649, "length": 32}], "3965": [{"start": 320, "length": 32}, {"start": 693, "length": 32}], "3972": [{"start": 397, "length": 32}]}}, "methodIdentifiers": {"allPairs(uint256)": "1e3dd18b", "allPairsLength()": "574f2ba3", "createPair(address,address)": "c9c65396", "feeTo()": "017e7e58", "feeToSetter()": "094b7415", "generateTokensWithinFactory()": "285c676e", "getPair(address,address)": "e6a43905", "pairFactory()": "e14f870d", "pluginRegistry()": "41dcea91", "saturationAndGeometricTWAPState()": "b3f01a33", "setFeeTo(address)": "f46901ed", "setFeeToSetter(address)": "a2e74af6", "tokenFactory()": "e77772fe"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_feeToSetter\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_tokenFactory\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_pairFactory\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_pluginRegistry\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_saturationAndGeometricTWAPState\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"BytecodeLengthZero\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FailedOnDeploy\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FeeToIsZeroAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FeeToSetterIsZeroAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"Forbidden\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"IdenticalAddresses\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NewTokensFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PairExists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroAddress\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"pair\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"depositL\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"depositX\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"depositY\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"borrowL\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"borrowX\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"borrowY\",\"type\":\"address\"}],\"name\":\"LendingTokensCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"feeTo\",\"type\":\"address\"}],\"name\":\"NewFeeTo\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"feeToSetter\",\"type\":\"address\"}],\"name\":\"NewFeeToSetter\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenX\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"tokenY\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"pair\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"allPairsLength\",\"type\":\"uint256\"}],\"name\":\"PairCreated\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"allPairs\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"allPairsLength\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"tokenA\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"tokenB\",\"type\":\"address\"}],\"name\":\"createPair\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"pair\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"feeTo\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"feeToSetter\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"generateTokensWithinFactory\",\"outputs\":[{\"internalType\":\"contract IERC20\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"contract IERC20\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"contract IAmmalgamERC20[6]\",\"name\":\"\",\"type\":\"address[6]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"getPair\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pairFactory\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pluginRegistry\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"saturationAndGeometricTWAPState\",\"outputs\":[{\"internalType\":\"contract ISaturationAndGeometricTWAPState\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newFeeTo\",\"type\":\"address\"}],\"name\":\"setFeeTo\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newFeeToSetter\",\"type\":\"address\"}],\"name\":\"setFeeToSetter\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"tokenFactory\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"events\":{\"LendingTokensCreated(address,address,address,address,address,address,address)\":{\"params\":{\"borrowL\":\"The address of the `BORROW_L` lending token.\",\"borrowX\":\"The address of the `BORROW_X` lending token.\",\"borrowY\":\"The address of the `BORROW_Y` lending token.\",\"depositL\":\"The address of the `DEPOSIT_L` lending token.\",\"depositX\":\"The address of the `DEPOSIT_X` lending token.\",\"depositY\":\"The address of the `DEPOSIT_Y` lending token.\",\"pair\":\"The address of the pair.\"}},\"PairCreated(address,address,address,uint256)\":{\"params\":{\"allPairsLength\":\"The current total number of token pairs.\",\"pair\":\"The address of the new pair.\",\"tokenX\":\"The first token in the pair.\",\"tokenY\":\"The second token in the pair.\"}}},\"kind\":\"dev\",\"methods\":{\"allPairsLength()\":{\"returns\":{\"_0\":\"The total number of token pairs.\"}},\"createPair(address,address)\":{\"params\":{\"tokenA\":\"The first token.\",\"tokenB\":\"The second token.\"},\"returns\":{\"pair\":\"The address of the new pair.\"}},\"generateTokensWithinFactory()\":{\"returns\":{\"_0\":\"A TokenFactoryConfig struct representing the current token factory config.\"}},\"setFeeTo(address)\":{\"params\":{\"newFeeTo\":\"The new fee recipient address.\"}},\"setFeeToSetter(address)\":{\"params\":{\"newFeeToSetter\":\"The new fee setter address.\"}}},\"stateVariables\":{\"allPairs\":{\"params\":{\"index\":\"The index of the pair.\"},\"return\":\"The address of the pair at the given index.\",\"returns\":{\"_0\":\"The address of the pair at the given index.\"}},\"feeTo\":{\"return\":\"The address of the fee recipient.\",\"returns\":{\"_0\":\"The address of the fee recipient.\"}},\"feeToSetter\":{\"return\":\"The address of the fee setter.\",\"returns\":{\"_0\":\"The address of the fee setter.\"}},\"getPair\":{\"params\":{\"tokenA\":\"The first token.\",\"tokenB\":\"The second token.\"},\"return\":\"The address of the pair for the two tokens.\",\"returns\":{\"_0\":\"The address of the pair for the two tokens.\"}}},\"version\":1},\"userdoc\":{\"events\":{\"LendingTokensCreated(address,address,address,address,address,address,address)\":{\"notice\":\"Emitted when new lending tokens are created.\"},\"PairCreated(address,address,address,uint256)\":{\"notice\":\"Emitted when a new pair is created.\"}},\"kind\":\"user\",\"methods\":{\"allPairs(uint256)\":{\"notice\":\"Returns the pair address at a specific index.\"},\"allPairsLength()\":{\"notice\":\"Returns the total number of token pairs.\"},\"createPair(address,address)\":{\"notice\":\"Creates a new pair for two tokens.\"},\"feeTo()\":{\"notice\":\"Returns the fee recipient address.\"},\"feeToSetter()\":{\"notice\":\"Returns the address that can change the fee recipient.\"},\"generateTokensWithinFactory()\":{\"notice\":\"Returns the current token factory configuration.\"},\"getPair(address,address)\":{\"notice\":\"Returns the pair address for two tokens.\"},\"saturationAndGeometricTWAPState()\":{\"notice\":\"Returns the address of the saturation state contract\"},\"setFeeTo(address)\":{\"notice\":\"Changes the fee recipient address.\"},\"setFeeToSetter(address)\":{\"notice\":\"Changes the address that can change the fee recipient.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/factories/AmmalgamFactory.sol\":\"AmmalgamFactory\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/AmmalgamPair.sol\":{\"keccak256\":\"0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4\",\"urls\":[\"bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2\",\"dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS\"]},\"contracts/SaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76\",\"dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE\"]},\"contracts/factories/AmmalgamFactory.sol\":{\"keccak256\":\"0xe0d9baf63d9538a7ecb8bd24ea61a8cdf6fc9c1e9eb028f343548adeb8b93e4e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://bfca24db47fbbaeef8bc36996cbfed78eb48771ac71d2800f081fb58a8e8c92b\",\"dweb:/ipfs/QmUfYEwfmrjSmchqXi58SnpSina4qKQvD2Jbk5RqYsaoCa\"]},\"contracts/interfaces/IAmmalgamPair.sol\":{\"keccak256\":\"0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795\",\"dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw\"]},\"contracts/interfaces/ISaturationAndGeometricTWAPState.sol\":{\"keccak256\":\"0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20\",\"dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR\"]},\"contracts/interfaces/callbacks/IAmmalgamCallee.sol\":{\"keccak256\":\"0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d\",\"dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1\"]},\"contracts/interfaces/callbacks/ITransferValidator.sol\":{\"keccak256\":\"0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4\",\"dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8\"]},\"contracts/interfaces/factories/IAmmalgamFactory.sol\":{\"keccak256\":\"0x1c80089901e8d7d7451775b5eaa92092eb2b65319cb92fa7884281bae49f52b8\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://bf1201399bb9d5046e0e788ff88394b2468771096a2a0d3500af542923e84628\",\"dweb:/ipfs/QmeUaPyQpbPbP5fyPUT2FfzeDgHcdyQAn1DaNg9uCuGoj9\"]},\"contracts/interfaces/factories/IFactoryCallback.sol\":{\"keccak256\":\"0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b\",\"dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT\"]},\"contracts/interfaces/factories/INewTokensFactory.sol\":{\"keccak256\":\"0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b\",\"dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/Convert.sol\":{\"keccak256\":\"0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c\",\"dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH\"]},\"contracts/libraries/GeometricTWAP.sol\":{\"keccak256\":\"0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa\",\"dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz\"]},\"contracts/libraries/Interest.sol\":{\"keccak256\":\"0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194\",\"dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX\"]},\"contracts/libraries/Liquidation.sol\":{\"keccak256\":\"0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877\",\"dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW\"]},\"contracts/libraries/QuadraticSwapFees.sol\":{\"keccak256\":\"0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb\",\"dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3\"]},\"contracts/libraries/Saturation.sol\":{\"keccak256\":\"0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20\",\"dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE\"]},\"contracts/libraries/TickMath.sol\":{\"keccak256\":\"0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20\",\"dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG\"]},\"contracts/libraries/Uint16Set.sol\":{\"keccak256\":\"0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06\",\"dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy\"]},\"contracts/libraries/Validation.sol\":{\"keccak256\":\"0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778\",\"dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"contracts/tokens/TokenController.sol\":{\"keccak256\":\"0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159\",\"dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn\"]},\"lib/mangrove-core/lib/core/BitLib.sol\":{\"keccak256\":\"0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8\",\"dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr\"]},\"lib/morpho-blue/src/libraries/MathLib.sol\":{\"keccak256\":\"0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a\",\"license\":\"GPL-2.0-or-later\",\"urls\":[\"bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08\",\"dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41\",\"dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb\",\"dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5\",\"dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_feeToSetter", "type": "address"}, {"internalType": "address", "name": "_tokenFactory", "type": "address"}, {"internalType": "address", "name": "_pairFactory", "type": "address"}, {"internalType": "address", "name": "_pluginRegistry", "type": "address"}, {"internalType": "address", "name": "_saturationAndGeometricTWAPState", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "BytecodeLengthZero"}, {"inputs": [], "type": "error", "name": "FailedOnDeploy"}, {"inputs": [], "type": "error", "name": "FeeToIsZeroAddress"}, {"inputs": [], "type": "error", "name": "FeeToSetterIsZeroAddress"}, {"inputs": [], "type": "error", "name": "Forbidden"}, {"inputs": [], "type": "error", "name": "IdenticalAddresses"}, {"inputs": [], "type": "error", "name": "NewTokensFailed"}, {"inputs": [], "type": "error", "name": "PairExists"}, {"inputs": [], "type": "error", "name": "ZeroAddress"}, {"inputs": [{"internalType": "address", "name": "pair", "type": "address", "indexed": true}, {"internalType": "address", "name": "depositL", "type": "address", "indexed": false}, {"internalType": "address", "name": "depositX", "type": "address", "indexed": false}, {"internalType": "address", "name": "depositY", "type": "address", "indexed": false}, {"internalType": "address", "name": "borrowL", "type": "address", "indexed": false}, {"internalType": "address", "name": "borrowX", "type": "address", "indexed": false}, {"internalType": "address", "name": "borrowY", "type": "address", "indexed": false}], "type": "event", "name": "LendingTokensCreated", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "feeTo", "type": "address", "indexed": true}], "type": "event", "name": "NewFeeTo", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "feeToSetter", "type": "address", "indexed": true}], "type": "event", "name": "NewFeeToSetter", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "tokenX", "type": "address", "indexed": true}, {"internalType": "address", "name": "tokenY", "type": "address", "indexed": true}, {"internalType": "address", "name": "pair", "type": "address", "indexed": false}, {"internalType": "uint256", "name": "allPairsLength", "type": "uint256", "indexed": false}], "type": "event", "name": "PairCreated", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "allPairs", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "allPairsLength", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "tokenA", "type": "address"}, {"internalType": "address", "name": "tokenB", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "createPair", "outputs": [{"internalType": "address", "name": "pair", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "feeTo", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "feeToSetter", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "generateTokensWithinFactory", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}, {"internalType": "contract IERC20", "name": "", "type": "address"}, {"internalType": "contract IAmmalgamERC20[6]", "name": "", "type": "address[6]"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function", "name": "getPair", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pairFactory", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pluginRegistry", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "saturationAndGeometricTWAPState", "outputs": [{"internalType": "contract ISaturationAndGeometricTWAPState", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "newFeeTo", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setFeeTo"}, {"inputs": [{"internalType": "address", "name": "newFeeToSetter", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "setFeeToSetter"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "tokenFactory", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}], "devdoc": {"kind": "dev", "methods": {"allPairsLength()": {"returns": {"_0": "The total number of token pairs."}}, "createPair(address,address)": {"params": {"tokenA": "The first token.", "tokenB": "The second token."}, "returns": {"pair": "The address of the new pair."}}, "generateTokensWithinFactory()": {"returns": {"_0": "A TokenFactoryConfig struct representing the current token factory config."}}, "setFeeTo(address)": {"params": {"newFeeTo": "The new fee recipient address."}}, "setFeeToSetter(address)": {"params": {"newFeeToSetter": "The new fee setter address."}}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"allPairs(uint256)": {"notice": "Returns the pair address at a specific index."}, "allPairsLength()": {"notice": "Returns the total number of token pairs."}, "createPair(address,address)": {"notice": "Creates a new pair for two tokens."}, "feeTo()": {"notice": "Returns the fee recipient address."}, "feeToSetter()": {"notice": "Returns the address that can change the fee recipient."}, "generateTokensWithinFactory()": {"notice": "Returns the current token factory configuration."}, "getPair(address,address)": {"notice": "Returns the pair address for two tokens."}, "saturationAndGeometricTWAPState()": {"notice": "Returns the address of the saturation state contract"}, "setFeeTo(address)": {"notice": "Changes the fee recipient address."}, "setFeeToSetter(address)": {"notice": "Changes the address that can change the fee recipient."}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/factories/AmmalgamFactory.sol": "AmmalgamFactory"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/AmmalgamPair.sol": {"keccak256": "0xe8f98844a55a216605e6c6dd6837977fafda458a6b5d0cfa1f3a18d25e9432e4", "urls": ["bzz-raw://65dda1a1de8dd64e31c666b13de3d0583b4b0da923c67065cadcddefe47562a2", "dweb:/ipfs/Qmaev9WFa4yyL8fXVoWkXwNsTTY8wY7jTBGDoKJbdwSCzS"], "license": null}, "contracts/SaturationAndGeometricTWAPState.sol": {"keccak256": "0x5e293a35668bb216a99379ea2176894314cc0f1ac68644fcf4c07017da1a4419", "urls": ["bzz-raw://00349bb86f1b657010919b4bc3f616ad56ef4883b99ab0eead36815dae93dc76", "dweb:/ipfs/QmbEd9GD2JxuDntX35YcfbSCcpRstDU9GDPUkBKGzsxvqE"], "license": "GPL-3.0-only"}, "contracts/factories/AmmalgamFactory.sol": {"keccak256": "0xe0d9baf63d9538a7ecb8bd24ea61a8cdf6fc9c1e9eb028f343548adeb8b93e4e", "urls": ["bzz-raw://bfca24db47fbbaeef8bc36996cbfed78eb48771ac71d2800f081fb58a8e8c92b", "dweb:/ipfs/QmUfYEwfmrjSmchqXi58SnpSina4qKQvD2Jbk5RqYsaoCa"], "license": "GPL-3.0-only"}, "contracts/interfaces/IAmmalgamPair.sol": {"keccak256": "0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337", "urls": ["bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795", "dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw"], "license": "GPL-3.0-only"}, "contracts/interfaces/ISaturationAndGeometricTWAPState.sol": {"keccak256": "0xc9add2ad41f8edd9d360ced8d2cd7bd18dd500304794434fb2e309fa0f5af83c", "urls": ["bzz-raw://8ecc810c544ac734ef26a2f6bebea3f3bd12d773965d297991e0e0e72892fa20", "dweb:/ipfs/QmarXc1Ut4FZzPRRZs2M2udbJjuZUJQHQ8fvmSr3bpHErR"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/IAmmalgamCallee.sol": {"keccak256": "0x904b858859d460a61c9e644ca87009d8e32ba20482ef218801c89c7fb1ece339", "urls": ["bzz-raw://1a7cedebbacc453e3e4e339fcc76fd3268247c13982de82b4930d59a44256c1d", "dweb:/ipfs/QmdjdvYabbwAYcV72xjiXyq278xQivFtiqD3eQ5P9Gk4f1"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/ITransferValidator.sol": {"keccak256": "0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56", "urls": ["bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4", "dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IAmmalgamFactory.sol": {"keccak256": "0x1c80089901e8d7d7451775b5eaa92092eb2b65319cb92fa7884281bae49f52b8", "urls": ["bzz-raw://bf1201399bb9d5046e0e788ff88394b2468771096a2a0d3500af542923e84628", "dweb:/ipfs/QmeUaPyQpbPbP5fyPUT2FfzeDgHcdyQAn1DaNg9uCuGoj9"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/IFactoryCallback.sol": {"keccak256": "0x33250cf8351adb4846a3d133a9bc06568288e4c680bcf5b1085e3bca40a35e52", "urls": ["bzz-raw://5663a39af4ed3040a58beaa5641425b9adca83c2683dd220e0c11e644fefe52b", "dweb:/ipfs/QmYB8Vf37WDzQfSpMDjv8hVicuaF1wMBzf7xjHRjGCy3wT"], "license": "GPL-3.0-only"}, "contracts/interfaces/factories/INewTokensFactory.sol": {"keccak256": "0x3b2f1ee34106d2694a9ebbe600be692bed645f4247f4a24da3d5ec46025ab3e9", "urls": ["bzz-raw://73143452a06db52deb593585fea6f2ef7c46e9ef6d649562dc39e79e4e5dca2b", "dweb:/ipfs/QmYQEy7BZWnfWKnuac8GB4QPhG5qJpaHQAfkTBoUDEuX1E"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/Convert.sol": {"keccak256": "0x944776d31291de1a9cdc6a52154c23c22b43a01c3edebe7a4140e267edbba975", "urls": ["bzz-raw://36c03749859077ba47a3acfd574f8c30f34f97def4ce81d7f4feac9a7b62794c", "dweb:/ipfs/QmdycZay5X2WrbS8qS7RycLpZbMQx7yKszWQzGU3rqidpH"], "license": "GPL-3.0-only"}, "contracts/libraries/GeometricTWAP.sol": {"keccak256": "0x3860409daa0fdb5d96f0bfb8b49cbca058b9fe32c8e32457f85d4ee2c5cdcb1e", "urls": ["bzz-raw://81fe70a80f4005a6529c7e93f1a92547ce0bf74c357c280a91e8778b378b18aa", "dweb:/ipfs/QmdRQ1DqsCu11zfbLAbrrzJ9Ups3oKgTGimYo3Zm3ALiCz"], "license": "GPL-3.0-only"}, "contracts/libraries/Interest.sol": {"keccak256": "0xbc8bfa20d7295dd70e3c716fd3dbeb5b45d313e3c609d063d186042cbf000646", "urls": ["bzz-raw://b015e8d4976d3b6d7eaca07dfcc487aeed3a7d8b4c41c8369a7476dcfb211194", "dweb:/ipfs/QmecH84UnZYxDZ2aL6rQtnrEExLEAfo7q4Y47yuBXdymeX"], "license": "GPL-3.0-only"}, "contracts/libraries/Liquidation.sol": {"keccak256": "0x842bc44bc3cff80360ab82c5920070b12680edefe9267bdffc2d6c3c3a692d63", "urls": ["bzz-raw://85ecd75568a0729aec06741d0575ed07dad8b7daebd7ba3114a93f6019250877", "dweb:/ipfs/QmQMvWdsPWsQ4t1yv6eyZy5TM7h1EZpSJdt5b8fDLcumCW"], "license": "GPL-3.0-only"}, "contracts/libraries/QuadraticSwapFees.sol": {"keccak256": "0x00f6b7909be4fa1fc1ba426dd8ae659d1c5cb20c79665148898c973f55cfdccb", "urls": ["bzz-raw://c64da0826a9b0ffc08319709f6db03339d22d24deda902a6540393251da0aecb", "dweb:/ipfs/QmSNwBbn2VAS8HPY4hNZusEc4DoKKZAZHtpPdjL9Gz3gs3"], "license": "GPL-3.0-only"}, "contracts/libraries/Saturation.sol": {"keccak256": "0xf44bc610ece4bc7ebdb0730aa6ad69ea47647e19d4c1944c663d2d2eb4f10860", "urls": ["bzz-raw://421fdf8d0b27132bc324a42ede9aaf23b476e5134e1073f84e824620a2a44f20", "dweb:/ipfs/QmbvSfMuMzDmrfPkCAEp7ydtRDWu5EUiXq4MyrGGjFErzE"], "license": "GPL-3.0-only"}, "contracts/libraries/TickMath.sol": {"keccak256": "0x753813c7ed638d22edb71f48f8eb8b4283b3db2ba5b136b5c8909bd37ffa3f12", "urls": ["bzz-raw://04dd5085b72f6d73e1b17f58148e4d03639f654bdc4fdbc173b7c92ff102fc20", "dweb:/ipfs/QmSg4xTQPkngjNxs84428FZdSwH4AUQpwLXaASx7Qev6oG"], "license": "GPL-2.0-or-later"}, "contracts/libraries/Uint16Set.sol": {"keccak256": "0x26a714430fe1618d78386e953153b4bd2bf024baee54453ec9a7a0cc60e1534f", "urls": ["bzz-raw://8667dd78541d656a09678e5f9cce4d49adc805955604ccaaec414e9b241f5e06", "dweb:/ipfs/QmZVWU2CzyDQfGit32HjJxDphBJMKG3d6JRuxbC682Z1gy"], "license": "GPL-3.0-only"}, "contracts/libraries/Validation.sol": {"keccak256": "0x294848b2af973dbcd8b83732a57b67f14fd15e4af0668de05a2928b8eca5a463", "urls": ["bzz-raw://fab25c941e87f6924b31e3f20742ca6b5ec1b7e4251543f4a61567a04ef4d778", "dweb:/ipfs/Qmf4ChH8afdHc3SfXkFPpNGp3e1hscyvnujPAMza3yuXeA"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "contracts/tokens/TokenController.sol": {"keccak256": "0x8b76b9ebb9385f0c4b7c0b8210fb96b11a49a8c9a3a6e855752c32a5c12d54e6", "urls": ["bzz-raw://de87bdae81940f397136f665d68907d4e4c32f35bf2cd0c9e9305a9fe190d159", "dweb:/ipfs/Qmce4hM6xofBYxzAXesHX4hkiHBexoGeQpCzpeCARctnCn"], "license": "GPL-3.0-only"}, "lib/mangrove-core/lib/core/BitLib.sol": {"keccak256": "0x80f6885268986b9e976b424993aa875cf7aab8464403ed675a86ade9e9be5ee3", "urls": ["bzz-raw://5a31b4e1e0dc95de9a1dbb114c40c44814de5db3e2d857c93c2524a61454f6c8", "dweb:/ipfs/QmRkgE9ue5rGwE6XDnszF2e2meWqAC9nnKM97xKHjHphQr"], "license": "MIT"}, "lib/morpho-blue/src/libraries/MathLib.sol": {"keccak256": "0xa7354cbbcecef7bc0c94b61061c4e5da75515056b8e2db65e826b00d7369744a", "urls": ["bzz-raw://d7419c59bb906fcfa49320b68f265c3200090e5c30b194766256aee70b012e08", "dweb:/ipfs/Qmbo4uaW6XYnudya4bb6RU6riWXFk5M3CWJge5XzTTaEfd"], "license": "GPL-2.0-or-later"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da", "urls": ["bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41", "dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b", "urls": ["bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb", "dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2", "urls": ["bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5", "dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}}, "version": 1}, "id": 2}