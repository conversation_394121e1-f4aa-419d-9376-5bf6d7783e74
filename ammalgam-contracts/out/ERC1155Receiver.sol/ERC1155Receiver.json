{"abi": [{"type": "function", "name": "onERC1155BatchReceived", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "onERC1155Received", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}], "bytecode": {"object": "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", "sourceMap": "161:745:174:-:0;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "161:745:174:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;212:214;;;;;;:::i;:::-;;:::i;:::-;;;470:14:188;;463:22;445:41;;433:2;418:18;212:214:174;;;;;;;;657:247;;;;;;:::i;:::-;-1:-1:-1;;;657:247:174;;;;;;;;;;;-1:-1:-1;;;;;;3551:33:188;;;3533:52;;3521:2;3506:18;657:247:174;3389:202:188;432:219:174;;;;;;:::i;:::-;-1:-1:-1;;;432:219:174;;;;;;;;212:214;333:86;;-1:-1:-1;;;333:86:174;;4507:2:188;333:86:174;;;4489:21:188;4546:2;4526:18;;;4519:30;4585:34;4565:18;;;4558:62;4656:34;4636:18;;;4629:62;-1:-1:-1;;;4707:19:188;;;4700:43;317:4:174;;4760:19:188;;333:86:174;;;;;;;14:286:188;72:6;125:2;113:9;104:7;100:23;96:32;93:52;;;141:1;138;131:12;93:52;167:23;;-1:-1:-1;;;;;;219:32:188;;209:43;;199:71;;266:1;263;256:12;199:71;289:5;14:286;-1:-1:-1;;;14:286:188:o;497:173::-;565:20;;-1:-1:-1;;;;;614:31:188;;604:42;;594:70;;660:1;657;650:12;594:70;497:173;;;:::o;675:127::-;736:10;731:3;727:20;724:1;717:31;767:4;764:1;757:15;791:4;788:1;781:15;807:275;878:2;872:9;943:2;924:13;;-1:-1:-1;;920:27:188;908:40;;978:18;963:34;;999:22;;;960:62;957:88;;;1025:18;;:::i;:::-;1061:2;1054:22;807:275;;-1:-1:-1;807:275:188:o;1087:775::-;1141:5;1194:3;1187:4;1179:6;1175:17;1171:27;1161:55;;1212:1;1209;1202:12;1161:55;1252:6;1239:20;1282:18;1274:6;1271:30;1268:56;;;1304:18;;:::i;:::-;1350:6;1347:1;1343:14;1377:30;1401:4;1397:2;1393:13;1377:30;:::i;:::-;1443:19;;;1487:4;1519:15;;;1515:26;;;1478:14;;;;1553:15;;;1550:35;;;1581:1;1578;1571:12;1550:35;1617:4;1609:6;1605:17;1594:28;;1631:200;1647:6;1642:3;1639:15;1631:200;;;1739:17;;1769:18;;1816:4;1664:14;;;;1807;;;;1631:200;;;1849:7;1087:775;-1:-1:-1;;;;;;1087:775:188:o;1867:558::-;1909:5;1962:3;1955:4;1947:6;1943:17;1939:27;1929:55;;1980:1;1977;1970:12;1929:55;2020:6;2007:20;2050:18;2042:6;2039:30;2036:56;;;2072:18;;:::i;:::-;2116:59;2163:2;2140:17;;-1:-1:-1;;2136:31:188;2169:4;2132:42;2116:59;:::i;:::-;2200:6;2191:7;2184:23;2254:3;2247:4;2238:6;2230;2226:19;2222:30;2219:39;2216:59;;;2271:1;2268;2261:12;2216:59;2336:6;2329:4;2321:6;2317:17;2310:4;2301:7;2297:18;2284:59;2392:1;2363:20;;;2385:4;2359:31;2352:42;;;;2367:7;1867:558;-1:-1:-1;;;1867:558:188:o;2430:954::-;2584:6;2592;2600;2608;2616;2669:3;2657:9;2648:7;2644:23;2640:33;2637:53;;;2686:1;2683;2676:12;2637:53;2709:29;2728:9;2709:29;:::i;:::-;2699:39;;2757:38;2791:2;2780:9;2776:18;2757:38;:::i;:::-;2747:48;;2846:2;2835:9;2831:18;2818:32;2873:18;2865:6;2862:30;2859:50;;;2905:1;2902;2895:12;2859:50;2928:61;2981:7;2972:6;2961:9;2957:22;2928:61;:::i;:::-;2918:71;;;3042:2;3031:9;3027:18;3014:32;3071:18;3061:8;3058:32;3055:52;;;3103:1;3100;3093:12;3055:52;3126:63;3181:7;3170:8;3159:9;3155:24;3126:63;:::i;:::-;3116:73;;;3242:3;3231:9;3227:19;3214:33;3272:18;3262:8;3259:32;3256:52;;;3304:1;3301;3294:12;3256:52;3327:51;3370:7;3359:8;3348:9;3344:24;3327:51;:::i;:::-;3317:61;;;2430:954;;;;;;;;:::o;3596:704::-;3700:6;3708;3716;3724;3732;3785:3;3773:9;3764:7;3760:23;3756:33;3753:53;;;3802:1;3799;3792:12;3753:53;3825:29;3844:9;3825:29;:::i;:::-;3815:39;;3873:38;3907:2;3896:9;3892:18;3873:38;:::i;:::-;3863:48;-1:-1:-1;3980:2:188;3965:18;;3952:32;;-1:-1:-1;4081:2:188;4066:18;;4053:32;;-1:-1:-1;4162:3:188;4147:19;;4134:33;4190:18;4179:30;;4176:50;;;4222:1;4219;4212:12", "linkReferences": {}}, "methodIdentifiers": {"onERC1155BatchReceived(address,address,uint256[],uint256[],bytes)": "bc197c81", "onERC1155Received(address,address,uint256,uint256,bytes)": "f23a6e61", "supportsInterface(bytes4)": "01ffc9a7"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256[]\",\"name\":\"\",\"type\":\"uint256[]\"},{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"onERC1155BatchReceived\",\"outputs\":[{\"internalType\":\"bytes4\",\"name\":\"\",\"type\":\"bytes4\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"onERC1155Received\",\"outputs\":[{\"internalType\":\"bytes4\",\"name\":\"\",\"type\":\"bytes4\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"test/shared/ERC1155Receiver.sol\":\"ERC1155Receiver\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol\":{\"keccak256\":\"0xe057eca485a0d3c02177b3cd8f6b512091935510b6115c6f7e56af93b85fbaaf\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3cee6bdcf50f491a33022ad0944a2e8e3b78faa26d1a7ecbed1380c47aaa9a41\",\"dweb:/ipfs/QmZNQARvSK1FcR6rpG1sDPcSvM8befnTx9Be9JdXDpnKJ5\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"test/shared/ERC1155Receiver.sol\":{\"keccak256\":\"0x9f580ea3f22fab01cc35dd5867529c797cd9d1ab1d8f28a294fb7773e4b539b7\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://10a45efb9648349d3fad8dae55797cc3a9e24d186cfc7c3405ad0d1845f52ff6\",\"dweb:/ipfs/Qmbn74BHvwZRT5qsjAJNZj1zLHBvtfaQRtKHAYYxvvyB13\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256[]", "name": "", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "", "type": "uint256[]"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "onERC1155BatchReceived", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}]}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "onERC1155Received", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}]}, {"inputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"test/shared/ERC1155Receiver.sol": "ERC1155Receiver"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/token/ERC1155/IERC1155Receiver.sol": {"keccak256": "0xe057eca485a0d3c02177b3cd8f6b512091935510b6115c6f7e56af93b85fbaaf", "urls": ["bzz-raw://3cee6bdcf50f491a33022ad0944a2e8e3b78faa26d1a7ecbed1380c47aaa9a41", "dweb:/ipfs/QmZNQARvSK1FcR6rpG1sDPcSvM8befnTx9Be9JdXDpnKJ5"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "test/shared/ERC1155Receiver.sol": {"keccak256": "0x9f580ea3f22fab01cc35dd5867529c797cd9d1ab1d8f28a294fb7773e4b539b7", "urls": ["bzz-raw://10a45efb9648349d3fad8dae55797cc3a9e24d186cfc7c3405ad0d1845f52ff6", "dweb:/ipfs/Qmbn74BHvwZRT5qsjAJNZj1zLHBvtfaQRtKHAYYxvvyB13"], "license": "GPL-3.0-only"}}, "version": 1}, "id": 174}