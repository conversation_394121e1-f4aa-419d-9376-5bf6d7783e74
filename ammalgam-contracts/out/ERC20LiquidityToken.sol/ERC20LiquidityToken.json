{"abi": [{"type": "constructor", "inputs": [{"name": "config", "type": "tuple", "internalType": "struct ERC20BaseConfig", "components": [{"name": "pair", "type": "address", "internalType": "address"}, {"name": "pluginRegistry", "type": "address", "internalType": "address"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "symbol", "type": "string", "internalType": "string"}, {"name": "tokenType", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "nonpayable"}, {"type": "function", "name": "DOMAIN_SEPARATOR", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "HOOK_CALL_GAS_LIMIT", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "MAX_HOOKS_PER_ACCOUNT", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "addHook", "inputs": [{"name": "hook", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "allowance", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "approve", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "balanceOf", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "eip712Domain", "inputs": [], "outputs": [{"name": "fields", "type": "bytes1", "internalType": "bytes1"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "version", "type": "string", "internalType": "string"}, {"name": "chainId", "type": "uint256", "internalType": "uint256"}, {"name": "verifyingContract", "type": "address", "internalType": "address"}, {"name": "salt", "type": "bytes32", "internalType": "bytes32"}, {"name": "extensions", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "view"}, {"type": "function", "name": "hasH<PERSON>", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "hook", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "hookAt", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "index", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "hookBalanceOf", "inputs": [{"name": "hook", "type": "address", "internalType": "address"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "hooks", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "hooksCount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "name", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "nonces", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "ownerBurn", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "ownerMint", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "assets", "type": "uint256", "internalType": "uint256"}, {"name": "shares", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "ownerTransfer", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "pair", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract ITransferValidator"}], "stateMutability": "view"}, {"type": "function", "name": "permit", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}, {"name": "spender", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}, {"name": "deadline", "type": "uint256", "internalType": "uint256"}, {"name": "v", "type": "uint8", "internalType": "uint8"}, {"name": "r", "type": "bytes32", "internalType": "bytes32"}, {"name": "s", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeAllHooks", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeH<PERSON>", "inputs": [{"name": "hook", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "tokenType", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "totalSupply", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "transfer", "inputs": [{"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferFrom", "inputs": [{"name": "from", "type": "address", "internalType": "address"}, {"name": "to", "type": "address", "internalType": "address"}, {"name": "value", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "Approval", "inputs": [{"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "spender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Borrow", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "BorrowLiquidity", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Burn", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "EIP712DomainChanged", "inputs": [], "anonymous": false}, {"type": "event", "name": "HookAdded", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}, {"name": "hook", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "HookRemoved", "inputs": [{"name": "account", "type": "address", "indexed": false, "internalType": "address"}, {"name": "hook", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Mint", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "<PERSON>ay", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "onBehalfOf", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RepayLiquidity", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "onBehalfOf", "type": "address", "indexed": true, "internalType": "address"}, {"name": "assets", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "shares", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Transfer", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}, {"name": "value", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "ECDSAInvalidSignature", "inputs": []}, {"type": "error", "name": "ECDSAInvalidSignatureLength", "inputs": [{"name": "length", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ECDSAInvalidSignatureS", "inputs": [{"name": "s", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "ERC20InsufficientAllowance", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}, {"name": "allowance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InsufficientBalance", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "balance", "type": "uint256", "internalType": "uint256"}, {"name": "needed", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC20InvalidApprover", "inputs": [{"name": "approver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidReceiver", "inputs": [{"name": "receiver", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSender", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC20InvalidSpender", "inputs": [{"name": "spender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ERC2612ExpiredSignature", "inputs": [{"name": "deadline", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ERC2612InvalidSigner", "inputs": [{"name": "signer", "type": "address", "internalType": "address"}, {"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "HookAlreadyAdded", "inputs": []}, {"type": "error", "name": "HookNotFound", "inputs": []}, {"type": "error", "name": "HooksLimitReachedForAccount", "inputs": []}, {"type": "error", "name": "IndexOutOfBounds", "inputs": []}, {"type": "error", "name": "InvalidAccountNonce", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "currentNonce", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidHookAddress", "inputs": []}, {"type": "error", "name": "InvalidShortString", "inputs": []}, {"type": "error", "name": "InvalidTokenInHook", "inputs": []}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ReentrantCall", "inputs": []}, {"type": "error", "name": "StringTooLong", "inputs": [{"name": "str", "type": "string", "internalType": "string"}]}, {"type": "error", "name": "ZeroHooksLimit", "inputs": []}], "bytecode": {"object": "0x610200604052348015610010575f5ffd5b5060405161269938038061269983398101604081905261002f91610367565b80806040015180604051806040016040528060018152602001603160f81b815250835f0151600a6207a12086604001518760600151816003908161007391906104ae565b50600461008082826104ae565b505050815f036100a3576040516357d413bb60e11b815260040160405180910390fd5b60809190915260a05260016005556001600160a01b0381166100df57604051631e4fbdf760e01b81525f60048201526024015b60405180910390fd5b6100e8816101c7565b506100f4826008610218565b61016052610103816009610218565b61018052815160208084019190912061012052815190820120610140524660e0526101916101205161014051604080517f8b73c3c69bb8fe3d512ecc4cf759cc79239f7b179b0ffacaa9a75d522b39400f60208201529081019290925260608201524660808201523060a08201525f9060c00160405160208183030381529060405280519060200120905090565b60c052505030610100525080516001600160a01b039081166101a05260808201516101e052602090910151166101c052506105c0565b600780546001600160a01b038381166001600160a01b0319831681179093556040519116919082907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0905f90a35050565b5f6020835110156102335761022c8361024a565b9050610244565b8161023e84826104ae565b5060ff90505b92915050565b5f5f829050601f81511115610274578260405163305a27a960e01b81526004016100d69190610568565b805161027f8261059d565b179392505050565b634e487b7160e01b5f52604160045260245ffd5b60405160a081016001600160401b03811182821017156102bd576102bd610287565b60405290565b80516001600160a01b03811681146102d9575f5ffd5b919050565b5f82601f8301126102ed575f5ffd5b81516001600160401b0381111561030657610306610287565b604051601f8201601f19908116603f011681016001600160401b038111828210171561033457610334610287565b60405281815283820160200185101561034b575f5ffd5b8160208501602083015e5f918101602001919091529392505050565b5f60208284031215610377575f5ffd5b81516001600160401b0381111561038c575f5ffd5b820160a0818503121561039d575f5ffd5b6103a561029b565b6103ae826102c3565b81526103bc602083016102c3565b602082015260408201516001600160401b038111156103d9575f5ffd5b6103e5868285016102de565b60408301525060608201516001600160401b03811115610403575f5ffd5b61040f868285016102de565b60608301525060808201516080820152809250505092915050565b600181811c9082168061043e57607f821691505b60208210810361045c57634e487b7160e01b5f52602260045260245ffd5b50919050565b601f8211156104a957805f5260205f20601f840160051c810160208510156104875750805b601f840160051c820191505b818110156104a6575f8155600101610493565b50505b505050565b81516001600160401b038111156104c7576104c7610287565b6104db816104d5845461042a565b84610462565b6020601f82116001811461050d575f83156104f65750848201515b5f19600385901b1c1916600184901b1784556104a6565b5f84815260208120601f198516915b8281101561053c578785015182556020948501946001909201910161051c565b508482101561055957868401515f19600387901b60f8161c191681555b50505050600190811b01905550565b602081525f82518060208401528060208501604085015e5f604082850101526040601f19601f83011684010191505092915050565b8051602080830151919081101561045c575f1960209190910360031b1b16919050565b60805160a05160c05160e05161010051610120516101405161016051610180516101a0516101c0516101e05161203a61065f5f395f8181610288015261131501525f61075b01525f81816103df015261137e01525f61106001525f61103301525f610c9b01525f610c7301525f610bce01525f610bf801525f610c2201525f818161037101526114bc01525f81816102e30152610f66015261203a5ff3fe608060405234801561000f575f5ffd5b50600436106101db575f3560e01c8063715018a611610109578063a8aa1b311161009e578063d505accf1161006e578063d505accf14610447578063dd62ed3e1461045a578063e5adf3ab14610492578063f2fde38b146104a5575f5ffd5b8063a8aa1b31146103da578063a9059cbb14610401578063ac2c3e1d14610414578063c982681b14610427575f5ffd5b806384b0196e116100d957806384b0196e146103935780638da5cb5b146103ae57806395d89b41146103bf578063a1291f7f146103c7575f5ffd5b8063715018a61461033e5780637b4e04e8146103465780637ecebe001461035957806382b361691461036c575f5ffd5b8063313ce5671161017f578063********1161014f578063********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", "sourceMap": "501:1209:34:-:0;;;549:77;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;616:6;2019::31;:11;;;1616:4:71;3428:431:85;;;;;;;;;;;;;-1:-1:-1;;;3428:431:85;;;2048:6:31;:11;;;1956:2;1960:7;1909:6;:11;;;1922:6;:13;;;1656:5:67;1648;:13;;;;;;:::i;:::-;-1:-1:-1;1671:7:67;:17;1681:7;1671;:17;:::i;:::-;;1582:113;;1546:11:44;1561:1;1546:16;1542:45;;1571:16;;-1:-1:-1;;;1571:16:44;;;;;;;;;;;1542:45;1597:35;;;;;1642:39;;349:1:47;1691:6:44;717:27:47;-1:-1:-1;;;;;1273:26:53;;1269:95;;1322:31;;-1:-1:-1;;;1322:31:53;;1350:1;1322:31;;;5001:51:188;4974:18;;1322:31:53;;;;;;;;1269:95;1373:32;1392:12;1373:18;:32::i;:::-;-1:-1:-1;3501:45:85;:4;3532:13;3501:30;:45::i;:::-;3493:53;;3567:51;:7;3601:16;3567:33;:51::i;:::-;3556:62;;3642:22;;;;;;;;;;3628:36;;3691:25;;;;;;3674:42;;3744:13;3727:30;;3792:23;4326:11;;4339:14;;4304:80;;;2079:95;4304:80;;;5322:25:188;5363:18;;;5356:34;;;;5406:18;;;5399:34;4355:13:85;5449:18:188;;;5442:34;4378:4:85;5492:19:188;;;5485:61;4268:7:85;;5294:19:188;;4304:80:85;;;;;;;;;;;;4294:91;;;;;;4287:98;;4213:179;;3792:23;3767:48;;-1:-1:-1;;3847:4:85;3825:27;;-1:-1:-1;2101:11:31;;-1:-1:-1;;;;;2075:38:31;;::::4;;::::0;2135:16:::4;::::0;::::4;::::0;2123:28:::4;::::0;2194:21:::4;::::0;;::::4;::::0;2161:55:::4;;::::0;-1:-1:-1;501:1209:34;;2912:187:53;3004:6;;;-1:-1:-1;;;;;3020:17:53;;;-1:-1:-1;;;;;;3020:17:53;;;;;;;3052:40;;3004:6;;;3020:17;3004:6;;3052:40;;2985:16;;3052:40;2975:124;2912:187;:::o;2887:340:81:-;2983:11;3032:2;3016:5;3010:19;:24;3006:215;;;3057:20;3071:5;3057:13;:20::i;:::-;3050:27;;;;3006:215;3134:5;3108:46;3149:5;3134;3108:46;:::i;:::-;-1:-1:-1;1390:66:81;;-1:-1:-1;3006:215:81;2887:340;;;;:::o;1708:286::-;1773:11;1796:17;1822:3;1796:30;;1854:2;1840:4;:11;:16;1836:72;;;1893:3;1879:18;;-1:-1:-1;;;1879:18:81;;;;;;;;:::i;1836:72::-;1974:11;;1957:13;1974:4;1957:13;:::i;:::-;1949:36;;1708:286;-1:-1:-1;;;1708:286:81:o;14:127:188:-;75:10;70:3;66:20;63:1;56:31;106:4;103:1;96:15;130:4;127:1;120:15;146:248;213:2;207:9;255:4;243:17;;-1:-1:-1;;;;;275:34:188;;311:22;;;272:62;269:88;;;337:18;;:::i;:::-;373:2;366:22;146:248;:::o;399:177::-;478:13;;-1:-1:-1;;;;;520:31:188;;510:42;;500:70;;566:1;563;556:12;500:70;399:177;;;:::o;581:743::-;635:5;688:3;681:4;673:6;669:17;665:27;655:55;;706:1;703;696:12;655:55;733:13;;-1:-1:-1;;;;;758:30:188;;755:56;;;791:18;;:::i;:::-;860:2;854:9;952:2;914:17;;-1:-1:-1;;910:31:188;;;943:2;906:40;902:54;890:67;;-1:-1:-1;;;;;972:34:188;;1008:22;;;969:62;966:88;;;1034:18;;:::i;:::-;1070:2;1063:22;1094;;;1135:19;;;1156:4;1131:30;1128:39;-1:-1:-1;1125:59:188;;;1180:1;1177;1170:12;1125:59;1237:6;1230:4;1222:6;1218:17;1211:4;1203:6;1199:17;1193:51;1292:1;1264:19;;;1285:4;1260:30;1253:41;;;;1268:6;581:743;-1:-1:-1;;;581:743:188:o;1329:1012::-;1433:6;1486:2;1474:9;1465:7;1461:23;1457:32;1454:52;;;1502:1;1499;1492:12;1454:52;1529:16;;-1:-1:-1;;;;;1557:30:188;;1554:50;;;1600:1;1597;1590:12;1554:50;1623:22;;1679:4;1661:16;;;1657:27;1654:47;;;1697:1;1694;1687:12;1654:47;1723:17;;:::i;:::-;1763:33;1793:2;1763:33;:::i;:::-;1756:5;1749:48;1829:42;1867:2;1863;1859:11;1829:42;:::i;:::-;1824:2;1813:14;;1806:66;1911:2;1903:11;;1897:18;-1:-1:-1;;;;;1927:32:188;;1924:52;;;1972:1;1969;1962:12;1924:52;2008:56;2056:7;2045:8;2041:2;2037:17;2008:56;:::i;:::-;2003:2;1992:14;;1985:80;-1:-1:-1;2104:2:188;2096:11;;2090:18;-1:-1:-1;;;;;2120:32:188;;2117:52;;;2165:1;2162;2155:12;2117:52;2201:56;2249:7;2238:8;2234:2;2230:17;2201:56;:::i;:::-;2196:2;2189:5;2185:14;2178:80;;2305:3;2301:2;2297:12;2291:19;2285:3;2278:5;2274:15;2267:44;2330:5;2320:15;;;;1329:1012;;;;:::o;2346:380::-;2425:1;2421:12;;;;2468;;;2489:61;;2543:4;2535:6;2531:17;2521:27;;2489:61;2596:2;2588:6;2585:14;2565:18;2562:38;2559:161;;2642:10;2637:3;2633:20;2630:1;2623:31;2677:4;2674:1;2667:15;2705:4;2702:1;2695:15;2559:161;;2346:380;;;:::o;2857:518::-;2959:2;2954:3;2951:11;2948:421;;;2995:5;2992:1;2985:16;3039:4;3036:1;3026:18;3109:2;3097:10;3093:19;3090:1;3086:27;3080:4;3076:38;3145:4;3133:10;3130:20;3127:47;;;-1:-1:-1;3168:4:188;3127:47;3223:2;3218:3;3214:12;3211:1;3207:20;3201:4;3197:31;3187:41;;3278:81;3296:2;3289:5;3286:13;3278:81;;;3355:1;3341:16;;3322:1;3311:13;3278:81;;;3282:3;;2948:421;2857:518;;;:::o;3551:1299::-;3671:10;;-1:-1:-1;;;;;3693:30:188;;3690:56;;;3726:18;;:::i;:::-;3755:97;3845:6;3805:38;3837:4;3831:11;3805:38;:::i;:::-;3799:4;3755:97;:::i;:::-;3901:4;3932:2;3921:14;;3949:1;3944:649;;;;4637:1;4654:6;4651:89;;;-1:-1:-1;4706:19:188;;;4700:26;4651:89;-1:-1:-1;;3508:1:188;3504:11;;;3500:24;3496:29;3486:40;3532:1;3528:11;;;3483:57;4753:81;;3914:930;;3944:649;2804:1;2797:14;;;2841:4;2828:18;;-1:-1:-1;;3980:20:188;;;4098:222;4112:7;4109:1;4106:14;4098:222;;;4194:19;;;4188:26;4173:42;;4301:4;4286:20;;;;4254:1;4242:14;;;;4128:12;4098:222;;;4102:3;4348:6;4339:7;4336:19;4333:201;;;4409:19;;;4403:26;-1:-1:-1;;4492:1:188;4488:14;;;4504:3;4484:24;4480:37;4476:42;4461:58;4446:74;;4333:201;-1:-1:-1;;;;4580:1:188;4564:14;;;4560:22;4547:36;;-1:-1:-1;3551:1299:188:o;5557:418::-;5706:2;5695:9;5688:21;5669:4;5738:6;5732:13;5781:6;5776:2;5765:9;5761:18;5754:34;5840:6;5835:2;5827:6;5823:15;5818:2;5807:9;5803:18;5797:50;5896:1;5891:2;5882:6;5871:9;5867:22;5863:31;5856:42;5966:2;5959;5955:7;5950:2;5942:6;5938:15;5934:29;5923:9;5919:45;5915:54;5907:62;;;5557:418;;;;:::o;5980:297::-;6098:12;;6145:4;6134:16;;;6128:23;;6098:12;6163:16;;6160:111;;;-1:-1:-1;;6237:4:188;6233:17;;;;6230:1;6226:25;6222:38;6211:50;;5980:297;-1:-1:-1;5980:297:188:o;:::-;501:1209:34;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "0x608060405234801561000f575f5ffd5b50600436106101db575f3560e01c8063715018a611610109578063a8aa1b311161009e578063d505accf1161006e578063d505accf14610447578063dd62ed3e1461045a578063e5adf3ab14610492578063f2fde38b146104a5575f5ffd5b8063a8aa1b31146103da578063a9059cbb14610401578063ac2c3e1d14610414578063c982681b14610427575f5ffd5b806384b0196e116100d957806384b0196e146103935780638da5cb5b146103ae57806395d89b41146103bf578063a1291f7f146103c7575f5ffd5b8063715018a61461033e5780637b4e04e8146103465780637ecebe001461035957806382b361691461036c575f5ffd5b8063313ce5671161017f578063********1161014f578063********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", "sourceMap": "501:1209:34:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2896:234:44;;;;;;:::i;:::-;;:::i;:::-;;;689:25:188;;;677:2;662:18;2896:234:44;;;;;;;;1760:89:67;;;:::i;:::-;;;;;;;:::i;3902:186::-;;;;;;:::i;:::-;;:::i;:::-;;;1781:14:188;;1774:22;1756:41;;1744:2;1729:18;3902:186:67;1616:187:188;2803:97:67;2881:12;;2803:97;;4680:244;;;;;;:::i;:::-;;:::i;2163:134:44:-;;;;;;:::i;:::-;;:::i;:::-;;;-1:-1:-1;;;;;2485:32:188;;;2467:51;;2455:2;2440:18;2163:134:44;2321:203:188;1650:34:31;;;;;2848:128;;;2761:2:67;2671:36:188;;2659:2;2644:18;2848:128:31;2529:184:188;3516:85:44;;;:::i;:::-;;2614:112:71;;;:::i;692:194:34:-;;;;;;:::i;:::-;;:::i;948:46:44:-;;;;;952:283:34;;;;;;:::i;:::-;;:::i;1775:136:44:-;;;;;;:::i;:::-;;:::i;2670:172:31:-;;;;;;:::i;:::-;;:::i;2293:101:53:-;;;:::i;3746:176:31:-;;;;;;:::i;:::-;;:::i;2292:162::-;;;;;;:::i;:::-;;:::i;1046:44:44:-;;;;;5228:557:85;;;:::i;:::-;;;;;;;;;;;;;:::i;1638:85:53:-;1710:6;;-1:-1:-1;;;;;1710:6:53;1638:85;;1962:93:67;;;:::i;1565:143:34:-;;;;;;:::i;:::-;;:::i;1549:40:31:-;;;;;3244:178:67;;;;;;:::i;:::-;;:::i;1978:122:44:-;;;;;;:::i;:::-;;:::i;2359:129::-;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;1668:672:71:-;;;;;;:::i;:::-;;:::i;3455:140:67:-;;;;;;:::i;:::-;-1:-1:-1;;;;;3561:18:67;;;3535:7;3561:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;;;;3455:140;3350:95:44;;;;;;:::i;:::-;;:::i;2543:215:53:-;;;;;;:::i;:::-;;:::i;2896:234:44:-;3003:7;2974:6;2390:12:47;:4;1591:12;392:1;1591:24;;1511:111;2390:12;2386:59;;;2411:34;;-1:-1:-1;;;2411:34:47;;;;;;;;;;;2386:59;3026:22:44::1;3034:7;3043:4;3026:7;:22::i;:::-;3022:84;;;-1:-1:-1::0;;;;;3024:18:67;;2998:7;3024:18;;;;;;;;;;;3064:31:44::1;;;;3022:84;3122:1;3115:8;;2455:1:47;2896:234:44::0;;;;;:::o;1760:89:67:-;1805:13;1837:5;1830:12;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1760:89;:::o;3902:186::-;3975:4;735:10:77;4029:31:67;735:10:77;4045:7:67;4054:5;4029:8;:31::i;:::-;4077:4;4070:11;;;3902:186;;;;;:::o;4680:244::-;4767:4;735:10:77;4823:37:67;4839:4;735:10:77;4854:5:67;4823:15;:37::i;:::-;4870:26;4880:4;4886:2;4890:5;4870:9;:26::i;:::-;-1:-1:-1;4913:4:67;;4680:244;-1:-1:-1;;;;4680:244:67:o;2163:134:44:-;-1:-1:-1;;;;;2265:15:44;;2239:7;2265:15;;;:6;:15;;;;;:25;;2284:5;2265:18;:25::i;:::-;2258:32;2163:134;-1:-1:-1;;;2163:134:44:o;2953:16:31:-;2946:23;;2848:128;:::o;3516:85:44:-;3567:27;3583:10;3567:15;:27::i;:::-;3516:85::o;2614:112:71:-;2673:7;2699:20;:18;:20::i;692:194:34:-;1531:13:53;:11;:13::i;:::-;833:2:34::1;-1:-1:-1::0;;;;;820:32:34::1;825:6;-1:-1:-1::0;;;;;820:32:34::1;;837:6;845;820:32;;;;;;7507:25:188::0;;;7563:2;7548:18;;7541:34;7495:2;7480:18;;7333:248;820:32:34::1;;;;;;;;862:17;868:2;872:6;862:5;:17::i;:::-;692:194:::0;;;;:::o;952:283::-;1531:13:53;:11;:13::i;:::-;1093:2:34::1;-1:-1:-1::0;;;;;1080:32:34::1;1085:6;-1:-1:-1::0;;;;;1080:32:34::1;;1097:6;1105;1080:32;;;;;;7507:25:188::0;;;7563:2;7548:18;;7541:34;7495:2;7480:18;;7333:248;1080:32:34::1;;;;;;;;1122:25;1128:10;1140:6;1122:5;:25::i;1775:136:44:-:0;-1:-1:-1;;;;;1874:15:44;;1851:4;1874:15;;;:6;:15;;;;;:30;;1899:4;1874:24;:30::i;2670:172:31:-;2785:7;2811:24;2827:7;2811:15;:24::i;2293:101:53:-;1531:13;:11;:13::i;:::-;2357:30:::1;2384:1;2357:18;:30::i;3746:176:31:-:0;3821:36;;-1:-1:-1;;;3821:36:31;;-1:-1:-1;;;;;2485:32:188;;;3821:36:31;;;2467:51:188;3821:14:31;:30;;;;2440:18:188;;3821:36:31;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;3817:99;;;3873:32;3888:10;3900:4;3873:14;:32::i;:::-;3746:176;:::o;2292:162::-;2402:7;2428:19;2441:5;2428:12;:19::i;5228:557:85:-;5326:13;5353:18;5385:21;5420:15;5449:25;5488:12;5514:27;5617:13;:11;:13::i;:::-;5644:16;:14;:16::i;:::-;5752;;;5736:1;5752:16;;;;;;;;;-1:-1:-1;;;5566:212:85;;;-1:-1:-1;5566:212:85;;-1:-1:-1;5674:13:85;;-1:-1:-1;5709:4:85;;-1:-1:-1;5736:1:85;-1:-1:-1;5752:16:85;-1:-1:-1;5566:212:85;-1:-1:-1;5228:557:85:o;1962:93:67:-;2009:13;2041:7;2034:14;;;;;:::i;1565:143:34:-;1531:13:53;:11;:13::i;:::-;1674:27:34::1;1684:4;1690:2;1694:6;1674:9;:27::i;:::-;1565:143:::0;;;:::o;3244:178:67:-;3313:4;735:10:77;3367:27:67;735:10:77;3384:2:67;3388:5;3367:9;:27::i;1978:122:44:-;-1:-1:-1;;;;;2069:15:44;;2043:7;2069:15;;;:6;:15;;;;;:24;;:22;:24::i;2359:129::-;-1:-1:-1;;;;;2454:15:44;;;;;;:6;:15;;;;;2419:16;;2454:27;;:25;:27::i;1668:672:71:-;1889:8;1871:15;:26;1867:97;;;1920:33;;-1:-1:-1;;;1920:33:71;;;;;689:25:188;;;662:18;;1920:33:71;;;;;;;;1867:97;1974:18;1024:95;2033:5;2040:7;2049:5;2056:16;2066:5;-1:-1:-1;;;;;1121:14:78;819:7;1121:14;;;:7;:14;;;;;:16;;;;;;;;;759:395;2056:16:71;2005:78;;;;;;8287:25:188;;;;-1:-1:-1;;;;;8348:32:188;;;8328:18;;;8321:60;8417:32;;;;8397:18;;;8390:60;8466:18;;;8459:34;8509:19;;;8502:35;8553:19;;;8546:35;;;8259:19;;2005:78:71;;;;;;;;;;;;1995:89;;;;;;1974:110;;2095:12;2110:28;2127:10;2110:16;:28::i;:::-;2095:43;;2149:14;2166:28;2180:4;2186:1;2189;2192;2166:13;:28::i;:::-;2149:45;;2218:5;-1:-1:-1;;;;;2208:15:71;:6;-1:-1:-1;;;;;2208:15:71;;2204:88;;2246:35;;-1:-1:-1;;;2246:35:71;;-1:-1:-1;;;;;8784:32:188;;;2246:35:71;;;8766:51:188;8853:32;;8833:18;;;8826:60;8739:18;;2246:35:71;8592:300:188;2204:88:71;2302:31;2311:5;2318:7;2327:5;2302:8;:31::i;:::-;1857:483;;;1668:672;;;;;;;:::o;3350:95:44:-;3409:29;3421:10;3433:4;3409:11;:29::i;2543:215:53:-;1531:13;:11;:13::i;:::-;-1:-1:-1;;;;;2627:22:53;::::1;2623:91;;2672:31;::::0;-1:-1:-1;;;2672:31:53;;2700:1:::1;2672:31;::::0;::::1;2467:51:188::0;2440:18;;2672:31:53::1;2321:203:188::0;2623:91:53::1;2723:28;2742:8;2723:18;:28::i;8630:128:67:-:0;8714:37;8723:5;8730:7;8739:5;8746:4;8714:8;:37::i;10319:476::-;-1:-1:-1;;;;;3561:18:67;;;10418:24;3561:18;;;:11;:18;;;;;;;;:27;;;;;;;;;;-1:-1:-1;;10484:36:67;;10480:309;;;10559:5;10540:16;:24;10536:130;;;10591:60;;-1:-1:-1;;;10591:60:67;;-1:-1:-1;;;;;9117:32:188;;10591:60:67;;;9099:51:188;9166:18;;;9159:34;;;9209:18;;;9202:34;;;9072:18;;10591:60:67;8897:345:188;10536:130:67;10707:57;10716:5;10723:7;10751:5;10732:16;:24;10758:5;10707:8;:57::i;5297:300::-;-1:-1:-1;;;;;5380:18:67;;5376:86;;5421:30;;-1:-1:-1;;;5421:30:67;;5448:1;5421:30;;;2467:51:188;2440:18;;5421:30:67;2321:203:188;5376:86:67;-1:-1:-1;;;;;5475:16:67;;5471:86;;5514:32;;-1:-1:-1;;;5514:32:67;;5543:1;5514:32;;;2467:51:188;2440:18;;5514:32:67;2321:203:188;5471:86:67;5566:24;5574:4;5580:2;5584:5;5566:7;:24::i;1203:116:41:-;1269:7;1295:17;:1;1306:5;1295:10;:17::i;4532:499:44:-;-1:-1:-1;;;;;4630:15:44;;4601:26;4630:15;;;:6;:15;;;;;:23;;:21;:23::i;:::-;4601:52;;4663:15;4681:18;4691:7;4681:9;:18::i;:::-;4663:36;-1:-1:-1;4738:9:44;4733:282;4757:9;:16;4753:1;:20;4733:282;;;4798:12;4813:9;4823:1;4813:12;;;;;;;;:::i;:::-;;;;;;;4798:27;;4848:26;4860:7;4869:4;4848:26;;;;;;-1:-1:-1;;;;;8784:32:188;;;8766:51;;8853:32;;8848:2;8833:18;;8826:60;8754:2;8739:18;;8592:300;4848:26:44;;;;;;;;4896:11;;4892:109;;4931:51;4947:4;4953:7;4970:1;4974:7;4931:15;:51::i;:::-;-1:-1:-1;4775:3:44;;4733:282;;3945:262:85;3998:7;4029:4;-1:-1:-1;;;;;4038:11:85;4021:28;;:63;;;;;4070:14;4053:13;:31;4021:63;4017:184;;;-1:-1:-1;4107:22:85;;3945:262::o;4017:184::-;4167:23;4304:80;;;2079:95;4304:80;;;10301:25:188;4326:11:85;10342:18:188;;;10335:34;;;;4339:14:85;10385:18:188;;;10378:34;4355:13:85;10428:18:188;;;10421:34;4378:4:85;10471:19:188;;;10464:61;4268:7:85;;10273:19:188;;4304:80:85;;;;;;;;;;;;4294:91;;;;;;4287:98;;4213:179;;1796:162:53;1710:6;;-1:-1:-1;;;;;1710:6:53;735:10:77;1855:23:53;1851:101;;1901:40;;-1:-1:-1;;;1901:40:53;;735:10:77;1901:40:53;;;2467:51:188;2440:18;;1901:40:53;2321:203:188;7362:208:67;-1:-1:-1;;;;;7432:21:67;;7428:91;;7476:32;;-1:-1:-1;;;7476:32:67;;7505:1;7476:32;;;2467:51:188;2440:18;;7476:32:67;2321:203:188;7428:91:67;7528:35;7544:1;7548:7;7557:5;7528:7;:35::i;:::-;7362:208;;:::o;7888:206::-;-1:-1:-1;;;;;7958:21:67;;7954:89;;8002:30;;-1:-1:-1;;;8002:30:67;;8029:1;8002:30;;;2467:51:188;2440:18;;8002:30:67;2321:203:188;7954:89:67;8052:35;8060:7;8077:1;8081:5;8052:7;:35::i;1950:175:41:-;-1:-1:-1;;;;;2053:14:41;;2021:4;2053:14;;;-1:-1:-1;;;2053:8:41;;:14;;;;;;2084:10;;;;;:34;;;-1:-1:-1;;2098:5:41;:20;;2084:34;2077:41;1950:175;-1:-1:-1;;;;1950:175:41:o;2656:170:44:-;2769:7;2716:6;2390:12:47;:4;1591:12;392:1;1591:24;;1511:111;2390:12;2386:59;;;2411:34;;-1:-1:-1;;;2411:34:47;;;;;;;;;;;2386:59;-1:-1:-1;;;;;3024:18:67;;2998:7;3024:18;;;;;;;;;;;2788:31:44::1;;2455:1:47;2656:170:44::0;;;;:::o;2912:187:53:-;3004:6;;;-1:-1:-1;;;;;3020:17:53;;;-1:-1:-1;;;;;;3020:17:53;;;;;;;3052:40;;3004:6;;;3020:17;3004:6;;3052:40;;2985:16;;3052:40;2975:124;2912:187;:::o;3607:581:44:-;-1:-1:-1;;;;;3687:18:44;;3683:51;;3714:20;;-1:-1:-1;;;3714:20:44;;;;;;;;;;;3683:51;3791:4;-1:-1:-1;;;;;3748:49:44;3754:4;-1:-1:-1;;;;;3748:17:44;;:19;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;3748:49:44;;3744:82;;3806:20;;-1:-1:-1;;;3806:20:44;;;;;;;;;;;3744:82;-1:-1:-1;;;;;3841:15:44;;;;;;:6;:15;;;;;:25;;3861:4;3841:19;:25::i;:::-;3836:57;;3875:18;;-1:-1:-1;;;3875:18:44;;;;;;;;;;;3836:57;-1:-1:-1;;;;;3907:15:44;;;;;;:6;:15;;;;;3934:21;;3907:24;;:22;:24::i;:::-;:48;3903:90;;;3964:29;;-1:-1:-1;;;3964:29:44;;;;;;;;;;;3903:90;4009:24;;;-1:-1:-1;;;;;8784:32:188;;;8766:51;;8853:32;;8848:2;8833:18;;8826:60;4009:24:44;;8739:18:188;4009:24:44;;;;;;;4043:15;4061:18;4071:7;4061:9;:18::i;:::-;4043:36;-1:-1:-1;4093:11:44;;4089:93;;4120:51;4136:4;4150:1;4154:7;4163;4120:15;:51::i;2379:143:71:-;-1:-1:-1;;;;;624:14:78;;2470:7:71;624:14:78;;;:7;:14;;;;;;2496:19:71;538:107:78;6105:126:85;6151:13;6183:41;:5;6210:13;6183:26;:41::i;6557:135::-;6606:13;6638:47;:8;6668:16;6638:29;:47::i;821:104:41:-;1770:12:40;;876:7:41;;1331:3:40;1769:47;;;902:16:41;1685:138:40;3007:919;3062:23;3207:9;3201:16;-1:-1:-1;;;3265:14:40;3261:33;3245:14;3241:54;-1:-1:-1;;;;;3323:14:40;3319:34;3308:45;;3413:4;3407:11;3397:21;;3473:3;3470:1;3466:11;3460:4;3456:22;3448:6;3444:35;3438:4;3431:49;3508:3;3500:6;3493:19;3529:3;3526:384;;;3640:4;3632:6;3628:17;3674:3;3669;3662:16;3710:1;3695:201;3720:3;3717:1;3714:10;3695:201;;;3787:17;;;3781:24;-1:-1:-1;;;;;3777:44:40;3862:4;3858:12;;3849:22;;3842:36;3738:1;3731:9;3695:201;;;3699:14;;3526:384;;;3007:919;;;:::o;5017:176:85:-;5094:7;5120:66;5153:20;:18;:20::i;:::-;5175:10;4049:4:86;4043:11;-1:-1:-1;;;4067:23:86;;4119:4;4110:14;;4103:39;;;;4171:4;4162:14;;4155:34;4227:4;4212:20;;;3874:374;8225:260:84;8310:7;8330:17;8349:18;8369:16;8389:25;8400:4;8406:1;8409;8412;8389:10;:25::i;:::-;8329:85;;;;;;8424:28;8436:5;8443:8;8424:11;:28::i;:::-;-1:-1:-1;8469:9:84;;8225:260;-1:-1:-1;;;;;;8225:260:84:o;4194:332:44:-;-1:-1:-1;;;;;4278:15:44;;;;;;:6;:15;;;;;:28;;4301:4;4278:22;:28::i;:::-;4273:56;;4315:14;;-1:-1:-1;;;4315:14:44;;;;;;;;;;;4273:56;4345:26;;;-1:-1:-1;;;;;8784:32:188;;;8766:51;;8853:32;;8848:2;8833:18;;8826:60;4345:26:44;;8739:18:188;4345:26:44;;;;;;;4381:15;4399:18;4409:7;4399:9;:18::i;:::-;4381:36;-1:-1:-1;4431:11:44;;4427:93;;4458:51;4474:4;4480:7;4497:1;4501:7;4458:15;:51::i;9605:432:67:-;-1:-1:-1;;;;;9717:19:67;;9713:89;;9759:32;;-1:-1:-1;;;9759:32:67;;9788:1;9759:32;;;2467:51:188;2440:18;;9759:32:67;2321:203:188;9713:89:67;-1:-1:-1;;;;;9815:21:67;;9811:90;;9859:31;;-1:-1:-1;;;9859:31:67;;9887:1;9859:31;;;2467:51:188;2440:18;;9859:31:67;2321:203:188;9811:90:67;-1:-1:-1;;;;;9910:18:67;;;;;;;:11;:18;;;;;;;;:27;;;;;;;;;:35;;;9955:76;;;;10005:7;-1:-1:-1;;;;;9989:31:67;9998:5;-1:-1:-1;;;;;9989:31:67;;10014:5;9989:31;;;;689:25:188;;677:2;662:18;;543:177;9989:31:67;;;;;;;;9605:432;;;;:::o;2982:758:31:-;3096:31;3110:4;3116:2;3120:6;3096:13;:31::i;:::-;-1:-1:-1;;;;;3141:20:31;;;;;;:42;;-1:-1:-1;;;;;;3165:18:31;;;;3141:42;:80;;;;-1:-1:-1;3188:33:31;;;;3187:34;3141:80;3137:597;;;3294:16;3324:14;3352:13;443:1:19;3438:9:31;:28;3434:227;;;3497:4;3486:15;;3528:2;3519:11;;3434:227;;;-1:-1:-1;3580:2:31;;-1:-1:-1;3609:4:31;;-1:-1:-1;3642:4:31;3434:227;3674:49;;-1:-1:-1;;;3674:49:31;;-1:-1:-1;;;;;9870:32:188;;;3674:49:31;;;9852:51:188;9939:32;;;9919:18;;;9912:60;10015:14;;10008:22;9988:18;;;9981:50;3674:4:31;:21;;;;9825:18:188;;3674:49:31;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2122:202:40;1770:12;;2187:7;;2226:1;;1331:3;1769:47;;;2210:17;2206:48;;2236:18;;-1:-1:-1;;;2236:18:40;;;;;;;;;;;2206:48;-1:-1:-1;;;;;2287:4:40;2297:1;-1:-1:-1;;;2287:12:40;;;;;;:::i;:::-;;;:28;;2122:202;-1:-1:-1;;;2122:202:40:o;4144:370:41:-;4192:22;4234:13;:1;:11;:13::i;:::-;4271:12;;4226:21;;-1:-1:-1;4297:7:41;;4293:215;;-1:-1:-1;;;;;9056:32:40;;4382:9:41;4377:107;4401:3;4397:1;:7;4377:107;;;-1:-1:-1;;4433:1:41;-1:-1:-1;;;4433:8:41;:18;4442:5;4448:1;4442:8;;;;;;;;:::i;:::-;;;;;;;;;;;;-1:-1:-1;;;;;4433:18:41;;;;;;;;;;;-1:-1:-1;4433:18:41;:32;4406:3;;4377:107;;5335:785:44;5638:4;5632:11;-1:-1:-1;;;5656:21:44;;;5706:4;5697:14;;5690:28;;;5747:4;5738:14;;5731:26;;;5786:4;5777:14;;5770:30;;;5452:29;5510:19;;5829:5;5895:1;5892;5886:4;5881:3;5878:1;5872:4;5862:8;5857:40;5847:257;;5950:8;5945:2;5940;5931:7;5927:16;5923:25;5920:39;5917:173;;;6005:16;6002:1;5997:3;5982:40;6055:16;6050:3;6043:29;5917:173;5847:257;;5564:550;;5335:785;;;;:::o;3001:266:41:-;-1:-1:-1;;;;;3094:14:41;;3062:4;3094:14;;;-1:-1:-1;;;3094:8:41;;:14;;;;;;3122:10;;;;;:34;;;-1:-1:-1;;3136:5:41;:20;;3122:34;3118:77;;;3179:5;3172:12;;;;;3118:77;3221:18;:1;3234:4;3221:12;:18::i;:::-;-1:-1:-1;;;;;3204:14:41;;;;;;-1:-1:-1;;;3204:8:41;;:14;;;;;:35;-1:-1:-1;3256:4:41;;-1:-1:-1;3001:266:41;;;;:::o;3368:267:81:-;3462:13;1390:66;3491:46;;3487:142;;3560:15;3569:5;3560:8;:15::i;:::-;3553:22;;;;3487:142;3613:5;3606:12;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6541:1551:84;6667:17;;;7621:66;7608:79;;7604:164;;;-1:-1:-1;7719:1:84;;-1:-1:-1;7723:30:84;;-1:-1:-1;7755:1:84;7703:54;;7604:164;7879:24;;;7862:14;7879:24;;;;;;;;;10763:25:188;;;10836:4;10824:17;;10804:18;;;10797:45;;;;10858:18;;;10851:34;;;10901:18;;;10894:34;;;7879:24:84;;10735:19:188;;7879:24:84;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;7879:24:84;;-1:-1:-1;;7879:24:84;;;-1:-1:-1;;;;;;;7917:20:84;;7913:113;;-1:-1:-1;7969:1:84;;-1:-1:-1;7973:29:84;;-1:-1:-1;7969:1:84;;-1:-1:-1;7953:62:84;;7913:113;8044:6;-1:-1:-1;8052:20:84;;-1:-1:-1;8052:20:84;;-1:-1:-1;6541:1551:84;;;;;;;;;:::o;8618:532::-;8713:20;8704:5;:29;;;;;;;;:::i;:::-;;8700:444;;8618:532;;:::o;8700:444::-;8809:29;8800:5;:38;;;;;;;;:::i;:::-;;8796:348;;8861:23;;-1:-1:-1;;;8861:23:84;;;;;;;;;;;8796:348;8914:35;8905:5;:44;;;;;;;;:::i;:::-;;8901:243;;8972:46;;-1:-1:-1;;;8972:46:84;;;;;689:25:188;;;662:18;;8972:46:84;543:177:188;8901:243:84;9048:30;9039:5;:39;;;;;;;;:::i;:::-;;9035:109;;9101:32;;-1:-1:-1;;;9101:32:84;;;;;689:25:188;;;662:18;;9101:32:84;543:177:188;3518:482:41;-1:-1:-1;;;;;3614:14:41;;3582:4;3614:14;;;-1:-1:-1;;;3614:8:41;;:14;;;;;;;-1:-1:-1;;3638:28:41;;;3680:10;;;:34;;;-1:-1:-1;;3694:5:41;:20;3680:34;3676:77;;;3737:5;3730:12;;;;;3676:77;3763:16;3782;:1;:14;:16::i;:::-;3763:35;;3824:4;-1:-1:-1;;;;;3812:16:41;:8;-1:-1:-1;;;;;3812:16:41;;3808:165;;3872:32;:1;-1:-1:-1;;3884:9:41;;3895:8;3872:11;:32::i;:::-;-1:-1:-1;;;;;3922:18:41;;;;;;-1:-1:-1;;;3922:8:41;;:18;;;;;:26;;;-1:-1:-1;3989:4:41;;3518:482;-1:-1:-1;;;;3518:482:41:o;6126:1626:44:-;6207:6;2073:12:47;:4;:10;:12::i;:::-;6242:31:44::1;6256:4;6262:2;6266:6;6242:13;:31::i;:::-;6321:1;6312:6;:10;:24;;;;;6334:2;-1:-1:-1::0;;;;;6326:10:44::1;:4;-1:-1:-1::0;;;;;6326:10:44::1;;;6312:24;6308:1428;;;-1:-1:-1::0;;;;;6385:12:44;::::1;6356:26;6385:12:::0;;;:6:::1;:12;::::0;;;;:24:::1;::::0;:22:::1;:24::i;:::-;-1:-1:-1::0;;;;;6454:10:44;::::1;6427:24;6454:10:::0;;;:6:::1;:10;::::0;;;;6356:53;;-1:-1:-1;6427:24:44;6454:22:::1;::::0;:20:::1;:22::i;:::-;6520:16:::0;;6578:14;;6427:49;;-1:-1:-1;6520:16:44;6494:23:::1;6611:762;6635:15;6631:1;:19;6611:762;;;6679:12;6694:9;6704:1;6694:12;;;;;;;;:::i;:::-;;;;;;;6679:27;;6729:9;6769:1;6765:5;;6760:371;6776:13;6772:1;:17;6760:371;;;6834:7;6842:1;6834:10;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1::0;;;;;6826:18:44::1;:4;-1:-1:-1::0;;;;;6826:18:44::1;::::0;6822:287:::1;;6955:39;6971:4;6977;6983:2;6987:6;6955:15;:39::i;:::-;7045:1;7024:7;7032:1;7024:10;;;;;;;;:::i;:::-;;;;;;:23;-1:-1:-1::0;;;;;7024:23:44::1;;;-1:-1:-1::0;;;;;7024:23:44::1;;;::::0;::::1;7077:5;;6822:287;6791:3;;6760:371;;;7162:13;7157:1;:18:::0;7153:202:::1;;7285:47;7301:4;7307;7321:1;7325:6;7285:15;:47::i;:::-;-1:-1:-1::0;;6652:3:44::1;;6611:762;;;-1:-1:-1::0;7396:9:44::1;7391:331;7415:13;7411:1;:17;7391:331;;;7457:12;7472:7;7480:1;7472:10;;;;;;;;:::i;:::-;;;;;;;7457:25;;7524:1;-1:-1:-1::0;;;;;7508:18:44::1;:4;-1:-1:-1::0;;;;;7508:18:44::1;;7504:200;;7636:45;7652:4;7666:1;7670:2;7674:6;7636:15;:45::i;:::-;-1:-1:-1::0;7430:3:44::1;;7391:331;;;;6338:1398;;;;6308:1428;349:1:47::0;1238:27;;2106:11;1186:86;5389:631:40;5457:11;5590:9;5584:16;-1:-1:-1;;;5648:14:40;5644:33;5628:14;5624:54;5699:3;5720:1;5715:82;;;;-1:-1:-1;;;5858:14:40;5854:32;5843:9;5836:51;-1:-1:-1;;;5935:7:40;5932:26;5926:3;5915:9;5911:19;5904:55;5692:281;;5715:82;-1:-1:-1;;;5761:7:40;5758:24;5747:9;5740:43;5692:281;-1:-1:-1;6002:1:40;5993:11;;5389:631;-1:-1:-1;;;;5389:631:40:o;2078:378:81:-;2137:13;2162:11;2176:16;2187:4;2176:10;:16::i;:::-;2300:14;;;2311:2;2300:14;;;;;;;;;2162:30;;-1:-1:-1;2280:17:81;;2300:14;;;;;;;;;-1:-1:-1;;;2363:16:81;;;-1:-1:-1;2408:4:81;2399:14;;2392:28;;;;-1:-1:-1;2363:16:81;2078:378::o;6997:781:40:-;7225:16;;7049:11;;-1:-1:-1;;;7085:26:40;7265:54;7269:14;7265:54;;;;;7356:82;;;;7456:1;7451:129;;;;-1:-1:-1;;;;;7660:1:40;7655:3;7651:11;7640:9;7636:27;7630:34;7626:54;7619:61;;-1:-1:-1;;;7719:14:40;7715:32;7704:9;7697:51;7333:429;;7356:82;7391:3;7388:1;7381:14;7422:1;7419;7412:12;7451:129;-1:-1:-1;;;;;7534:32:40;;;-1:-1:-1;;;;;;;7483:34:40;;6997:781::o;8043:762::-;8278:16;;-1:-1:-1;;;8139:25:40;8318:54;8322:14;8318:54;;;;-1:-1:-1;;;;;8396:34:40;;8454:14;;;8444:101;;8498:3;8495:1;8488:14;8529:1;8526;8519:12;8444:101;8566:5;8584:95;;;;-1:-1:-1;;;8751:7:40;8748:26;8740:5;8729:9;8725:21;8718:57;8559:230;;8584:95;-1:-1:-1;8630:24:40;;;;8627:37;;;8609:56;;;-1:-1:-1;;;8043:762:40:o;917:145:47:-;974:12;;-1:-1:-1;;974:24:47;970:52;;1007:15;;-1:-1:-1;;;1007:15:47;;;;;;;;;;;970:52;392:1;1032:23;;917:145::o;5912:1107:67:-;-1:-1:-1;;;;;6001:18:67;;5997:540;;6153:5;6137:12;;:21;;;;;;;:::i;:::-;;;;-1:-1:-1;5997:540:67;;-1:-1:-1;5997:540:67;;-1:-1:-1;;;;;6211:15:67;;6189:19;6211:15;;;;;;;;;;;6244:19;;;6240:115;;;6290:50;;-1:-1:-1;;;6290:50:67;;-1:-1:-1;;;;;9117:32:188;;6290:50:67;;;9099:51:188;9166:18;;;9159:34;;;9209:18;;;9202:34;;;9072:18;;6290:50:67;8897:345:188;6240:115:67;-1:-1:-1;;;;;6475:15:67;;:9;:15;;;;;;;;;;6493:19;;;;6475:37;;5997:540;-1:-1:-1;;;;;6551:16:67;;6547:425;;6714:12;:21;;;;;;;6547:425;;;-1:-1:-1;;;;;6925:13:67;;:9;:13;;;;;;;;;;:22;;;;;;6547:425;7002:2;-1:-1:-1;;;;;6987:25:67;6996:4;-1:-1:-1;;;;;6987:25:67;;7006:5;6987:25;;;;689::188;;677:2;662:18;;543:177;6987:25:67;;;;;;;;5912:1107;;;:::o;2528:245:81:-;2589:7;2661:4;2625:40;;2688:2;2679:11;;2675:69;;;2713:20;;-1:-1:-1;;;2713:20:81;;;;;;;;;;;14:131:188;-1:-1:-1;;;;;89:31:188;;79:42;;69:70;;135:1;132;125:12;150:388;218:6;226;279:2;267:9;258:7;254:23;250:32;247:52;;;295:1;292;285:12;247:52;334:9;321:23;353:31;378:5;353:31;:::i;:::-;403:5;-1:-1:-1;460:2:188;445:18;;432:32;473:33;432:32;473:33;:::i;:::-;525:7;515:17;;;150:388;;;;;:::o;725:289::-;767:3;805:5;799:12;832:6;827:3;820:19;888:6;881:4;874:5;870:16;863:4;858:3;854:14;848:47;940:1;933:4;924:6;919:3;915:16;911:27;904:38;1003:4;996:2;992:7;987:2;979:6;975:15;971:29;966:3;962:39;958:50;951:57;;;725:289;;;;:::o;1019:220::-;1168:2;1157:9;1150:21;1131:4;1188:45;1229:2;1218:9;1214:18;1206:6;1188:45;:::i;1244:367::-;1312:6;1320;1373:2;1361:9;1352:7;1348:23;1344:32;1341:52;;;1389:1;1386;1379:12;1341:52;1428:9;1415:23;1447:31;1472:5;1447:31;:::i;:::-;1497:5;1575:2;1560:18;;;;1547:32;;-1:-1:-1;;;1244:367:188:o;1808:508::-;1885:6;1893;1901;1954:2;1942:9;1933:7;1929:23;1925:32;1922:52;;;1970:1;1967;1960:12;1922:52;2009:9;1996:23;2028:31;2053:5;2028:31;:::i;:::-;2078:5;-1:-1:-1;2135:2:188;2120:18;;2107:32;2148:33;2107:32;2148:33;:::i;:::-;1808:508;;2200:7;;-1:-1:-1;;;2280:2:188;2265:18;;;;2252:32;;1808:508::o;2900:629::-;2986:6;2994;3002;3010;3063:3;3051:9;3042:7;3038:23;3034:33;3031:53;;;3080:1;3077;3070:12;3031:53;3119:9;3106:23;3138:31;3163:5;3138:31;:::i;:::-;3188:5;-1:-1:-1;3245:2:188;3230:18;;3217:32;3258:33;3217:32;3258:33;:::i;:::-;2900:629;;3310:7;;-1:-1:-1;;;;3390:2:188;3375:18;;3362:32;;3493:2;3478:18;3465:32;;2900:629::o;3534:247::-;3593:6;3646:2;3634:9;3625:7;3621:23;3617:32;3614:52;;;3662:1;3659;3652:12;3614:52;3701:9;3688:23;3720:31;3745:5;3720:31;:::i;3786:1238::-;4192:3;4187;4183:13;4175:6;4171:26;4160:9;4153:45;4234:3;4229:2;4218:9;4214:18;4207:31;4134:4;4261:46;4302:3;4291:9;4287:19;4279:6;4261:46;:::i;:::-;4355:9;4347:6;4343:22;4338:2;4327:9;4323:18;4316:50;4389:33;4415:6;4407;4389:33;:::i;:::-;4453:2;4438:18;;4431:34;;;-1:-1:-1;;;;;4502:32:188;;4496:3;4481:19;;4474:61;4522:3;4551:19;;4544:35;;;4616:22;;;4610:3;4595:19;;4588:51;4688:13;;4710:22;;;4760:2;4786:15;;;;-1:-1:-1;4748:15:188;;;;-1:-1:-1;4829:169:188;4843:6;4840:1;4837:13;4829:169;;;4904:13;;4892:26;;4947:2;4973:15;;;;4938:12;;;;4865:1;4858:9;4829:169;;;-1:-1:-1;5015:3:188;;3786:1238;-1:-1:-1;;;;;;;;;;;3786:1238:188:o;5264:637::-;5454:2;5466:21;;;5536:13;;5439:18;;;5558:22;;;5406:4;;5637:15;;;5611:2;5596:18;;;5406:4;5680:195;5694:6;5691:1;5688:13;5680:195;;;5759:13;;-1:-1:-1;;;;;5755:39:188;5743:52;;5824:2;5850:15;;;;5815:12;;;;5791:1;5709:9;5680:195;;;-1:-1:-1;5892:3:188;;5264:637;-1:-1:-1;;;;;5264:637:188:o;5906:1037::-;6017:6;6025;6033;6041;6049;6057;6065;6118:3;6106:9;6097:7;6093:23;6089:33;6086:53;;;6135:1;6132;6125:12;6086:53;6174:9;6161:23;6193:31;6218:5;6193:31;:::i;:::-;6243:5;-1:-1:-1;6300:2:188;6285:18;;6272:32;6313:33;6272:32;6313:33;:::i;:::-;6365:7;-1:-1:-1;6445:2:188;6430:18;;6417:32;;-1:-1:-1;6548:2:188;6533:18;;6520:32;;-1:-1:-1;6630:3:188;6615:19;;6602:33;6679:4;6666:18;;6654:31;;6644:59;;6699:1;6696;6689:12;6644:59;5906:1037;;;;-1:-1:-1;5906:1037:188;;;;6722:7;6802:3;6787:19;;6774:33;;-1:-1:-1;6906:3:188;6891:19;;;6878:33;;5906:1037;-1:-1:-1;;5906:1037:188:o;6948:380::-;7027:1;7023:12;;;;7070;;;7091:61;;7145:4;7137:6;7133:17;7123:27;;7091:61;7198:2;7190:6;7187:14;7167:18;7164:38;7161:161;;7244:10;7239:3;7235:20;7232:1;7225:31;7279:4;7276:1;7269:15;7307:4;7304:1;7297:15;7586:277;7653:6;7706:2;7694:9;7685:7;7681:23;7677:32;7674:52;;;7722:1;7719;7712:12;7674:52;7754:9;7748:16;7807:5;7800:13;7793:21;7786:5;7783:32;7773:60;;7829:1;7826;7819:12;9247:127;9308:10;9303:3;9299:20;9296:1;9289:31;9339:4;9336:1;9329:15;9363:4;9360:1;9353:15;9379:272;9470:6;9523:2;9511:9;9502:7;9498:23;9494:32;9491:52;;;9539:1;9536;9529:12;9491:52;9571:9;9565:16;9590:31;9615:5;9590:31;:::i;10939:127::-;11000:10;10995:3;10991:20;10988:1;10981:31;11031:4;11028:1;11021:15;11055:4;11052:1;11045:15;11071:222;11136:9;;;11157:10;;;11154:133;;;11209:10;11204:3;11200:20;11197:1;11190:31;11244:4;11241:1;11234:15;11272:4;11269:1;11262:15", "linkReferences": {}, "immutableReferences": {"16149": [{"start": 991, "length": 32}, {"start": 4990, "length": 32}], "16152": [{"start": 1883, "length": 32}], "16154": [{"start": 648, "length": 32}, {"start": 4885, "length": 32}], "20383": [{"start": 739, "length": 32}, {"start": 3942, "length": 32}], "20386": [{"start": 881, "length": 32}, {"start": 5308, "length": 32}], "28107": [{"start": 3106, "length": 32}], "28109": [{"start": 3064, "length": 32}], "28111": [{"start": 3022, "length": 32}], "28113": [{"start": 3187, "length": 32}], "28115": [{"start": 3227, "length": 32}], "28118": [{"start": 4147, "length": 32}], "28121": [{"start": 4192, "length": 32}]}}, "methodIdentifiers": {"DOMAIN_SEPARATOR()": "3644e515", "HOOK_CALL_GAS_LIMIT()": "82b36169", "MAX_HOOKS_PER_ACCOUNT()": "********", "addHook(address)": "7b4e04e8", "allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "decimals()": "313ce567", "eip712Domain()": "84b0196e", "hasHook(address,address)": "6ea3d988", "hookAt(address,uint256)": "2d6516b6", "hookBalanceOf(address,address)": "00d137dd", "hooks(address)": "c982681b", "hooksCount(address)": "ac2c3e1d", "name()": "06fdde03", "nonces(address)": "7ecebe00", "owner()": "8da5cb5b", "ownerBurn(address,address,uint256,uint256)": "62a0d2cb", "ownerMint(address,address,uint256,uint256)": "4c412cd2", "ownerTransfer(address,address,uint256)": "a1291f7f", "pair()": "a8aa1b31", "permit(address,address,uint256,uint256,uint8,bytes32,bytes32)": "d505accf", "removeAllHooks()": "34aa9832", "removeHook(address)": "e5adf3ab", "renounceOwnership()": "715018a6", "symbol()": "95d89b41", "tokenType()": "30fa738c", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd", "transferOwnership(address)": "f2fde38b"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"pair\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"pluginRegistry\",\"type\":\"address\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"symbol\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"tokenType\",\"type\":\"uint256\"}],\"internalType\":\"struct ERC20BaseConfig\",\"name\":\"config\",\"type\":\"tuple\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"ECDSAInvalidSignature\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"length\",\"type\":\"uint256\"}],\"name\":\"ECDSAInvalidSignatureLength\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"ECDSAInvalidSignatureS\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"allowance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientAllowance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"needed\",\"type\":\"uint256\"}],\"name\":\"ERC20InsufficientBalance\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"approver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidApprover\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"}],\"name\":\"ERC20InvalidReceiver\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"ERC20InvalidSpender\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"}],\"name\":\"ERC2612ExpiredSignature\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"ERC2612InvalidSigner\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"HookAlreadyAdded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"HookNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"HooksLimitReachedForAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"IndexOutOfBounds\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"currentNonce\",\"type\":\"uint256\"}],\"name\":\"InvalidAccountNonce\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidHookAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidShortString\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidTokenInHook\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrantCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"str\",\"type\":\"string\"}],\"name\":\"StringTooLong\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroHooksLimit\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"Borrow\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"BorrowLiquidity\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"Burn\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"EIP712DomainChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"hook\",\"type\":\"address\"}],\"name\":\"HookAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"hook\",\"type\":\"address\"}],\"name\":\"HookRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"Mint\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"onBehalfOf\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"Repay\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"onBehalfOf\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"RepayLiquidity\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"DOMAIN_SEPARATOR\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"HOOK_CALL_GAS_LIMIT\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MAX_HOOKS_PER_ACCOUNT\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"hook\",\"type\":\"address\"}],\"name\":\"addHook\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"eip712Domain\",\"outputs\":[{\"internalType\":\"bytes1\",\"name\":\"fields\",\"type\":\"bytes1\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"version\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"chainId\",\"type\":\"uint256\"},{\"internalType\":\"address\",\"name\":\"verifyingContract\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"salt\",\"type\":\"bytes32\"},{\"internalType\":\"uint256[]\",\"name\":\"extensions\",\"type\":\"uint256[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"hook\",\"type\":\"address\"}],\"name\":\"hasHook\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"index\",\"type\":\"uint256\"}],\"name\":\"hookAt\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"hook\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hookBalanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hooks\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hooksCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"nonces\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"ownerBurn\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"assets\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"shares\",\"type\":\"uint256\"}],\"name\":\"ownerMint\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"ownerTransfer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pair\",\"outputs\":[{\"internalType\":\"contract ITransferValidator\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"deadline\",\"type\":\"uint256\"},{\"internalType\":\"uint8\",\"name\":\"v\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"r\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"permit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"removeAllHooks\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"hook\",\"type\":\"address\"}],\"name\":\"removeHook\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"tokenType\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"errors\":{\"ECDSAInvalidSignature()\":[{\"details\":\"The signature derives the `address(0)`.\"}],\"ECDSAInvalidSignatureLength(uint256)\":[{\"details\":\"The signature has an invalid length.\"}],\"ECDSAInvalidSignatureS(bytes32)\":[{\"details\":\"The signature has an S value that is in the upper half order.\"}],\"ERC20InsufficientAllowance(address,uint256,uint256)\":[{\"details\":\"Indicates a failure with the `spender`\\u2019s `allowance`. Used in transfers.\",\"params\":{\"allowance\":\"Amount of tokens a `spender` is allowed to operate with.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC20InsufficientBalance(address,uint256,uint256)\":[{\"details\":\"Indicates an error related to the current `balance` of a `sender`. Used in transfers.\",\"params\":{\"balance\":\"Current balance for the interacting account.\",\"needed\":\"Minimum amount required to perform a transfer.\",\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidApprover(address)\":[{\"details\":\"Indicates a failure with the `approver` of a token to be approved. Used in approvals.\",\"params\":{\"approver\":\"Address initiating an approval operation.\"}}],\"ERC20InvalidReceiver(address)\":[{\"details\":\"Indicates a failure with the token `receiver`. Used in transfers.\",\"params\":{\"receiver\":\"Address to which tokens are being transferred.\"}}],\"ERC20InvalidSender(address)\":[{\"details\":\"Indicates a failure with the token `sender`. Used in transfers.\",\"params\":{\"sender\":\"Address whose tokens are being transferred.\"}}],\"ERC20InvalidSpender(address)\":[{\"details\":\"Indicates a failure with the `spender` to be approved. Used in approvals.\",\"params\":{\"spender\":\"Address that may be allowed to operate on tokens without being their owner.\"}}],\"ERC2612ExpiredSignature(uint256)\":[{\"details\":\"Permit deadline has expired.\"}],\"ERC2612InvalidSigner(address,address)\":[{\"details\":\"Mismatched signature.\"}],\"IndexOutOfBounds()\":[{\"details\":\"Error thrown when attempting to access an index outside the bounds of the array.\"}],\"InvalidAccountNonce(address,uint256)\":[{\"details\":\"The nonce used for an `account` is not the expected current nonce.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"ReentrantCall()\":[{\"details\":\"Emit when reentrancy detected\"}]},\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Borrow(address,address,uint256,uint256)\":{\"details\":\"Emitted on a borrow of tokens\",\"params\":{\"assets\":\"The amount of the borrowed token assets\",\"sender\":\"The address initiating the borrowing action\",\"shares\":\"The amount of token shares being borrowed\",\"to\":\"The address receiving the borrowed tokens\"}},\"BorrowLiquidity(address,address,uint256,uint256)\":{\"details\":\"Emitted on a liquidity borrow\",\"params\":{\"assets\":\"The amount of the borrowed liquidity token\",\"sender\":\"The address initiating the borrowing action\",\"shares\":\"The amount of token shares being borrowed\",\"to\":\"Address where the borrowed liquidity is sent\"}},\"Burn(address,address,uint256,uint256)\":{\"details\":\"Emitted when tokens are burned\",\"params\":{\"assets\":\"The amount of token assets being burned\",\"sender\":\"Supplies Ammalgam Liquidity token into the pair contract and receives the minted assets in exchange\",\"shares\":\"The amount of token shares being burned\",\"to\":\"Address where burned tokens are sent\"}},\"EIP712DomainChanged()\":{\"details\":\"MAY be emitted to signal that the domain could have changed.\"},\"Mint(address,address,uint256,uint256)\":{\"details\":\"Emitted when tokens are minted\",\"params\":{\"assets\":\"The amount of token assets being minted\",\"shares\":\"The amount of token shares being minted\",\"to\":\"Address where minted tokens are sent\"}},\"Repay(address,address,uint256,uint256)\":{\"details\":\"Emitted on a repayment of tokens\",\"params\":{\"assets\":\"The address of the repaid token\",\"onBehalfOf\":\"The address of the account on whose behalf tokens are repaid\",\"sender\":\"The address initiating the repayment action\",\"shares\":\"The amount of tokens being repaid\"}},\"RepayLiquidity(address,address,uint256,uint256)\":{\"details\":\"Emitted on a liquidity repayment\",\"params\":{\"assets\":\"The amount of liquidity assets being repaid\",\"onBehalfOf\":\"Address for whom the repayment is made\",\"sender\":\"Supplies borrowed liquidity into the pair contract and the corresponding Ammalgam Debt tokens will be destroyed\",\"shares\":\"The amount of liquidity shares being repaid\"}},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"DOMAIN_SEPARATOR()\":{\"details\":\"Returns the domain separator used in the encoding of the signature for {permit}, as defined by {EIP712}.\"},\"allowance(address,address)\":{\"details\":\"Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"eip712Domain()\":{\"details\":\"returns the fields and values that describe the domain separator used by this contract for EIP-712 signature.\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"ownerBurn(address,address,uint256,uint256)\":{\"details\":\"override {ERC20Base-ownerBurn}.\"},\"ownerMint(address,address,uint256,uint256)\":{\"details\":\"override {ERC20Base-ownerMint}.\"},\"ownerTransfer(address,address,uint256)\":{\"details\":\"override {ERC20Base-ownerTransfer}.\",\"params\":{\"amount\":\"The amount of tokens to be transferred.\",\"from\":\"The account to deduct the tokens from.\",\"to\":\"The account to deliver the tokens to.\"}},\"permit(address,address,uint256,uint256,uint8,bytes32,bytes32)\":{\"details\":\"Sets `value` as the allowance of `spender` over ``owner``'s tokens, given ``owner``'s signed approval. IMPORTANT: The same issues {IERC20-approve} has related to transaction ordering also apply here. Emits an {Approval} event. Requirements: - `spender` cannot be the zero address. - `deadline` must be a timestamp in the future. - `v`, `r` and `s` must be a valid `secp256k1` signature from `owner` over the EIP712-formatted function arguments. - the signature must use ``owner``'s current nonce (see {nonces}). For more information on the signature format, see the https://eips.ethereum.org/EIPS/eip-2612#specification[relevant EIP section]. CAUTION: See Security Considerations above.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"totalSupply()\":{\"details\":\"Returns the value of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{\"addHook(address)\":{\"notice\":\"See {IERC20Hooks-addHook}.\"},\"hasHook(address,address)\":{\"notice\":\"See {IERC20Hooks-hasHook}.\"},\"hookAt(address,uint256)\":{\"notice\":\"See {IERC20Hooks-hookAt}.\"},\"hookBalanceOf(address,address)\":{\"notice\":\"See {IERC20Hooks-hookBalanceOf}.\"},\"hooks(address)\":{\"notice\":\"See {IERC20Hooks-hooks}.\"},\"hooksCount(address)\":{\"notice\":\"See {IERC20Hooks-hooksCount}.\"},\"ownerTransfer(address,address,uint256)\":{\"notice\":\"Transfers `amount` tokens from the `from` address to the `to` address.\"},\"removeAllHooks()\":{\"notice\":\"See {IERC20Hooks-removeAllHooks}.\"},\"removeHook(address)\":{\"notice\":\"See {IERC20Hooks-removeHook}.\"}},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/tokens/ERC20LiquidityToken.sol\":\"ERC20LiquidityToken\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":1inch/=lib/1inch/\",\":@1inch/=lib/1inch/\",\":@mangrovedao/mangrove-core/=lib/mangrove-core/\",\":@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/\",\":@mgv/lib/=lib/mangrove-core/lib/\",\":@mgv/script/=lib/mangrove-core/script/\",\":@mgv/src/=lib/mangrove-core/src/\",\":@mgv/test/=lib/mangrove-core/test/\",\":@morpho-org/morpho-blue/=lib/morpho-blue/\",\":@openzeppelin/=lib/openzeppelin-contracts/\",\":ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/\",\":core/=lib/mangrove-core/lib/core/\",\":ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/\",\":halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/\",\":mangrove-core/=lib/mangrove-core/\",\":morpho-blue/=lib/morpho-blue/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\",\":preprocessed/=lib/mangrove-core/lib/preprocessed/\"]},\"sources\":{\"contracts/interfaces/IAmmalgamPair.sol\":{\"keccak256\":\"0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795\",\"dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw\"]},\"contracts/interfaces/callbacks/ITransferValidator.sol\":{\"keccak256\":\"0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4\",\"dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8\"]},\"contracts/interfaces/tokens/IAmmalgamERC20.sol\":{\"keccak256\":\"0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e\",\"dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK\"]},\"contracts/interfaces/tokens/IPluginRegistry.sol\":{\"keccak256\":\"0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d\",\"dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X\"]},\"contracts/interfaces/tokens/ITokenController.sol\":{\"keccak256\":\"0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570\",\"dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg\"]},\"contracts/libraries/constants.sol\":{\"keccak256\":\"0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4\",\"license\":\"GPL-3.0-only\",\"urls\":[\"bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362\",\"dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat\"]},\"contracts/tokens/ERC20Base.sol\":{\"keccak256\":\"0xdd3db9eaa855b6ff747ffaa0e74dd2a64dd5b0d704356acb75c2690c3fc6bc2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8f9e05ae3078bc3a29ef43861b006fb290606c1c9b2676baaed3ab98ecdb2d59\",\"dweb:/ipfs/QmZrvgxYtUD6jQVBvM9rT7jj5Vzb5csiThGj3ZwHSPryAL\"]},\"contracts/tokens/ERC20LiquidityToken.sol\":{\"keccak256\":\"0x2bb2429e551c031034c747749373d2e4c451580e9b203b689d6eaf03ad896358\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9ad5902756073578beee9068b74bd921e59a36b803cf34ef01570c670363689e\",\"dweb:/ipfs/QmTkT5K2XcB3ZbPDqd4ZAfnZMp2reCzu3Pv7JpRqhAtZHP\"]},\"lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol\":{\"keccak256\":\"0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22\",\"dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9\"]},\"lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol\":{\"keccak256\":\"0xbb8e2a541ac268f00f382c9fba9403b3ec5b58a48dc7236920d7c87817f93318\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://614b60ff60f75c46aa41d00ec5dd78136d42e0d6035aa2331d16f26d2f5f5368\",\"dweb:/ipfs/QmUJWqcx2heKcmBhxpHfAnpKrwnevqtAwaQKT7Bmpke5NB\"]},\"lib/1inch/token-plugins/contracts/ERC20Hooks.sol\":{\"keccak256\":\"0xd2657f278b2ed4667663344aa06c02b52b15862c69c215570de329aa1a4683a2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4ffb90146369b7fa890489a0d3ecab5aa81c9f47b3ec7a1776fd8bc14eaa33d5\",\"dweb:/ipfs/QmWo24VHySo9jQBeXfE87Z3Hh576cNKcwccLrBAMsfax1c\"]},\"lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol\":{\"keccak256\":\"0x2fb4fcbf91a7edf36e7ada3f578a8de1aee7ebdd12460648d3e09d4448351875\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d09a05543afcad3c0c2d2b9013647408cc44e1b17f362665ee20f9abed5964e8\",\"dweb:/ipfs/QmX3MMGsVJSfsrVoYY2jWK2BXd3i6scBhQQxytk461mizh\"]},\"lib/1inch/token-plugins/contracts/interfaces/IHook.sol\":{\"keccak256\":\"0x474893cc48ee17530ad0e978ecbccaa726943615a5736260187a37b8e133ee80\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://511ed52ec427cced459878f28453790817d4cbacc8601d77eb8dfa28e2e0b30d\",\"dweb:/ipfs/QmXvbB1bAZZanGepiUxWh3zZQUaHQZeYpR1UdaW4w5yKVS\"]},\"lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol\":{\"keccak256\":\"0xa88ccab1ee6b34a9007986ca15ea5fd5580864029130eb38333183af1bc4f66c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b5ad18c862e59b0f24edc900519a55c7aee5537a42d039d6ea5e34df40267bb0\",\"dweb:/ipfs/QmVMRSjUEPxCno1sFdhWvpRqAqoqns8zwVyd7yCHYC6P8Z\"]},\"lib/openzeppelin-contracts/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol\":{\"keccak256\":\"0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41\",\"dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol\":{\"keccak256\":\"0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb\",\"dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol\":{\"keccak256\":\"0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be\",\"dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol\":{\"keccak256\":\"0x932fe72aeda629f70ee2ca902cfd8cce9faa0a13b39222c240979f42984c4541\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://4e9e0a320185800eb9a6d57ef0e4ccf3bb32a6b5dd44882f7552617c30d4a05e\",\"dweb:/ipfs/QmYJVpT7DDPWx3DWro8vtM6Gqre2AyufsyCYoHm9cfQ1vr\"]},\"lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol\":{\"keccak256\":\"0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215\",\"dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT\"]},\"lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol\":{\"keccak256\":\"0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318\",\"dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol\":{\"keccak256\":\"0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79\",\"dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662\",\"dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol\":{\"keccak256\":\"0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834\",\"dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol\":{\"keccak256\":\"0x0d99c706d010fa15de36e7e7b7a03dd0fdc9bcec52f9f812ef80ec7f3fc6fa63\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://bee73c81a2964e8da0537de14082e60d64cd7b1cd9162adc04b58317e334c896\",\"dweb:/ipfs/QmbQ75T9PEJuiLk1kypX68rEBFtTaEzPWsy8Dv99buqVPH\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"keccak256\":\"0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912\",\"dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol\":{\"keccak256\":\"0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0\",\"dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK\"]},\"lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol\":{\"keccak256\":\"0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5\",\"dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/Nonces.sol\":{\"keccak256\":\"0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e\",\"dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ\"]},\"lib/openzeppelin-contracts/contracts/utils/Panic.sol\":{\"keccak256\":\"0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a\",\"dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG\"]},\"lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol\":{\"keccak256\":\"0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35\",\"dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy\"]},\"lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol\":{\"keccak256\":\"0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b\",\"dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM\"]},\"lib/openzeppelin-contracts/contracts/utils/Strings.sol\":{\"keccak256\":\"0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211\",\"dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol\":{\"keccak256\":\"0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4\",\"dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol\":{\"keccak256\":\"0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f\",\"dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun\"]},\"lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol\":{\"keccak256\":\"0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59\",\"dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5\",\"dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf\"]},\"lib/openzeppelin-contracts/contracts/utils/math/Math.sol\":{\"keccak256\":\"0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3\",\"dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol\":{\"keccak256\":\"0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8\",\"dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy\"]},\"lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol\":{\"keccak256\":\"0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03\",\"dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.28+commit.7893614a"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "struct ERC20BaseConfig", "name": "config", "type": "tuple", "components": [{"internalType": "address", "name": "pair", "type": "address"}, {"internalType": "address", "name": "pluginRegistry", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint256", "name": "tokenType", "type": "uint256"}]}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "ECDSAInvalidSignature"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "type": "error", "name": "ECDSAInvalidSignatureLength"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "type": "error", "name": "ECDSAInvalidSignatureS"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientAllowance"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "type": "error", "name": "ERC20InsufficientBalance"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "type": "error", "name": "ERC20InvalidApprover"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "type": "error", "name": "ERC20InvalidReceiver"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "type": "error", "name": "ERC20InvalidSender"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "type": "error", "name": "ERC20InvalidSpender"}, {"inputs": [{"internalType": "uint256", "name": "deadline", "type": "uint256"}], "type": "error", "name": "ERC2612ExpiredSignature"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "ERC2612InvalidSigner"}, {"inputs": [], "type": "error", "name": "HookAlreadyAdded"}, {"inputs": [], "type": "error", "name": "HookNotFound"}, {"inputs": [], "type": "error", "name": "HooksLimitReachedForAccount"}, {"inputs": [], "type": "error", "name": "IndexOutOfBounds"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "currentNonce", "type": "uint256"}], "type": "error", "name": "InvalidAccountNonce"}, {"inputs": [], "type": "error", "name": "InvalidHookAddress"}, {"inputs": [], "type": "error", "name": "InvalidShortString"}, {"inputs": [], "type": "error", "name": "InvalidTokenInHook"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "type": "error", "name": "OwnableInvalidOwner"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "type": "error", "name": "OwnableUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "ReentrantCall"}, {"inputs": [{"internalType": "string", "name": "str", "type": "string"}], "type": "error", "name": "StringTooLong"}, {"inputs": [], "type": "error", "name": "ZeroHooksLimit"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "address", "name": "spender", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Approval", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "Borrow", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "BorrowLiquidity", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "Burn", "anonymous": false}, {"inputs": [], "type": "event", "name": "EIP712DomainChanged", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": false}, {"internalType": "address", "name": "hook", "type": "address", "indexed": false}], "type": "event", "name": "HookAdded", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "account", "type": "address", "indexed": false}, {"internalType": "address", "name": "hook", "type": "address", "indexed": false}], "type": "event", "name": "HookRemoved", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "Mint", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "previousOwner", "type": "address", "indexed": true}, {"internalType": "address", "name": "new<PERSON>wner", "type": "address", "indexed": true}], "type": "event", "name": "OwnershipTransferred", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "onBehalfOf", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "<PERSON>ay", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address", "indexed": true}, {"internalType": "address", "name": "onBehalfOf", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "assets", "type": "uint256", "indexed": false}, {"internalType": "uint256", "name": "shares", "type": "uint256", "indexed": false}], "type": "event", "name": "RepayLiquidity", "anonymous": false}, {"inputs": [{"internalType": "address", "name": "from", "type": "address", "indexed": true}, {"internalType": "address", "name": "to", "type": "address", "indexed": true}, {"internalType": "uint256", "name": "value", "type": "uint256", "indexed": false}], "type": "event", "name": "Transfer", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "HOOK_CALL_GAS_LIMIT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MAX_HOOKS_PER_ACCOUNT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "hook", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "addHook"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "stateMutability": "view", "type": "function", "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "eip712Domain", "outputs": [{"internalType": "bytes1", "name": "fields", "type": "bytes1"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "version", "type": "string"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "address", "name": "verifyingContract", "type": "address"}, {"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "uint256[]", "name": "extensions", "type": "uint256[]"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "hook", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hasH<PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "hookAt", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "hook", "type": "address"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hookBalanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hooks", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}]}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hooksCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function", "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "ownerBurn"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "assets", "type": "uint256"}, {"internalType": "uint256", "name": "shares", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "ownerMint"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "ownerTransfer"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "pair", "outputs": [{"internalType": "contract ITransferValidator", "name": "", "type": "address"}]}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function", "name": "permit"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "removeAllHooks"}, {"inputs": [{"internalType": "address", "name": "hook", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "removeH<PERSON>"}, {"inputs": [], "stateMutability": "nonpayable", "type": "function", "name": "renounceOwnership"}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "tokenType", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function", "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "transferOwnership"}], "devdoc": {"kind": "dev", "methods": {"DOMAIN_SEPARATOR()": {"details": "Returns the domain separator used in the encoding of the signature for {permit}, as defined by {EIP712}."}, "allowance(address,address)": {"details": "Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called."}, "approve(address,uint256)": {"details": "See {IERC20-approve}. NOTE: If `value` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address."}, "eip712Domain()": {"details": "returns the fields and values that describe the domain separator used by this contract for EIP-712 signature."}, "name()": {"details": "Returns the name of the token."}, "owner()": {"details": "Returns the address of the current owner."}, "ownerBurn(address,address,uint256,uint256)": {"details": "override {ERC20Base-ownerBurn}."}, "ownerMint(address,address,uint256,uint256)": {"details": "override {ERC20Base-ownerMint}."}, "ownerTransfer(address,address,uint256)": {"details": "override {ERC20Base-ownerTransfer}.", "params": {"amount": "The amount of tokens to be transferred.", "from": "The account to deduct the tokens from.", "to": "The account to deliver the tokens to."}}, "permit(address,address,uint256,uint256,uint8,bytes32,bytes32)": {"details": "Sets `value` as the allowance of `spender` over ``owner``'s tokens, given ``owner``'s signed approval. IMPORTANT: The same issues {IERC20-approve} has related to transaction ordering also apply here. Emits an {Approval} event. Requirements: - `spender` cannot be the zero address. - `deadline` must be a timestamp in the future. - `v`, `r` and `s` must be a valid `secp256k1` signature from `owner` over the EIP712-formatted function arguments. - the signature must use ``owner``'s current nonce (see {nonces}). For more information on the signature format, see the https://eips.ethereum.org/EIPS/eip-2612#specification[relevant EIP section]. CAUTION: See Security Considerations above."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "symbol()": {"details": "Returns the symbol of the token, usually a shorter version of the name."}, "totalSupply()": {"details": "Returns the value of tokens in existence."}, "transfer(address,uint256)": {"details": "See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `value`."}, "transferFrom(address,address,uint256)": {"details": "See {IERC20-transferFrom}. Skips emitting an {Approval} event indicating an allowance update. This is not required by the ERC. See {xref-ERC20-_approve-address-address-uint256-bool-}[_approve]. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `value`. - the caller must have allowance for ``from``'s tokens of at least `value`."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {"addHook(address)": {"notice": "See {IERC20Hooks-addHook}."}, "hasHook(address,address)": {"notice": "See {IERC20Hooks-hasHook}."}, "hookAt(address,uint256)": {"notice": "See {IERC20Hooks-hookAt}."}, "hookBalanceOf(address,address)": {"notice": "See {IERC20Hooks-hookBalanceOf}."}, "hooks(address)": {"notice": "See {IERC20Hooks-hooks}."}, "hooksCount(address)": {"notice": "See {IERC20Hooks-hooksCount}."}, "ownerTransfer(address,address,uint256)": {"notice": "Transfers `amount` tokens from the `from` address to the `to` address."}, "removeAllHooks()": {"notice": "See {IERC20Hooks-removeAllHooks}."}, "removeHook(address)": {"notice": "See {IERC20Hooks-removeHook}."}}, "version": 1}}, "settings": {"remappings": ["1inch/=lib/1inch/", "@1inch/=lib/1inch/", "@mangrovedao/mangrove-core/=lib/mangrove-core/", "@mgv/forge-std/=lib/mangrove-core/lib/forge-std/src/", "@mgv/lib/=lib/mangrove-core/lib/", "@mgv/script/=lib/mangrove-core/script/", "@mgv/src/=lib/mangrove-core/src/", "@mgv/test/=lib/mangrove-core/test/", "@morpho-org/morpho-blue/=lib/morpho-blue/", "@openzeppelin/=lib/openzeppelin-contracts/", "ExcessivelySafeCall/=lib/ExcessivelySafeCall/src/", "core/=lib/mangrove-core/lib/core/", "ds-test/=lib/ExcessivelySafeCall/lib/ds-test/src/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/openzeppelin-contracts/lib/forge-std/src/", "halmos-cheatcodes/=lib/morpho-blue/lib/halmos-cheatcodes/src/", "mangrove-core/=lib/mangrove-core/", "morpho-blue/=lib/morpho-blue/", "openzeppelin-contracts/=lib/openzeppelin-contracts/", "preprocessed/=lib/mangrove-core/lib/preprocessed/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"contracts/tokens/ERC20LiquidityToken.sol": "ERC20LiquidityToken"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"contracts/interfaces/IAmmalgamPair.sol": {"keccak256": "0xa17e45b2348d8920d9970c5d50b300fc0a1e8d03350cdd0d1a624494baa70337", "urls": ["bzz-raw://8d252e89e5d49d1c15a0c0c0a495a325b9f8d608714b29279a7bacb1e4bf8795", "dweb:/ipfs/QmRkZ7a8JJQYEw6HQMJjjkuAK8b5Th1X1ET6BG1R8mx4qw"], "license": "GPL-3.0-only"}, "contracts/interfaces/callbacks/ITransferValidator.sol": {"keccak256": "0x6d9028fc4ad1914e6b2091e6ba46a9f836f9e67ea435c4a8fef41363f2ceaf56", "urls": ["bzz-raw://7ecaade4884d460168f6978edf35706f7b9e363de6002942b1d171a338dca6a4", "dweb:/ipfs/QmS5wgfDt5Pn68rpCytpzhiy57LcmivVFQ5XLGXUUP5Tt8"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IAmmalgamERC20.sol": {"keccak256": "0x44a376269170b4270ec221ce3cb31a609b394e216cc4d2e27b818361b4369829", "urls": ["bzz-raw://c48bc7586631f27ede73d3d0b4c1d7a29b1653e6c501c8b7fc9877c125f8f57e", "dweb:/ipfs/QmTSLtqnsxr7h7ct524rqYssHUo4qursmCZ7g5q3J1qQPK"], "license": "GPL-3.0-only"}, "contracts/interfaces/tokens/IPluginRegistry.sol": {"keccak256": "0x9a677620d88ac7dc42afb21d82a7b7a89bd934c1cada5450cf2b6200bf374ccf", "urls": ["bzz-raw://304091e5d54e9ad7db24ba1022e84e39dd9305d9cc72fd87423c71b40de4ab3d", "dweb:/ipfs/QmZEF5MfmUcxzLF9fcGCLvGMTTLLhcWdCMCDK2WyXj6s7X"], "license": "MIT"}, "contracts/interfaces/tokens/ITokenController.sol": {"keccak256": "0x7778001aaf582fe10005240eb6023b2b6cee3f100b6c2222bf6b9ade93732624", "urls": ["bzz-raw://91e5c4519207d6a450be1e0a8649157e86d20f8ef6a91ff6512a31cf5561a570", "dweb:/ipfs/QmUqZLW27JJZHFPf2fgLDYSWWj5gM158DdaxTTmDVukRAg"], "license": "GPL-3.0-only"}, "contracts/libraries/constants.sol": {"keccak256": "0x0dfb294985a8f48287ff13e8476718ddb5334b1d8bf6bfa59a5db1dbcf6ca7c4", "urls": ["bzz-raw://4bedcfdb2850cfb22b5daa768ab8125b4ccab97c90068d1d0ad4495bf942b362", "dweb:/ipfs/Qmf9p88yQN2JYRBR5D7q9BLmwhDJWpFk47ZuayrKqCyHat"], "license": "GPL-3.0-only"}, "contracts/tokens/ERC20Base.sol": {"keccak256": "0xdd3db9eaa855b6ff747ffaa0e74dd2a64dd5b0d704356acb75c2690c3fc6bc2b", "urls": ["bzz-raw://8f9e05ae3078bc3a29ef43861b006fb290606c1c9b2676baaed3ab98ecdb2d59", "dweb:/ipfs/QmZrvgxYtUD6jQVBvM9rT7jj5Vzb5csiThGj3ZwHSPryAL"], "license": "MIT"}, "contracts/tokens/ERC20LiquidityToken.sol": {"keccak256": "0x2bb2429e551c031034c747749373d2e4c451580e9b203b689d6eaf03ad896358", "urls": ["bzz-raw://9ad5902756073578beee9068b74bd921e59a36b803cf34ef01570c670363689e", "dweb:/ipfs/QmTkT5K2XcB3ZbPDqd4ZAfnZMp2reCzu3Pv7JpRqhAtZHP"], "license": "MIT"}, "lib/1inch/solidity-utils/contracts/libraries/AddressArray.sol": {"keccak256": "0x7895eaf7b55d9612b22ec586970488de51436c021b9f9414b3c27c3583c8856e", "urls": ["bzz-raw://e43897dfeff84e2983b017ab100d74061a6b9bed4618ec37a7cbb68bf064ac22", "dweb:/ipfs/Qmejoj4CuNr1giwGBDG7SgRtfYmi64fgy2DsGP2AAW9gH9"], "license": "MIT"}, "lib/1inch/solidity-utils/contracts/libraries/AddressSet.sol": {"keccak256": "0xbb8e2a541ac268f00f382c9fba9403b3ec5b58a48dc7236920d7c87817f93318", "urls": ["bzz-raw://614b60ff60f75c46aa41d00ec5dd78136d42e0d6035aa2331d16f26d2f5f5368", "dweb:/ipfs/QmUJWqcx2heKcmBhxpHfAnpKrwnevqtAwaQKT7Bmpke5NB"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/ERC20Hooks.sol": {"keccak256": "0xd2657f278b2ed4667663344aa06c02b52b15862c69c215570de329aa1a4683a2", "urls": ["bzz-raw://4ffb90146369b7fa890489a0d3ecab5aa81c9f47b3ec7a1776fd8bc14eaa33d5", "dweb:/ipfs/QmWo24VHySo9jQBeXfE87Z3Hh576cNKcwccLrBAMsfax1c"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/interfaces/IERC20Hooks.sol": {"keccak256": "0x2fb4fcbf91a7edf36e7ada3f578a8de1aee7ebdd12460648d3e09d4448351875", "urls": ["bzz-raw://d09a05543afcad3c0c2d2b9013647408cc44e1b17f362665ee20f9abed5964e8", "dweb:/ipfs/QmX3MMGsVJSfsrVoYY2jWK2BXd3i6scBhQQxytk461mizh"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/interfaces/IHook.sol": {"keccak256": "0x474893cc48ee17530ad0e978ecbccaa726943615a5736260187a37b8e133ee80", "urls": ["bzz-raw://511ed52ec427cced459878f28453790817d4cbacc8601d77eb8dfa28e2e0b30d", "dweb:/ipfs/QmXvbB1bAZZanGepiUxWh3zZQUaHQZeYpR1UdaW4w5yKVS"], "license": "MIT"}, "lib/1inch/token-plugins/contracts/libs/ReentrancyGuard.sol": {"keccak256": "0xa88ccab1ee6b34a9007986ca15ea5fd5580864029130eb38333183af1bc4f66c", "urls": ["bzz-raw://b5ad18c862e59b0f24edc900519a55c7aee5537a42d039d6ea5e34df40267bb0", "dweb:/ipfs/QmVMRSjUEPxCno1sFdhWvpRqAqoqns8zwVyd7yCHYC6P8Z"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/Ownable.sol": {"keccak256": "0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb", "urls": ["bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6", "dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC1363.sol": {"keccak256": "0xc15298eb2b9ba5e18a8c9d12f93ad17a3e162a5c1d9b85f54c8adb5827b0d4da", "urls": ["bzz-raw://1f3c3d8f81d2daf1231890a6a2f897be365d6a479b53dcd52ec2527b5d3faf41", "dweb:/ipfs/QmeNdkd6u4at9pd2GAyyqxzrVGGvxfLpGmAKnFoYM5ya2e"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC165.sol": {"keccak256": "0x81b022028c39007cce9920c394b9cddd1cb9f3a1c0398f254b4a6492df92ad2b", "urls": ["bzz-raw://e0b61b8a5c69b4df993c3d6f94c174ab293aa8698d149bce7be2d88f82929beb", "dweb:/ipfs/QmbtacmB1k8ginfrHvAJpjVeqnjYGfXYrkXmMPYEb83z4t"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC20.sol": {"keccak256": "0xb5d81383d40f4006d1ce4bbad0064e7a930e17302cbe2a745e09cb403f042733", "urls": ["bzz-raw://3fc4a5681c2f00f41f49260a36ae6bbe1121dd93d470ea24d51d556eff2980be", "dweb:/ipfs/QmUBW6TwVWtGP96ka9TfuGivd27kH8CtkXD8RQAAecSFiR"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC4626.sol": {"keccak256": "0x932fe72aeda629f70ee2ca902cfd8cce9faa0a13b39222c240979f42984c4541", "urls": ["bzz-raw://4e9e0a320185800eb9a6d57ef0e4ccf3bb32a6b5dd44882f7552617c30d4a05e", "dweb:/ipfs/QmYJVpT7DDPWx3DWro8vtM6Gqre2AyufsyCYoHm9cfQ1vr"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/IERC5267.sol": {"keccak256": "0xf29e9088951d8a2074d872a733674618fe5c164df21b8b5cf4a6295f523ba7ad", "urls": ["bzz-raw://562a1abc7ea505582827ce0c9a2f778360a1a8242742683af179930640020215", "dweb:/ipfs/QmPjx5f6KKaPfsDi1uV3ovQN9gHTAcNkMAFJZxE1Adw6VT"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x85cf779582f7860b00bba1d259be00e754bfbac3c2339e324d0113d682d9e9f9", "urls": ["bzz-raw://2ddf369affd106e2a9e2b8a22a6ce3da8a6ccda14c6ba5b8c87f6b08169e6318", "dweb:/ipfs/QmNadAttd47ycHShxhk33JUJhrbzmyZQ7mHs7WEyG4Qkmp"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/ERC20.sol": {"keccak256": "0x5aad1745bddba11752c3962464a3b12e0af079310cc22d1f43f0388ae1aaf8db", "urls": ["bzz-raw://577fad916bfdfe89aadf2685322fec7562cb0ed87722923085213cd9f85d7b79", "dweb:/ipfs/QmSM3J6PjrAUyEoNbdhq1ECZLXczKdCTzZTBUieKHsBYEL"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xfe37358f223eddd65d61bb62b0b7bdb69d7101b5ec8d484292b8c1583a153b8a", "urls": ["bzz-raw://28dd43f30af3c12ae0fc08dd031b1250e906ef3c95f63f30fac6fd15aee2a662", "dweb:/ipfs/QmUkSyWsSRx36w1ti7U6qnGnQgJq16wpMhjeJrnyn9AXwG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC20Permit.sol": {"keccak256": "0x6485b101d7335f0fd25abc996c5e2fc965e72e5fbd0a7ad1a465bd3f012b5fd8", "urls": ["bzz-raw://1f8d296121044e697bcd34c4cd394cebd46fc30b04ce94fccff37200872e6834", "dweb:/ipfs/QmTNdmLdoHgMzoCDZ8Txk9aYvwtuyeJYHf5mjLWgzGTZAu"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/ERC4626.sol": {"keccak256": "0x0d99c706d010fa15de36e7e7b7a03dd0fdc9bcec52f9f812ef80ec7f3fc6fa63", "urls": ["bzz-raw://bee73c81a2964e8da0537de14082e60d64cd7b1cd9162adc04b58317e334c896", "dweb:/ipfs/QmbQ75T9PEJuiLk1kypX68rEBFtTaEzPWsy8Dv99buqVPH"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Metadata.sol": {"keccak256": "0xbaffa0bcc92adf28a53cc3b68551fc3632cb8f849a0028cb8d5c06e4677715e9", "urls": ["bzz-raw://32e6f8f6b2e883c85e6a602c0882d9962ce2f92406961244e86cd974df815912", "dweb:/ipfs/Qmahvx6fPpecicq1aUE1JihCxV5ep1bfuPukzrxa8Ub5PS"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/extensions/IERC20Permit.sol": {"keccak256": "0x093f32ab700c2b05373387263915a75f5455cdb0f09a7630cc621e27b7b50d04", "urls": ["bzz-raw://d163e6ef21df143969df5557305e8c643a135c7660a678d0c65dca91772114a0", "dweb:/ipfs/QmTZUgiwEro5oLRhbJ2iSWyCqu1JTDekoFHALVUn4eHqYK"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/token/ERC20/utils/SafeERC20.sol": {"keccak256": "0x1183b415155c1a7bf56d45edad5b17caf0da70935ac420698cbe8afb6750cbb2", "urls": ["bzz-raw://21d9edaeb3e5e8f93eb0fdab41530654e8169b1990b3bbfcf5e4527c52aa03f5", "dweb:/ipfs/QmWrqpNW3x5k3pTjvrT8XU1hauHnXTjqaPL2tfzMuWYosj"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Nonces.sol": {"keccak256": "0x0082767004fca261c332e9ad100868327a863a88ef724e844857128845ab350f", "urls": ["bzz-raw://132dce9686a54e025eb5ba5d2e48208f847a1ec3e60a3e527766d7bf53fb7f9e", "dweb:/ipfs/QmXn1a2nUZMpu2z6S88UoTfMVtY2YNh86iGrzJDYmMkKeZ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ShortStrings.sol": {"keccak256": "0x1fcf8cceb1a67e6c8512267e780933c4a3f63ef44756e6c818fda79be51c8402", "urls": ["bzz-raw://617d7d57f6f9cd449068b4d23daf485676d083aae648e038d05eb3a13291de35", "dweb:/ipfs/QmPADWPiGaSzZDFNpFEUx4ZPqhzPkYncBpHyTfAGcfsqzy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/StorageSlot.sol": {"keccak256": "0xcf74f855663ce2ae00ed8352666b7935f6cddea2932fdf2c3ecd30a9b1cd0e97", "urls": ["bzz-raw://9f660b1f351b757dfe01438e59888f31f33ded3afcf5cb5b0d9bf9aa6f320a8b", "dweb:/ipfs/QmarDJ5hZEgBtCmmrVzEZWjub9769eD686jmzb2XpSU1cM"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Strings.sol": {"keccak256": "0x1402d9ac66fbca0a2b282cd938f01f3cd5fb1e4c696ed28b37839401674aef52", "urls": ["bzz-raw://d3e6c46b6d1ea36bd73e0ac443a53504089167b98baa24923d702a865a38d211", "dweb:/ipfs/QmdutUpr5KktmvgtqG2v96Bo8nVKLJ3PgPedxbsRD42CuQ"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/ECDSA.sol": {"keccak256": "0x6c29257484c0595ca5af8844fafe99cc5eace7447c9f5bced71d6b3a19a6a2a5", "urls": ["bzz-raw://cce7ac0bdb05f73c0918e362dea2e52426e00ddf0a1018f14accdcf78c6eb6e4", "dweb:/ipfs/QmbkNq5dDxww27FzFFiKgW3S7C5VoZpjdZGpSCtsb9hP32"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/EIP712.sol": {"keccak256": "0xda8013da608bda3c9eaa9e59053d38d7888e64bb40aa557e5929cd702f8de87e", "urls": ["bzz-raw://3ea13234c6b00ae79dc1a98e7e7f2faf38d37e76a687ccd0c95ad84b03ea570f", "dweb:/ipfs/QmWtdefDm5jiEzAjmfPMZ5B1NKVxFoMiD5ZoD68hcNTHun"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/cryptography/MessageHashUtils.sol": {"keccak256": "0x26670fef37d4adf55570ba78815eec5f31cb017e708f61886add4fc4da665631", "urls": ["bzz-raw://b16d45febff462bafd8a5669f904796a835baf607df58a8461916d3bf4f08c59", "dweb:/ipfs/QmU2eJFpjmT4vxeJWJyLeQb8Xht1kdB8Y6MKLDPFA9WPux"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x4ee0e04cc52827588793a141d5efb9830f179a17e80867cc332b3a30ceb30fd9", "urls": ["bzz-raw://17d8f47fce493b34099ed9005c5aee3012488f063cfe1c34ed8f9e6fc3d576e5", "dweb:/ipfs/QmZco2GbZZhEMvG3BovyoGMAFKvfi2LhfNGQLn283LPrXf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/Math.sol": {"keccak256": "0x1225214420c83ebcca88f2ae2b50f053aaa7df7bd684c3e878d334627f2edfc6", "urls": ["bzz-raw://6c5fab4970634f9ab9a620983dc1c8a30153981a0b1a521666e269d0a11399d3", "dweb:/ipfs/QmVRnBC575MESGkEHndjujtR7qub2FzU9RWy9eKLp4hPZB"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3", "urls": ["bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03", "dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ"], "license": "MIT"}}, "version": 1}, "id": 34}